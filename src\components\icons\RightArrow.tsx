import { SVGProps } from "react";
import { JSX } from "react/jsx-runtime";

const RightArrow = (
  props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>
) => (
  <svg
    width="16"
    height="12"
    viewBox="0 0 16 12"
    xmlns="http://www.w3.org/2000/svg"
    fill="currentColor"
    {...props}
  >
    <path d="M9.84625 11.6538L8.79225 10.5693L12.6115 6.75H0.5V5.25H12.6115L8.79225 1.43075L9.84625 0.346252L15.5 6L9.84625 11.6538Z" />
  </svg>
);
export default RightArrow;
