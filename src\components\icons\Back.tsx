import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <mask id="mask0_168_453" style={{maskType: "alpha",}} maskUnits="userSpaceOnUse" x="0" y="0" width="30" height="30">
            <rect width="30" height="30" fill="#D9D9D9"/>
        </mask>
        <g mask="url(#mask0_168_453)">
            <path d="M7.59375 15.4375L12.1641 20.0078C12.5308 20.3746 12.5308 20.9692 12.1641 21.3359C11.7973 21.7027 11.2027 21.7027 10.8359 21.3359L4 14.5L10.8359 7.66406C11.2027 7.29731 11.7973 7.29731 12.1641 7.66406C12.5308 8.03081 12.5308 8.62544 12.1641 8.99219L7.59375 13.5625H24.5625C25.0803 13.5625 25.5 13.9822 25.5 14.5C25.5 15.0178 25.0803 15.4375 24.5625 15.4375H7.59375Z" fill="#373737"/>
        </g>
    </svg>
)
export default SvgComponent

