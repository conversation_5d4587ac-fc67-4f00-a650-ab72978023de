export const extractFilenameFromUrl = (url: string) => {
    try {
      const cleanUrl = url.split('?')[0].split('#')[0];

      const urlPath = new URL(cleanUrl).pathname;
      const fileName = urlPath.split('/').pop() || 'model.glb';

      if (!fileName.includes('.')) {
        return fileName + '.glb';
      }

      return fileName;
    } catch (error) {
      console.error('Error extracting filename from URL:', url, error);
      // Fallback: use last part of URL path or generate a name
      const fallbackName = url.split('/').pop()?.split('?')[0] || 'model.glb';
      return fallbackName.includes('.') ? fallbackName : fallbackName + '.glb';
    }
}