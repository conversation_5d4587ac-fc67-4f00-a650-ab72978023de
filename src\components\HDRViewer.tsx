import { useEffect, FC, useState, useCallback } from "react";
import { CustomFileData } from "../api/api";
import { useUser } from "../provider/UserProvider";
import { useBrowser } from "../provider/BrowserProvider";

interface ViewerProps {
  file: CustomFileData;
  setLoading: any;
  setError: any;
}

const HDRViewer: FC<ViewerProps> = (props: ViewerProps) => {
  const [data, setData] = useState<string>();
  const { api } = useUser();
  const {getThumbnailGenerator } = useBrowser();

  const loadFile = useCallback(async () => {
    if(!props.file  || !api) return;
  
    const thumbGen : any = await getThumbnailGenerator();
    
    try{
      const data = await fetch(api.getDownloadUrl(props.file));
      if(!data.ok) {
        throw new Error("Failed to fetch file");
      }

      const blob = await data.blob();
      const file = new File([blob], props.file.name, { type: blob.type });
      const preview = await thumbGen.snapFile(file , {
        width : 1920,
        height : 1080,
        quality : 1,
        mimeType : "image/jpeg",
        snapOriginal : true,
      }) as any

      if(!preview) {
        throw new Error("Failed to generate preview");
      }

      setData(URL.createObjectURL(preview));
      props.setLoading(false);
    } catch(e){
      console.log(e);
      props.setError(true);
      props.setLoading(false);
    }
    


  }, [props, api, getThumbnailGenerator]);

  useEffect(() => {
    loadFile();
  }, [loadFile, props.file]);

  return (
    <div className="w-full h-full overflow-auto p-unit-xl isolate ">
      {data && (
        <img src={data} className="w-full h-full object-contain" />
      )}
    </div>
  );
};

export default HDRViewer;
