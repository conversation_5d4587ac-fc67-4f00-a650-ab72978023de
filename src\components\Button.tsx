import { Button } from "@nextui-org/react";
import { FC } from "react";

interface buttonProps {
  name?: string | JSX.Element;
  className?: string;
  color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger";
  size?: "sm" | "md" | "lg";
  varient?: "solid" | "bordered" | "light" | "flat" | "faded" | "shadow" | "ghost";
  heiglighted?: boolean;
  onClick?: any;
  fullWidth?: boolean;
  isLoading?: boolean;
  disabled?: boolean;
  ref?: any;
  disableRipple?: boolean
}

const MyButton: FC<buttonProps> = (props) => {
  return (
    <Button
      ref={props.ref}
      isDisabled={props.disabled}
      isLoading={props.isLoading}
      onClick={props.onClick}
      color={props.color}
      className={
        props.className +
        (props.heiglighted ? " bg-white bg-gradient-to-r from-[#6E72F2] to-[#00CFDC] text-white" + (props.disabled ? " opacity-40" : "") : "")
      }
      disableRipple={props.disableRipple || false}
      fullWidth={props.fullWidth !== undefined ? props.fullWidth : true}
      size={props.size ? props.size : "sm"}
      style={{ fontWeight: "inherit", fontSize: "inherit" }}
      variant={props.varient}
    >
      {props.name}
    </Button>
  );
};

export { MyButton as Button };
