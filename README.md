# iJewel Drive Frontend

This repository uses React with Vite.
For file manager, a custom fork of Chonky is used.

Automatic deployments are setup.

`master` will deploy to https://drive.ijewel3d.com/
`dev` will deploy to https://dev.drive.ijewel3d.com/

For local development update package.json to chonky local reference: 
`"chonky": "file:./../Chonky/packages/chonky"`

To update the chonky library in ijewel Drive, run
`pack` script in your local reference.