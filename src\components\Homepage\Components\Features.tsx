import RightArrow from "../../icons/RightArrow";
import { Button } from "../../Button";
import { Link } from "react-router-dom";

export const FeaturePrimaryCard = (props: {
  title: string;
  description: string;
  imageSrc: string;
  actions?: JSX.Element;
  className?: string;
}) => {
  const { title, description, imageSrc, className } = props;
  return (
    <div
      className={
        "overflow-hidden flex flex-col md:flex-row justify-center items-center rounded-[30px] border-1 border-[#373737]" +
        ` ${className}`
      }
    >
      {/* Image */}
      <img
        src={imageSrc} // Replace with actual image source
        alt="3D Viewer"
        className="w-[350px] md:w-1/2 object-cover pointer-events-none"
      />
      {/* Content */}
      <div className="w-full md:w-1/2 p-5 md:pr-[60px]">
        <h2 className="text-3xl md:text-4xl font-[700] font-ltnormal text-gray-900">
          {title}
        </h2>
        <p className="font-ltlight mt-2.5 text-[#373737] text-xl md:text-2xl font-[300]">
          {description}
        </p>
        <Link to={"https://drive.ijewel3d.com/drive/files/Ep9bWZIlTSG6_8DH7QDc1w/embedded?isAutoplay=true"} className="mt-5 flex justify-center md:justify-start" target="_blank">
          <Button
            varient="ghost"
            fullWidth={false}
            name={
              <span className="flex gap-2 text-xs md:text-base text-[#373737] items-center">
                See Example
                <RightArrow />
              </span>
            }
            className="h-[40px] w-[178px] rounded-full border-1 border-[#373737]"
          />
        </Link>
      </div>
    </div>
  );
};

export const FeatureCard = (props: {
  title: string;
  description: string;
  imageSrc: string;
  flip?: boolean;
  className?: string;
  classNames?: { contentWrapper?: string };
}) => {
  const { title, description, imageSrc, flip, className, classNames } = props;

  return (
    <div
      className={
        "flex flex-col md:flex-row items-center px-5 " +
        (flip ? " md:flex-row-reverse" : "") +
        ` ${className}`
      }
    >
      {/* Image */}
      <img
        src={imageSrc}
        alt="3D Viewer"
        className="min-w-[300px] md:w-1/2 object-cover pointer-events-none"
      />

      {/* Content */}
      <div
        className={
          "w-full md:w-1/2 " +
          (flip ? "md:pr-[60px]" : "md:pl-[60px]") +
          ` ${classNames?.contentWrapper ?? ""}`
        }
      >
        <h2 className="text-3xl md:text-4xl font-[700]">{title}</h2>
        <p className="font-ltlight mt-2.5 text-[#373737] text-xl md:text-2xl">
          {description}
        </p>
      </div>
    </div>
  );
};
const featureList = [
  {
    title: "Support multiple file formats",
    deescription: `Upload and work with a wide range of 3D file formats, including 3dm, fbx, obj, gltf, glb and stl. Just drag and drop your designs and get started right away.`,
    imageSrc: "/feature-files.png",
  },
  {
    title: "Secure File Sharing with Password Protection",
    deescription: `Share your designs securely by protecting files with encryption. Only those with the password can access your files, ensuring that your work stays private and safe during every exchange.`,
    imageSrc: "/feature-protect.png",
  },
  {
    title: "Private Hosting",
    deescription: `Store your 3D jewelry designs securely on private servers. This ensures that your files are fully controlled by you, with no third-party access, keeping your work safe and confidential.`,
    imageSrc: "/feature-hosting.png",
  },
  {
    title: "Secure Backup",
    deescription: `Your data is backed up regularly in secure environments, ensuring that your designs are safe from any loss while maintaining the privacy and security of your assets.`,
    imageSrc: "/feature-backup.png",
    className:
      "bg-[linear-gradient(97.4deg,_#F0F1FF_2.04%,_#E0F6FF_100.68%)] rounded-[30px]",
    classNames: { contentWrapper: "py-5 md:py-0 md:pl-0 md:pr-[60px]" },
  },
  {
    title: "Full data ownership",
    deescription: `Retain complete ownership of all your files and data. Your designs and assets are never shared or used without your permission.`,
    imageSrc: "/feature-ownership.png",
  },
  {
    title: "Custom Library",
    deescription: `Build a personalized library of your own custom metal and gem materials to quickly apply to your designs, making customization faster and consistent.`,
    imageSrc: "/feature-library.png",
  },
  {
    title: "Advanced customization options",
    deescription: `Full-Editor and Mini-Editors are tools for adjusting materials, lighting, and setting scene.`,
    imageSrc: "/feature-advanced.png",
  },
];
const Features = () => {
  return (
    <div className="max-w-[1000px] mx-auto px-4 lg:px-0">
      <img
        src="/cover.png"
        alt="cover"
        className="min-w-[350px] md:w-full object-cover pointer-events-none"
      />
      <h1 className="text-5xl font-san font-[700] text-[#373737] text-center leading-[67px] py-10 md:py-[60px]">
        Features
      </h1>
      <FeaturePrimaryCard
        title="Real Time 3D Viewer"
        description="Interact with 3D models in real time, exploring jewelry designs from every angle. Zoom in, rotate, and examine details instantly from any browser or device."
        imageSrc="/feature-ring.png"
      />
      <div className="my-10 md:my-[70px] flex flex-col gap-[70px] md:px-5">
        {featureList.map((feature, idx) => (
          <FeatureCard
            key={feature.title}
            title={feature.title}
            description={feature.deescription}
            imageSrc={feature.imageSrc}
            flip={idx % 2 === 0}
            className={feature.className ?? ""}
            classNames={feature.classNames}
          />
        ))}
      </div>
    </div>
  );
};

export default Features;
