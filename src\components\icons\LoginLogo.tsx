import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg width="170" height="32" viewBox="0 0 170 32" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path d="M8.23922 10.6585C8.23922 10.4138 8.15848 10.1985 7.99701 10.0125C7.83553 9.82659 7.62864 9.70914 7.37634 9.65041L11.6554 7.9425V27.0133C11.6554 28.2955 11.3173 29.4406 10.6361 30.4438C9.95993 31.447 8.93558 31.9657 7.56304 32C6.01894 31.9119 4.91889 31.1534 4.24776 29.7244C3.98032 29.1323 3.84912 28.5255 3.84912 27.8991H4.19226C4.19226 28.4912 4.31336 29.0491 4.55557 29.5727C4.81797 30.0278 5.23175 30.3264 5.79187 30.4585C6.35198 30.5906 6.85154 30.5417 7.30065 30.3068C7.6135 30.1159 7.83048 29.7978 7.96673 29.3525C8.09793 28.9072 8.17867 28.4619 8.19885 28.0116C8.22408 27.5663 8.23417 27.2482 8.23417 27.0574L8.23922 10.6585Z" fill="#373737"/>
        <path d="M26.501 15.6795C26.501 15.704 26.4758 15.7187 26.4304 15.7284H26.501V16.0612H16.4038C16.3685 17.3678 16.6712 18.6647 17.3121 19.9517C17.9479 21.2388 19.0076 22.0511 20.481 22.3888C22.2976 22.6139 23.9325 22.1784 25.3808 21.0822C25.6331 20.8571 25.8652 20.6124 26.0721 20.3481C26.0973 20.3139 26.1276 20.2943 26.173 20.2796C26.2184 20.2698 26.2638 20.2796 26.3093 20.3139C26.3446 20.3383 26.3648 20.3677 26.3799 20.4117C26.39 20.4558 26.3799 20.4998 26.3446 20.5439C26.1276 20.813 25.8904 21.0675 25.6381 21.3122C24.2706 22.7852 22.5499 23.666 20.476 23.9548C18.1497 24.0331 16.323 23.2452 14.9959 21.5862C13.6688 19.9273 12.9573 18.1068 12.8665 16.1004C12.7958 14.4952 13.139 12.9929 13.8858 11.5884C14.6326 10.1888 15.8033 9.17086 17.3878 8.53468C18.6897 8.06488 20.0168 7.92786 21.3742 8.11382C23.2816 8.50532 24.6693 9.53789 25.5322 11.2115C26.2134 12.6307 26.5313 14.1184 26.501 15.6795ZM16.4038 15.7333H22.9284C22.9385 15.7089 22.9435 15.6942 22.9435 15.6844C23.4834 14.2995 23.7711 12.9488 23.8064 11.6324C23.7509 10.0273 22.9132 8.97512 21.3035 8.48574C19.4062 8.1285 17.9933 8.76958 17.0749 10.409C16.535 11.8477 16.3079 13.3452 16.4038 14.8965V15.7333Z" fill="#373737"/>
        <path d="M52.2311 8.7841C52.3017 8.59325 52.2816 8.41708 52.1806 8.25558C52.0797 8.09409 51.9132 8.006 51.6962 7.99622H53.7853C53.2201 8.04026 52.8265 8.31431 52.5944 8.81836L46.6552 23.7833L46.741 24.0036H43.0674L39.5806 15.1851L36.1644 23.7833L36.2501 24.0036H32.5766L26.5869 8.87219C26.3548 8.35835 25.9561 8.06962 25.3809 8.00111H30.765C30.5581 8.02558 30.4017 8.11367 30.2907 8.27516C30.1797 8.43665 30.1595 8.61772 30.2301 8.81836L35.9928 23.3135L39.3888 14.73L37.0777 8.87219C36.8456 8.35835 36.4469 8.06962 35.8717 8.00111H41.2559H41.2407C41.0792 8.02558 40.948 8.09409 40.8421 8.21154C40.7412 8.32899 40.6856 8.46112 40.6856 8.60304C40.6856 8.67155 40.6957 8.74495 40.721 8.81836L41.2407 10.0907L41.4123 10.5605L46.4836 23.3135L52.2311 8.7841Z" fill="#373737"/>
        <path d="M66.2438 15.6793C66.2438 15.7038 66.2186 15.7185 66.1731 15.7283H66.2438V16.0611H56.1466C56.1112 17.3677 56.414 18.6645 57.0498 19.9516C57.6907 21.2386 58.7453 22.051 60.2188 22.3886C62.0353 22.6137 63.6703 22.1782 65.1185 21.082C65.3708 20.8569 65.6029 20.6122 65.8098 20.3479C65.83 20.3137 65.8653 20.2941 65.9158 20.2794C65.9612 20.2696 66.0066 20.2794 66.052 20.3137C66.0874 20.3382 66.1075 20.3675 66.1227 20.4116C66.1328 20.4556 66.1227 20.4997 66.0874 20.5437C65.8704 20.8128 65.6332 21.0673 65.3809 21.312C64.0134 22.785 62.2927 23.6659 60.2188 23.9546C57.8925 24.0329 56.0709 23.245 54.7387 21.5861C53.4116 19.9271 52.7001 18.0968 52.6093 16.0904C52.5386 14.4853 52.8767 12.9829 53.6286 11.5784C54.3754 10.1788 55.5461 9.16092 57.1305 8.52473C58.4324 8.05494 59.7596 7.91791 61.117 8.10387C63.0244 8.49537 64.4121 9.52794 65.2749 11.2016C65.9562 12.6306 66.2791 14.1182 66.2438 15.6793ZM56.1466 15.7332H62.6712C62.6812 15.7087 62.6863 15.694 62.6863 15.6842C63.2262 14.2993 63.5139 12.9487 63.5492 11.6322C63.4886 10.0271 62.656 8.97495 61.0463 8.48558C59.149 8.12834 57.7361 8.76942 56.8177 10.4088C56.2778 11.8476 56.0507 13.345 56.1466 14.8963V15.7332Z" fill="#373737"/>
        <path d="M71.1287 23.113C71.1287 23.3479 71.2094 23.5486 71.3709 23.7149C71.5324 23.8813 71.7342 23.9792 71.9764 23.9988H66.8496C67.0918 23.9743 67.2937 23.8862 67.4551 23.7247C67.6166 23.5632 67.6973 23.3626 67.6973 23.1326V2.57899C67.6973 2.34409 67.6166 2.14345 67.4551 1.96727C67.2937 1.79599 67.0918 1.69323 66.8496 1.65897L71.1287 0V23.113Z" fill="#373737"/>
        <path d="M2.6176 10.9129V23.0542L6.05399 21.9678V8.28497L1.7749 9.95862C2.02721 9.99288 2.2341 10.1005 2.38548 10.2767C2.53686 10.4529 2.6176 10.6682 2.6176 10.9129Z" fill="#373737"/>
        <path d="M6.86681 1.60995L5.60529 0.680147C5.60025 0.675254 5.5952 0.675254 5.59015 0.67036C5.59015 0.67036 5.59015 0.67036 5.58511 0.67036C5.58006 0.665466 5.56997 0.665466 5.56492 0.665466H2.25974C2.25469 0.665466 2.2446 0.665466 2.23955 0.67036C2.2345 0.675254 2.22946 0.675254 2.22441 0.680147L0.962891 1.60995H6.86681Z" fill="#373737"/>
        <path d="M1.00293 1.80085L2.9053 3.43535L3.86911 4.26239C3.86911 4.26239 3.86911 4.26239 3.87415 4.26239C3.8792 4.26729 3.88425 4.26729 3.88929 4.27218C3.89434 4.27707 3.90443 4.27707 3.90948 4.27707C3.91452 4.27707 3.92461 4.27707 3.92966 4.27218C3.93471 4.27218 3.93975 4.26729 3.9448 4.26239C3.9448 4.26239 3.9448 4.26239 3.94985 4.26239L6.81602 1.79596L1.00293 1.80085Z" fill="#373737"/>
        <path d="M169.037 0V6.98395C168.957 5.08583 168.249 3.50593 166.913 2.24424C165.577 0.971389 163.953 0.334962 162.042 0.334962H157.706V11.8241H163.838C164.322 11.8241 164.731 11.6678 165.064 11.3552C165.41 11.0426 165.6 10.6462 165.635 10.1661V13.8172C165.6 13.3482 165.41 12.9574 165.064 12.6448C164.731 12.321 164.322 12.1591 163.838 12.1591H157.706V23.665H161.955C163.867 23.665 165.496 23.0342 166.844 21.7725C168.203 20.4996 168.934 18.9142 169.037 17.016V24H153.422C153.998 23.7767 154.286 23.4082 154.286 22.8946V1.10537C154.286 0.591765 153.998 0.223308 153.422 0H169.037Z" fill="#6E72F2"/>
        <path d="M150.148 0.78716C150.217 0.586183 150.194 0.407537 150.079 0.251221C149.975 0.0949058 149.82 0.0111654 149.612 0H151.703C151.138 0.0334962 150.741 0.295883 150.511 0.78716L140.907 23.6818L141.045 24H137.348L127.64 0.8709C127.41 0.357292 127.007 0.0669923 126.431 0H131.838C131.631 0.0223308 131.475 0.111654 131.371 0.267969C131.268 0.424285 131.251 0.602931 131.32 0.803908L140.734 23.2463L150.148 0.78716Z" fill="#6E72F2"/>
        <path d="M124.712 0C124.147 0.346127 123.865 0.809491 123.865 1.39009V22.9281C123.865 23.1738 123.94 23.3971 124.09 23.598C124.251 23.799 124.458 23.933 124.712 24H119.581C120.146 23.7767 120.428 23.4082 120.428 22.8946V1.10537C120.428 0.591765 120.146 0.223308 119.581 0H124.712Z" fill="#6E72F2"/>
        <path d="M116.394 23.2128C116.785 23.6706 117.275 23.933 117.862 24H111.954C112.116 23.9777 112.225 23.8939 112.283 23.7488C112.34 23.6036 112.323 23.4641 112.231 23.3301L102.868 11.9916C102.834 11.9916 102.805 11.9916 102.782 11.9916C102.747 11.9916 102.719 11.9916 102.696 11.9916V24H98.4118C98.9876 23.7655 99.2755 23.3915 99.2755 22.8779V1.08863C99.2755 0.586183 98.9876 0.223308 98.4118 0H102.696C102.719 0 103.001 0 103.542 0C104.083 0 104.694 0 105.373 0C106.052 0 106.657 0 107.187 0C107.728 0 107.999 0 107.999 0C108.759 0 109.524 0.0949058 110.296 0.284717C111.724 0.653175 112.91 1.33426 113.854 2.32798C114.799 3.3217 115.288 4.54431 115.323 5.99581C115.277 7.45848 114.781 8.69225 113.837 9.69714C112.904 10.6909 111.724 11.3608 110.296 11.7069C109.513 11.8967 108.741 11.9916 107.981 11.9916C107.935 11.9916 107.659 11.9916 107.152 11.9916L116.394 23.2128ZM102.696 11.6567C102.811 11.6567 102.961 11.6567 103.145 11.6567C103.513 11.6567 103.968 11.6567 104.509 11.6567C105.051 11.6567 105.58 11.6567 106.099 11.6567C106.628 11.6567 107.066 11.6567 107.411 11.6567C107.768 11.6567 107.964 11.6567 107.999 11.6567C108.736 11.6343 109.404 11.4222 110.002 11.0202C110.751 10.4061 111.258 9.65248 111.522 8.75924C111.787 7.85485 111.908 6.9337 111.885 5.99581C111.908 5.04675 111.793 4.13119 111.54 3.24913C111.286 2.36706 110.774 1.60782 110.002 0.971389C109.404 0.569435 108.73 0.362875 107.981 0.35171C107.981 0.35171 107.717 0.35171 107.187 0.35171C106.657 0.340544 106.047 0.334962 105.356 0.334962C104.676 0.334962 104.066 0.334962 103.525 0.334962C102.995 0.334962 102.719 0.334962 102.696 0.334962V11.6567Z" fill="#6E72F2"/>
        <path d="M89.0873 0.234473C92.7954 1.10537 95.1676 3.46685 96.204 7.31891C96.561 8.85973 96.7222 10.4117 96.6877 11.9749C96.7107 13.7725 96.4747 15.5255 95.9795 17.2338C95.4843 18.9309 94.5861 20.4103 93.2848 21.672C91.7532 23.0007 89.9913 23.7488 87.9991 23.9163C87.2966 23.9721 86.5941 24 85.8917 24C85.8341 24 85.6326 24 85.2871 24C84.9416 24 84.5559 24 84.1298 24C83.7037 24 83.3179 24 82.9724 24C82.6385 24 82.4485 24 82.4024 24H78.8613C79.4371 23.7767 79.725 23.4082 79.725 22.8946V1.10537C79.725 0.591765 79.4371 0.223308 78.8613 0H83.1452L85.8917 0.0167481C86.5826 -0.00558269 87.2909 0.0167481 88.0163 0.0837404C88.3733 0.106071 88.7303 0.156315 89.0873 0.234473ZM87.8263 23.1961C89.8416 22.1242 91.2926 20.5611 92.1793 18.5066C93.066 16.441 93.5036 14.2749 93.4921 12.0084C93.5266 9.69714 93.0948 7.53105 92.1966 5.51012C91.3098 3.47802 89.8531 1.91486 87.8263 0.820656C87.216 0.51919 86.5711 0.357292 85.8917 0.334962L83.1452 0.301465V23.6985C83.4791 23.6985 83.8476 23.6985 84.2507 23.6985C84.6537 23.6985 85.0107 23.6985 85.3217 23.6985C85.6441 23.6985 85.8341 23.6985 85.8917 23.6985C86.5596 23.6985 87.2045 23.5311 87.8263 23.1961Z" fill="#6E72F2"/>
    </svg>
)
export default SvgComponent

