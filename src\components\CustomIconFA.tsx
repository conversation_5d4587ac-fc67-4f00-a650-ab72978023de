import { ChonkyIconProps } from 'chonky';
import { IconFA as ChonkyIconFA } from 'chonky';
import React from 'react';

function isUrl(s: string): boolean {
    try {
        new URL(s);
        return true;
    } catch (_) {
        return false;
    }
}

export const CustomIconFA: React.FC<ChonkyIconProps> = React.memo((props) => {
    const { icon, className, style } = props;

    if (typeof icon === 'string' && isUrl(icon)) {
        // Render the icon as an image if it's a URL
        return (
            <img
                src={icon}
                alt=""
                style={{ width: '1em', height: '1em', ...style }}
                className={className}
            />
        );
    } else {
        // Fallback to the default Chonky icon
        return <ChonkyIconFA {...props} />;
    }
});
