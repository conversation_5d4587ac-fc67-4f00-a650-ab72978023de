import "./index.css";
import { lazy } from "react";
import ReactDOM from "react-dom/client";
import { setChonkyDefaults } from "chonky";
import { UserProvider } from "./provider/UserProvider.ts";
import { Toaster } from "react-hot-toast";
import { BrowserProvider } from "./provider/BrowserProvider.ts";
import { createBrowserRouter, createRoutesFromElements, Route, RouterProvider } from "react-router-dom";
import SuspendedElement from "./components/SuspendedElement.tsx";
import Login from "./components/auth/Login.tsx";
import { CustomIconFA } from "./components/CustomIconFA.tsx";
import { FileAPI } from "./api/api.ts";
import { AppConfig } from "./components/shared/types.ts";
import ForgotPassword from './components/auth/ForgotPassword.tsx'
import ResetPassword from './components/ResetPassword.tsx'
import TermsOfService from "./pages/Terms.tsx";
import Homepage from "./pages/Homepage.tsx";
import Pricing from "./pages/Pricing.tsx";
import VerifyEmail from "./pages/VerifyEmail.tsx";
import { AssetsProvider } from "./provider/AssetsProvider.ts";
import EmbeddedViewer from "./pages/embedded/EmbeddedViewer.tsx";
import EmbeddedPreview from "./pages/embedded/EmbeddedPreview.tsx";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { NextUIProvider } from "@nextui-org/react";

// Lazy-loaded components
const Folders = lazy(() => import("./pages/Folders.tsx"));
const View = lazy(() => import("./pages/View.tsx"));
const Edit = lazy(() => import("./pages/Edit.tsx"));

// Set default icons for Chonky
// @ts-ignore
setChonkyDefaults({ iconComponent: CustomIconFA });
const api =  FileAPI.getInstance()
const baseName = api.getBaseName()

const router = createBrowserRouter(
  createRoutesFromElements(
    <Route path="/:basename?" element={<Login />} loader={loader}>
      {!baseName && <Route path="" element={<Homepage/>}/>}
      <Route path="login" />
      <Route path="signup" />
      <Route
        path="terms"
        element={
          <SuspendedElement>
            <TermsOfService />
          </SuspendedElement>
        }
      />
      <Route
        path="pricing"
        element={
          <SuspendedElement>
            <Pricing />
          </SuspendedElement>
        }
      />
      <Route
        path="forgot-password"
        element={
          <SuspendedElement>
            <ForgotPassword />
          </SuspendedElement>
        }
      />
      <Route
        path="reset-password/:token"
        element={
          <SuspendedElement>
            <ResetPassword />
          </SuspendedElement>
        }
      />
      <Route
        path="verify-email/:token?"
        element={
          <SuspendedElement>
            <VerifyEmail />
          </SuspendedElement>
        }
      />
      <Route
        path="folders/:folderId?"
        element={
          <SuspendedElement>
              <Folders />
          </SuspendedElement>
        }
      />
      <Route path="files/:fileId">
        <Route
          path="view"
          element={
            <SuspendedElement>
              <View />
            </SuspendedElement>
          }
        />
        <Route
          path="edit"
          element={
            <SuspendedElement>
              <Edit />
            </SuspendedElement>
          }
        />
        <Route
          path="playground"
          element={
            <SuspendedElement>
              <Edit isPlayground />
            </SuspendedElement>
          }
        />
        <Route
          path="embedded"
          loader={FileLoader}
          element={
            <SuspendedElement>
              <EmbeddedViewer />
            </SuspendedElement>
          }
        />
        <Route
          path="embedded/preview"
          element={
            <SuspendedElement>
              <EmbeddedPreview />
            </SuspendedElement>
          }
        />
      </Route>
    </Route>
  )
);

ReactDOM.createRoot(document.getElementById("root")!).render(
  <DndProvider backend={HTML5Backend}>
    <UserProvider>
      <BrowserProvider>
        <AssetsProvider>
          <NextUIProvider>
              <Toaster key={"drive-toaster"} />
              <RouterProvider router={router}>
              </RouterProvider>
          </NextUIProvider>
        </AssetsProvider>
      </BrowserProvider>
    </UserProvider>
  </DndProvider>
);


export async function loader() {
  // const id = params.eventID;
  const api = FileAPI.getInstance();
  const response = await api.getDriveConfig();

  if(response.error || !response.data){
    console.error("Error loading drive config" , response.error);
    return { config: null };
  }

  //convert array to object
  const config: AppConfig = response.data.reduce((acc: AppConfig, item) => {
    //check if the value is a json string and parse it
    try {
      const val = JSON.parse(item.val);
      acc[item.id] = val;
    } catch (e) {
      acc[item.id] = item.val as any
    }

    return acc;
  }, {});

  if(config["files-api"]){
    api.fileApi = config["files-api"]
  }

  return { config };
}

export async function FileLoader({request} : any)  {
  const url = new URL(request.url);

  const api = FileAPI.getInstance();
  const queryParams = url.searchParams;

  //get id from slug or id from query params
  const id = queryParams.get("slug") || queryParams.get("id");
  if (!id) {
    return null
  }

  const {data , error} = await api.viewFile(id);
  if (!data || error) {
    return null
  }

  return {file: data};

}
