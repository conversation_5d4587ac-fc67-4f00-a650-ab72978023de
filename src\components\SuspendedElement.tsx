import { Suspense } from "react";
import { ReactNode } from "react";
import { Logo } from "./Logo";
export default function SuspendedElement({ children }: { children: ReactNode }) {
    return (
      <Suspense fallback={
        <div className='w-screen h-screen flex flex-col justify-center items-center gap-unit-xl'>
          <Logo className="h-unit-3xl w-auto "/>
          Loading...
        </div>}>
        {children}
      </Suspense>
    );
  }
  