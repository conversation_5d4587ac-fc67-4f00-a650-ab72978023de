import * as React from "react"
import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 218.22 208" width={13} height={13} {...props}>
    <g data-name="Layer 2">
      <path
        fill="currentColor"
        d="M62.88 136.68c-38.32-30.87-38-83.32-8.15-113.79a76.14 76.14 0 0 1 107.14-1.69c31.53 30.27 32.48 84.61-6.48 115.6 13.87 5.88 26.93 13.11 37.32 23.85 8.28 8.56 15.62 18.11 22.68 27.73 4.89 6.66 3.14 14-3 17.78s-12.88 1.63-17.33-5.4c-20-31.56-48.62-48.56-86-48.58-36.32 0-64.51 16.33-84.62 46.57-.64 1-1.17 2-1.81 3-4.08 6.22-11 8-16.85 4.33S-2 195.35 2.19 189.53c6.73-9.45 13.71-18.87 21.64-27.3 9.65-10.26 21.66-17.61 34.55-23.38 1.48-.66 2.92-1.4 4.5-2.17ZM109.2 128a52 52 0 1 0-52.06-51.72A52 52 0 0 0 109.2 128Z"
        data-name="Layer 1"
      />
    </g>
  </svg>
)
export default SvgComponent
