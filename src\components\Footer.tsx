import React from "react";
import { Logo } from "./Logo";
import { Link as RouterLink } from "react-router-dom";
import { Link } from "@nextui-org/react";

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const footerSections = [
    {
      title: "Products",
      items: [
        {
          name: "iJewel Drive",
          link: "https://www.ijewel3d.com/products/ijewel-drive",
        },
        {
          name: "iJewel Design",
          link: "https://www.ijewel3d.com/products/ijewel-design",
        },
        {
          name: "iJewel Playground",
          link: "https://www.ijewel3d.com/products/ijewel-playground",
        },
        {
          name: "iJewel Studio",
          link: "https://www.ijewel3d.com/products/ijewel-studio",
        },
        {
          name: "iJewel TryOn",
          link: "https://www.ijewel3d.com/products/ijewel-tryon",
        },
      ],
    },
    {
      title: "Resources",
      items: [
        { name: "Documentation", link: "https://developer.ijewel3d.com/" },
        { name: "Discord", link: "https://discord.com/invite/apzU8rUWxY" },
        { name: "Blogs", link: "https://www.ijewel3d.com/blog" },
        {
          name: "Tutorials",
          link: "https://www.youtube.com/watch?v=KLC-KDWdHiM",
        },
      ],
    },
    {
      title: "Social",
      items: [
        { name: "Youtube", link: "https://www.youtube.com/@iJewel3d" },
        {
          name: "LinkedIn",
          link: "https://www.linkedin.com/company/ijewel3d/",
        },
        { name: "Instagram", link: "https://www.instagram.com/ijewel3d/" },
        { name: "Twitter", link: "https://x.com/pixotronics" },
      ],
    },
    {
      title: "Contact",
      items: [
        { name: "contact us", link: "mailto:<EMAIL>" },
        { name: "Request a demo", link: "https://ijewel3d.zohobookings.in/#/209816000000031082",target:'_blank' },
      ],
    },
  ];

  return (
    <footer className="bg-white py-8 px-4">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-wrap justify-between">
          <div className="w-full md:w-auto mb-6 md:mb-0">
            <RouterLink to={"/"}>
              <Logo className="h-unit-2xl w-auto" />
            </RouterLink>
          </div>
          {footerSections.map((section, index) => (
            <div key={index} className="w-1/2 sm:w-1/3 md:w-auto mb-6">
              <h3 className="text-gray-900 font-semibold mb-4 text-lg">
                {section.title}
              </h3>
              <ul className="space-y-2">
                {section.items.map((item, itemIndex) => (
                  <li key={itemIndex}>
                    <Link
                      href={item.link}
                      color="foreground"
                      className="text-sm hover:underline font-normal"
                      target={item.target ?? "_self"}
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
        <hr className="my-8 border-gray-200" />
        <div className="flex flex-wrap items-center justify-between text-sm text-gray-500">
          <p>&copy; {currentYear} iJewel3d. All rights reserved</p>
          <div className="space-x-4 mt-4 sm:mt-0">
            <Link
              href="/terms"
              color="foreground"
              className="hover:underline text-sm font-normal"
            >
              Terms of Service
            </Link>
            <Link
              href="/privacy"
              color="foreground"
              className="hover:underline text-sm font-normal"
            >
              Privacy Policy
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
