import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <mask id="mask0_135_613" style={{maskType: "alpha",}} maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
            <rect width="20" height="20" fill="#D9D9D9"/>
        </mask>
        <g mask="url(#mask0_135_613)">
            <path d="M10.7884 9.99997L6.95508 6.16664L7.8332 5.28851L12.5447 9.99997L7.8332 14.7114L6.95508 13.8333L10.7884 9.99997Z" fill="#1C1B1F"/>
        </g>
    </svg>
)
export default SvgComponent

