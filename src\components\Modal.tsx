import { FC } from "react";
import {
  ModalContent,
  Modal,
  ModalProps,
  ModalBody,
  Modal<PERSON>ooter,
  ModalHeader,
} from "@nextui-org/react";
import React from "react";

interface CustomModalProps extends Omit<ModalProps, "children"> {
  children: (onClose: any) => JSX.Element;
  sideButtons?: ((onClose: any) => JSX.Element) | any;
  headerComponent?: (onClose: any) => JSX.Element;
  footerComponent?: (onClose: any) => JSX.Element;
}
export const defaultModelConfig: Partial<Omit<ModalProps, "children">> = {
  size: "4xl",
  placement: "center",
  backdrop: "blur",
  shadow: "sm",
  scrollBehavior: "inside",
  classNames: {
    base: `p-0 bg-white overflow-hidden`,
    body: "p-0",
    closeButton: "z-[1010] right-unit-lg top-unit-lg",
  },
};
const MyModal: FC<CustomModalProps> = (props) => {
  const { headerComponent, footerComponent, children, sideButtons, ...rest } =
    props;

  const mergedProps = { ...defaultModelConfig, ...rest };
  return (
    <Modal {...mergedProps}>
      <ModalContent>
        {/* base */}
        {(onClose) => (
          <>
            {headerComponent && (
              <ModalHeader>
                {/* header & closeButton*/}
                {headerComponent(onClose)}
              </ModalHeader>
            )}
            <ModalBody>
              {/* body */}
              {children(onClose) as any}
            </ModalBody>
            {sideButtons?.(onClose)}
            {footerComponent && (
              <ModalFooter>
                {/* footer */}
                {footerComponent(onClose)}
              </ModalFooter>
            )}
          </>
        )}
      </ModalContent>
    </Modal>
  );
};

export { MyModal as Modal };
