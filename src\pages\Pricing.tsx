import React, { useCallback, useMemo } from "react";
import AuthHeader from "../components/auth/AuthHeader.tsx";
import Footer from "../components/Footer.tsx";
import {useState} from "react";
import {Button} from "../components/Button.tsx";
import {Faq} from "../components/Homepage/Components/Faq.tsx";
import BlueTick from "../components/icons/BlueTick.tsx";
import Info from "../components/icons/Info.tsx";
import IJewelBranding from "../components/icons/iJewelBranding.tsx";
import { useUser } from "../provider/UserProvider.ts";
import toast from "react-hot-toast";
import { useNavigate } from "react-router";
import { Popover, PopoverContent, PopoverTrigger, Button as B2 } from "@nextui-org/react";

export default function Pricing() {
    const [isYearly, setIsYearly] = useState(true);
    const {api , user} = useUser();
    const navigate = useNavigate();

    const subscribed = useMemo(() => user?.meta?.plan && user?.meta?.plan !== "free", [user?.meta?.plan]);

    const lookUpkeys = useMemo(
      () => ({
        "start-up": {
          monthly: "ij_drive_start_up_monthly",
          yearly: "ij_drive_start_up_yearly",
        },
        premium: {
          monthly: "ij_drive_premium_monthly",
          yearly: "ij_drive_premium_yearly",
        },
        business: {
          monthly: "ij_drive_business_monthly",
          yearly: "ij_drive_business_yearly",
        },
      }),
      []
    );


    const handleCheckout = useCallback(async (lookup: string) => {
          if (!api) return;
          if(!user){
            toast.error("Please login to continue");
            return;
          }

          toast("Please contact us to buy this plan.");
          return;
        //   const { error } = await api.checkout(lookup);

        //   if(error){
        //     toast.error("Error processing payment");
        //     return;
        //   }
    }, [api, user] );

    const handleUpgradeClick = useCallback(async (type: "start-up" | "premium" | "business") => {
        if (!api) return;

        toast("Please contact us to buy this plan.");
          return;

        // if(subscribed){
        //     const {error} = await api.manageSubscription();
        //     if(error){
        //         toast.error("Error managing subscription");
        //     }
        //     return;
        // }

        // handleCheckout(isYearly ? lookUpkeys[type].yearly : lookUpkeys[type].monthly)

    },[api, handleCheckout, isYearly, lookUpkeys, subscribed]);

    const plans = useMemo(() => [
        {
            name: 'FREE',
            buttonText: subscribed ? "Manage"  : 'Try now',
            values: ['20 GB', '50 MB', '50', <IJewelBranding className="h-9" />, <BlueTick />, <BlueTick />, <BlueTick />,'🔒','🔒','🔒','🔒','🔒', <BlueTick/>],
            recommended: false,
            onClick: ()=> subscribed ? handleUpgradeClick("start-up") : navigate("..") // if user is subscribed, redirect to manage subscription
        },
        {
            name: 'START-UP',
            buttonText: subscribed ? "Manage"  :'Buy now',
            values: ['40 GB', '100 MB', '200', <IJewelBranding className="h-9" />, <BlueTick />, <BlueTick />, <BlueTick />, <BlueTick/>, '🔒', <span>Addon $50 / month</span>, '🔒', <BlueTick/>, <BlueTick/>],
            recommended: false,
            onClick : ()=> handleUpgradeClick("start-up")
        },
        {
            name: 'PREMIUM',
            buttonText: subscribed ? "Manage"  : 'Buy now',
            values: ['100 GB', '100 MB', '500', <BlueTick />, <BlueTick />, <BlueTick />, <BlueTick />, <BlueTick/>, <BlueTick/>,  <p>One included.<br></br><span className="font-normal text-small">Addon $50 / month</span></p>, '🔒', <BlueTick/>, <BlueTick/>],
            recommended: false,
            onClick : ()=> handleUpgradeClick("premium")
        },
        {
            name: 'BUSINESS',
            buttonText: subscribed ? "Manage"  : 'Buy now',
            values: ['200 GB', '300 MB', '1500', <BlueTick />, <BlueTick />, <BlueTick />, <BlueTick />, <BlueTick />, <BlueTick />, <p>One included.<br></br><span className="font-normal text-small">Addon $50 / month</span></p>, '🔒', <BlueTick/>, <BlueTick/>],
            recommended: true,
            onClick : ()=> handleUpgradeClick("business")
        },
        {
            name: 'ENTERPRISE',
            buttonText: 'Contact Us',
            values: ['Custom', '500 MB', 'Custom', <BlueTick />, <BlueTick />, <BlueTick />, <BlueTick />, <BlueTick />, <BlueTick />, <BlueTick />, <BlueTick />, <BlueTick />, <BlueTick />],
            recommended: false,
            onClick : ()=> window.open('https://ijewel3d.zohobookings.in/#/209816000000031082', '_blank')
        }
    ] , [handleUpgradeClick, navigate, subscribed]);

    const planColumns = [
      {
        title: "Bandwidth GB/month",
        detail:
          "Data transferred from iJewel Drive to your websites and apps, where iJewel viewer is embedded.",
      },
      {
        title: "Max file size / model",
        detail:
          "This includes 3d mesh, textures, and any other files associated with the 3d model. This limit applies to the final compressed file size which is stored on our servers.",
      },
      {
        title: "Model Storage upto",
        detail:
          "Total number of 3d models which can be stored. (Unlisted + Private Models).",
      },
      {
        title: "Custom branding",
        detail:
          "Remove iJewel logo, and add your own logo for a white labeled experience.",
      },
      { title: "Secure sharing", detail: "" },
      { title: "Encryption", detail: "" },
      { title: "Viewer embedding on custom domains", detail: "" },
      {
        title: "Remove controls",
        detail: "Hide extra buttons and control options in the 3d viewer.",
      },
      {
        title: "Remove outside link",
        detail: "Remove all links back to iJewel drive.",
      },
      {
        title: "Extra domain (Embedding)",
        detail:
          "From Premium plan onwards one domain is included, where iJewel viewer can be embedded. Embedding iJewel viewer on Extra domain(s) can be purchased as an addon.",
      },
      {
        title: "Dedicated hosting",
        detail:
          "Dedicated hosting provides each customer with dedicated storage, load balancers, optimized performance to handle traffic surges seamlessly, and the ability to use a custom domain, along with increased storage and bandwidth limits.",
      },
      {
        title: "Custom loading logo / Remove iJewel Loading Logo",
        detail: "Remove iJewel logo at loading, and/or add your custom logo.",
      },
      {
        title: "iJewel SDK",
        detail: "Build apps and configurators using iJewel SDK",
      },
    ];

    return (
      <div className="w-full min-h-dvh font-ltnormal flex flex-col">
        <AuthHeader backgroundColor="bg-[#F0F1FF]" />
        <main className="flex-1">
          <div className="bg-[#F0F1FF] py-16">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center">
                <h2 className="text-xl font-bold sm:text-4xl mt-8">
                  Choose the best plan for your business
                </h2>
                <p className="mt-6 text-xl">Upload - Design - Share</p>
              </div>

              <div className="mt-1 sm:mt-5 flex justify-center md:justify-end">
                <div className="flex items-center">
                  <PricingTabs
                    isYearly={isYearly}
                    setIsYearly={setIsYearly}></PricingTabs>
                </div>
              </div>

              <div className="mt-10 grid grid-cols-1 md:grid-cols-4 gap-6">
                {/* Free Plan */}
                {/*<div className="w-full max-w-[300px] mx-auto bg-white rounded-[20px] p-8">*/}
                {/*    <h3 className="text-[20px] font-medium text-center text-[#373737]">*/}
                {/*        FREE*/}
                {/*    </h3>*/}
                {/*    */}
                {/*    <div className="mt-6 text-center">*/}
                {/*        <div className="text-[40px] font-semibold text-[#373737]">0$</div>*/}
                {/*        <div className="mt-1 text-[14px] text-[#9BA1A8]">forever free</div>*/}
                {/*    </div>*/}

                {/*    <div className="mt-8 space-y-4">*/}
                {/*        <div className="flex justify-between items-center py-3 border-b border-[#E4E4E7]">*/}
                {/*            <span className="text-[14px] text-[#535353]">Bandwidth GB/month</span>*/}
                {/*            <span className="text-[14px] text-[#535353]">20 GB</span>*/}
                {/*        </div>*/}
                {/*        <div className="flex justify-between items-center py-3 border-b border-[#E4E4E7]">*/}
                {/*            <span className="text-[14px] text-[#535353]">Max file size / model</span>*/}
                {/*            <span className="text-[14px] text-[#535353]">50 MB</span>*/}
                {/*        </div>*/}
                {/*        <div className="flex justify-between items-center py-3 border-b border-[#E4E4E7]">*/}
                {/*            <span className="text-[14px] text-[#535353]">Model Storage upto</span>*/}
                {/*            <span className="text-[14px] text-[#535353]">50</span>*/}
                {/*        </div>*/}
                {/*        <div className="flex justify-between items-center py-3 border-b border-[#E4E4E7]">*/}
                {/*            <span className="text-[14px] text-[#535353]">Custom logo</span>*/}
                {/*            <span className="text-[14px] text-[#9BA1A8]">🔒</span>*/}
                {/*        </div>*/}
                {/*    </div>*/}

                {/*    /!* Free Plan Button *!/*/}
                {/*    <Button*/}
                {/*        name="Get Started"*/}
                {/*        className="w-full border-[1px] mt-6 h-[48px] text-[#6E72F2] text-sm font-medium rounded-3xl border-[#6E72F2]"*/}
                {/*        varient="light"*/}
                {/*    />*/}
                {/*</div>*/}

                {/* Start-up Plan */}
                <div className="w-full max-w-[300px] mx-auto bg-white rounded-[20px] p-8">
                  <h3 className="text-[20px] font-medium text-center text-[#373737]">
                    START-UP
                  </h3>

                  <div className="mt-6 text-center">
                    {isYearly ? (
                      <>
                        <div className="text-[40px] font-semibold text-[#373737] flex items-center justify-center gap-3">
                          <span className="text-[#9BA1A8] text-2xl line-through">
                            720$
                          </span>
                          <span>600$</span>
                        </div>
                        <div className="mt-1 text-[14px] text-[#9BA1A8]">
                          per user / year
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="text-[40px] font-semibold text-[#373737]">
                          60$
                        </div>
                        <div className="mt-1 text-[14px] text-[#9BA1A8]">
                          per user / month
                        </div>
                      </>
                    )}
                  </div>

                  <div className="mt-8 space-y-4">
                    <div className="flex justify-between items-center py-3 border-b border-[#E4E4E7]">
                      <span className="text-[14px] text-[#535353]">
                        Bandwidth GB/month
                      </span>
                      <span className="text-[14px] text-[#535353]">40 GB</span>
                    </div>
                    <div className="flex justify-between items-center py-3 border-b border-[#E4E4E7]">
                      <span className="text-[14px] text-[#535353]">
                        Max file size / model
                      </span>
                      <span className="text-[14px] text-[#535353]">100 MB</span>
                    </div>
                    <div className="flex justify-between items-center py-3 border-b border-[#E4E4E7]">
                      <span className="text-[14px] text-[#535353]">
                        Model Storage upto
                      </span>
                      <span className="text-[14px] text-[#535353]">200</span>
                    </div>
                    <div className="flex justify-between items-center py-3 border-b border-[#E4E4E7]">
                      <span className="text-[14px] text-[#535353]">
                        Custom logo
                      </span>
                      <span className="text-[14px] text-[#9BA1A8]">🔒</span>
                    </div>
                  </div>

                  {/* Start-up Plan Button */}
                  <Button
                    name={subscribed ? "Manage Subscription" : "Get Start-up"}
                    className="w-full border-[1px] mt-6 h-[48px] text-[#6E72F2] text-sm font-medium rounded-3xl border-[#6E72F2]"
                    varient="light"
                    onClick={() => handleUpgradeClick("start-up")}
                    disabled={user?.meta?.plan === "start-up"}
                  />
                </div>

                {/* Premium Plan */}
                <div className="w-full max-w-[300px] mx-auto bg-white rounded-[20px] p-8">
                  <h3 className="text-[20px] font-medium text-center text-[#373737]">
                    PREMIUM
                  </h3>

                  <div className="mt-6 text-center">
                    {isYearly ? (
                      <>
                        <div className="text-[40px] font-semibold text-[#373737] flex items-center justify-center gap-3">
                          <span className="text-[#9BA1A8] text-2xl line-through">
                            1800$
                          </span>
                          <span>1500$</span>
                        </div>
                        <div className="mt-1 text-[14px] text-[#9BA1A8]">
                          per user / year
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="text-[40px] font-semibold text-[#373737]">
                          150$
                        </div>
                        <div className="mt-1 text-[14px] text-[#9BA1A8]">
                          per user / month
                        </div>
                      </>
                    )}
                  </div>

                  <div className="mt-8 space-y-4">
                    <div className="flex justify-between items-center py-3 border-b border-[#E4E4E7]">
                      <span className="text-[14px] text-[#535353]">
                        Bandwidth / month
                      </span>
                      <span className="text-[14px] text-[#535353]">100 GB</span>
                    </div>
                    <div className="flex justify-between items-center py-3 border-b border-[#E4E4E7]">
                      <span className="text-[14px] text-[#535353]">
                        Max file size / model
                      </span>
                      <span className="text-[14px] text-[#535353]">100 MB</span>
                    </div>
                    <div className="flex justify-between items-center py-3 border-b border-[#E4E4E7]">
                      <span className="text-[14px] text-[#535353]">
                        Model Storage upto
                      </span>
                      <span className="text-[14px] text-[#535353]">500</span>
                    </div>
                    <div className="flex justify-between items-center py-3 border-b border-[#E4E4E7]">
                      <span className="text-[14px] text-[#535353]">
                        Custom logo
                      </span>
                      <div className="text-center">
                        <BlueTick />
                      </div>
                    </div>
                  </div>

                  {/* Premium Plan Button */}
                  <Button
                    name={subscribed ? "Manage Subscription" : "Get Premium"}
                    className="w-full border-[1px] mt-6 h-[48px] text-[#6E72F2] text-sm font-medium rounded-3xl border-[#6E72F2]"
                    varient="light"
                    onClick={() => handleUpgradeClick("premium")}
                    disabled={user?.meta?.plan === "premium"}
                  />
                </div>

                {/* BUSINESS Plan */}
                <div className="w-full max-w-[300px] mx-auto bg-white rounded-[20px] p-8 relative border-2 border-[#6E72F2]">
                  <div className="absolute -top-3 left-1/2 -translate-x-1/2 bg-gradient-to-r from-[#6E72F2] to-[#00CFDC] text-white text-sm font-medium px-4 py-1 rounded-full">
                    RECOMMENDED
                  </div>
                  <h3 className="text-[20px] font-medium text-center text-[#373737]">
                    BUSINESS
                  </h3>

                  <div className="mt-6 text-center">
                    {isYearly ? (
                      <>
                        <div className="text-[40px] font-semibold text-[#373737] flex items-center justify-center gap-3">
                          <span className="text-[#9BA1A8] text-2xl line-through">
                            3600$
                          </span>
                          <span>3000$</span>
                        </div>
                        <div className="mt-1 text-[14px] text-[#9BA1A8]">
                          per user / year
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="text-[40px] font-semibold text-[#373737]">
                          300$
                        </div>
                        <div className="mt-1 text-[14px] text-[#9BA1A8]">
                          per user / month
                        </div>
                      </>
                    )}
                  </div>

                  <div className="mt-8 space-y-4">
                    <div className="flex justify-between items-center py-3 border-b border-[#E4E4E7]">
                      <span className="text-[14px] text-[#535353]">
                        Bandwidth / month
                      </span>
                      <span className="text-[14px] text-[#535353]">200 GB</span>
                    </div>
                    <div className="flex justify-between items-center py-3 border-b border-[#E4E4E7]">
                      <span className="text-[14px] text-[#535353]">
                        Max file size / model
                      </span>
                      <span className="text-[14px] text-[#535353]">300 MB</span>
                    </div>
                    <div className="flex justify-between items-center py-3 border-b border-[#E4E4E7]">
                      <span className="text-[14px] text-[#535353]">
                        Model Storage upto
                      </span>
                      <span className="text-[14px] text-[#535353]">1500</span>
                    </div>
                    <div className="flex justify-between items-center py-3 border-b border-[#E4E4E7]">
                      <span className="text-[14px] text-[#535353]">
                        Custom logo
                      </span>
                      <div className="text-center">
                        <BlueTick />
                      </div>
                    </div>
                  </div>

                  {/* BUSINESS Plan Button */}
                  <Button
                    name={subscribed ? "Manage Subscription" : "Get "}
                    className="w-full mt-6 h-[48px] text-white text-sm font-medium rounded-3xl"
                    heiglighted
                    onClick={() => handleUpgradeClick("business")}
                    disabled={user?.meta?.plan === "business"}
                  />
                </div>

                {/* Enterprise Plan */}
                <div className="w-full max-w-[300px] mx-auto bg-white rounded-[20px] p-8">
                  <h3 className="text-[20px] font-medium text-center text-[#373737]">
                    ENTERPRISE
                  </h3>

                  <div className="mt-6 text-center">
                    <div
                      className="text-[40px] font-semibold text-[#373737] cursor-pointer transition-colors duration-200 hover:text-transparent hover:bg-clip-text hover:bg-gradient-to-r hover:from-[#6E72F2] hover:to-[#00CFDC]"
                      onClick={() =>
                        window.open(
                          "https://ijewel3d.zohobookings.in/#/209816000000031082",
                          "_blank"
                        )
                      }>
                      Contact Us
                    </div>
                    <p className="mt-1 text-[14px] text-[#9BA1A8]">&nbsp;</p>
                  </div>

                  <div className="mt-8 space-y-4">
                    <div className="flex justify-between items-center py-3 border-b border-[#E4E4E7]">
                      <span className="text-[14px] text-[#535353]">
                        Bandwidth / month
                      </span>
                      <span className="text-[14px] text-[#535353]">Custom</span>
                    </div>
                    <div className="flex justify-between items-center py-3 border-b border-[#E4E4E7]">
                      <span className="text-[14px] text-[#535353]">
                        Max file size / model
                      </span>
                      <span className="text-[14px] text-[#535353]">500 MB</span>
                    </div>
                    <div className="flex justify-between items-center py-3 border-b border-[#E4E4E7]">
                      <span className="text-[14px] text-[#535353]">
                        Model Storage upto
                      </span>
                      <span className="text-[14px] text-[#535353]">Custom</span>
                    </div>
                    <div className="flex justify-between items-center py-3 border-b border-[#E4E4E7]">
                      <span className="text-[14px] text-[#535353]">
                        Custom logo
                      </span>
                      <div className="text-center">
                        <BlueTick />
                      </div>
                    </div>
                  </div>

                  {/* Enterprise Plan Button */}
                  <Button
                    name="Get Enterprise"
                    className="w-full border-[1px] mt-6 h-[48px] text-[#6E72F2] text-sm font-medium rounded-3xl border-[#6E72F2]"
                    varient="light"
                    onClick={() =>
                      window.open(
                        "https://ijewel3d.zohobookings.in/#/209816000000031082",
                        "_blank"
                      )
                    }
                  />
                </div>
              </div>

              <div className="text-center w-full  text-[#C8C8C8] text-sm mt-12 underline">
                {/*<Link href="/terms"> Terms apply</Link>*/}
              </div>
            </div>
          </div>
          <div className="w-full bg-[#F6F6F6] pt-12 md:pt-20 pb-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <h2 className="text-[28px] md:text-[40px] font-semibold text-center text-[#373737] mb-8 md:mb-16">
                All benefits
              </h2>

              {/* Mobile view - Vertical stack */}
              <div className="grid grid-cols-1 gap-6 lg:hidden">
                {plans.map((plan) => (
                  <div
                    key={plan.name}
                    className={`w-full max-w-[300px] mx-auto bg-white rounded-[20px] p-8
                                        ${
                                          plan.name === "BUSINESS"
                                            ? "border-2 border-[#6E72F2] relative"
                                            : "🔒"
                                        }`}>
                    {plan.name === "BUSINESS" && (
                      <div className="absolute -top-3 left-1/2 -translate-x-1/2">
                        <div className="bg-[#6E72F2] text-white text-xs px-4 py-1 rounded-full">
                          RECOMMENDED
                        </div>
                      </div>
                    )}
                    {plan.name != "START-UP" && (
                      <h3 className="text-[16px] font-bold text-center text-[#373737] mb-6">
                        {plan.name}
                      </h3>
                    )}
                    {plan.name == "START-UP" && (
                      <Popover>
                        <PopoverTrigger>
                          <h3 className="text-[16px] font-bold text-center text-[#373737] mb-6">
                            {plan.name}*
                          </h3>
                        </PopoverTrigger>
                        <PopoverContent>
                          {() => (
                            <div className="px-[15px] py-[10px] w-[270px]">
                              <div className="text-[#373737]">
                                The Startup plan includes a diamond hologram at
                                the bottom left of the canvas. Upgrade to the
                                Premium plan to remove it. Premium plans and
                                higher allow you to remove the diamond hologram
                                from the bottom left of the canvas.
                              </div>
                            </div>
                          )}
                        </PopoverContent>
                      </Popover>
                    )}
                    <div className="space-y-4">
                      {planColumns.map((label, idx) => (
                        <div
                          key={idx}
                          className="flex justify-between items-center py-3 border-b border-[#E4E4E7]">
                          <div className="flex items-center gap-1">
                            <span className="text-[14px] text-[#535353]">
                              {label.title}
                            </span>
                            {label.detail != "" && (
                              <div>
                                <Popover>
                                  <PopoverTrigger>
                                    <B2 className="bg-transparent">
                                      <Info className="cursor-pointer" />
                                    </B2>
                                  </PopoverTrigger>
                                  <PopoverContent>
                                    {(details) => (
                                      <div className="px-[15px] py-[10px] w-[270px]">
                                        <h3
                                          className="text-small font-bold text-[#373737]"
                                          {...details}>
                                          {label.title}
                                        </h3>
                                        <div className="text-[#373737] mt-2">
                                          {label.detail}
                                        </div>
                                      </div>
                                    )}
                                  </PopoverContent>
                                </Popover>
                              </div>
                            )}
                          </div>
                          <div className="text-[14px] text-[#535353]">
                            {React.isValidElement(plan.values[idx])
                              ? plan.values[idx]
                              : plan.values[idx]}
                          </div>
                        </div>
                      ))}
                    </div>
                    <Button
                      name={plan.buttonText}
                      onClick={plan.onClick}
                      className={`w-full mt-6 h-[48px] text-sm font-medium border-[1px] border-[#6E72F2] rounded-3xl ${
                        plan.name === "BUSINESS"
                          ? "bg-[#6E72F2] text-white"
                          : "text-[#6E72F2]"
                      }`}
                      varient={"light"}
                    />
                  </div>
                ))}
              </div>

              {/* Desktop view - Original grid layout */}
              <div className="hidden lg:grid grid-cols-[1fr_repeat(5,minmax(0,1fr))] gap-6">
                {/* Labels column */}
                <div className="hidden lg:block bg-white rounded-[20px] py-[20px] w-[220px]">
                  <div className="h-[102px]"></div>
                  <div className="flex flex-col gap-6">
                    {planColumns.map((label, idx) => (
                      <div
                        key={idx}
                        className={`flex items-center justify-between px-6 ${
                          idx % 2 === 0 ? "bg-[#F4F4F5] py-4" : "bg-white"
                        }`}>
                        <div
                          className={`h-[44px] flex items-center text-[14px] text-[#535353] justify-center text-start`}>
                          {label.title}
                        </div>
                        {label.detail != "" && (
                          <div>
                            <Popover>
                              <PopoverTrigger>
                                <B2 className="bg-transparent">
                                  <Info className="cursor-pointer" />
                                </B2>
                              </PopoverTrigger>
                              <PopoverContent>
                                {(details) => (
                                  <div className="px-[15px] py-[10px] w-[270px]">
                                    <h3
                                      className="text-small font-bold text-[#373737]"
                                      {...details}>
                                      {label.title}
                                    </h3>
                                    <div className="text-[#373737] mt-2">
                                      {label.detail}
                                    </div>
                                  </div>
                                )}
                              </PopoverContent>
                            </Popover>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Plan columns */}
                {plans.map((plan) => (
                  <div
                    key={plan.name}
                    className={`${
                      plan.name === "BUSINESS"
                        ? "md:col-span-2 lg:col-span-1"
                        : "🔒"
                    }`}>
                    {plan.name === "BUSINESS" ? (
                      <div className="relative w-[196x]">
                        <div className="absolute -top-3 left-1/2 -translate-x-1/2 z-10">
                          <div className="bg-[#6E72F2] text-white text-xs px-4 py-1 rounded-full">
                            RECOMMENDED
                          </div>
                        </div>
                        <div className="border-2 border-[#6E72F2] rounded-[20px] w-full">
                          <div className="bg-white rounded-[18px] py-[20px]">
                            <PlanContent plan={plan} isPro={true} />
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="bg-white rounded-[20px] max-w-[196px] h-full py-[20px]">
                        <PlanContent plan={plan} isPro={false} />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </main>
        <Faq />
        <Footer />
      </div>
    );
}

interface PricingTabsProps {
    isYearly: boolean;
    setIsYearly: (value: boolean) => void;
}

export function PricingTabs({ isYearly, setIsYearly }: PricingTabsProps) {
    return (
        <div className="flex items-center gap-1">
            <div className="flex flex-col gap-2 w-[168px]">
                <div className="flex gap-2 items-center">
                    <span className={`text-sm ${!isYearly ? 'text-black' : 'text-[#9BA1A8]'}`}>
                    Monthly
                </span>
                    <button
                        onClick={() => setIsYearly(!isYearly)}
                        className="w-12 h-6 rounded-full p-1 relative bg-[#E4E4E7]"
                    >
                        <div
                            className={`w-4 h-4 rounded-full bg-white transition-transform duration-200 ${
                                isYearly ? 'translate-x-6' : 'translate-x-0'
                            }`}
                        />
                    </button>
                    <span className={`text-sm ${isYearly ? 'text-black' : 'text-[#9BA1A8]'}`}>
                    Yearly
                </span>
                </div>
                <div className="flex justify-end">
                    <span className="text-xs text-[#9BA1A8]">
                        Save 20%
                    </span>
                </div>
            </div>
        </div>
    );
}

interface Plan {
    name: string;
    buttonText: string;
    values: (string | boolean | JSX.Element)[];
    recommended: boolean;
    onClick: () => void;
}

interface PlanContentProps {
    plan: Plan;
    isPro: boolean;
}

const PlanContent = ({ plan, isPro }: PlanContentProps) => (
    <>
        <div className="h-[102px] text-center">
                {plan.name != "START-UP" && <h3 className="text-[16px] font-bold text-[#373737] mb-4">{plan.name}</h3>}
                {plan.name == "START-UP" && (
                    <Popover>
                        <PopoverTrigger>
                            <h3 className="text-[16px] font-bold text-[#373737] mb-4 hover:cursor-pointer">{plan.name}*</h3>
                        </PopoverTrigger>
                        <PopoverContent>
                            {(details) => (
                                <div className="px-[15px] py-[10px] w-[350px]">
                                    <h3 className="text-small font-bold text-[#373737]" {...details}>
                                        {/* {label.title} */}
                                    </h3>
                                    <div className="text-[#373737]">The Startup plan includes a diamond hologram at the bottom left of the canvas. Upgrade to the Premium plan to remove it. Premium plans and higher allow you to remove the diamond hologram from the bottom left of the canvas.</div>
                                </div>
                            )}
                        </PopoverContent>
                    </Popover>
                )}
            <div className="flex justify-center">
                <Button
                    name={plan.buttonText}
                    onClick={plan.onClick}
                    className={`w-[120px] h-[40px] text-sm font-medium border-[1px] border-[#6E72F2] rounded-3xl ${
                        isPro
                            ? 'bg-[#6E72F2] text-white hover:text-primary'
                            : 'text-[#6E72F2]'
                    }`}
                    varient={'light'}
                />
            </div>
        </div>

        <div className="flex flex-col gap-6">
            {plan.values.map((value, idx) => (
                <div key={idx} className={`${(idx <= 2 || idx > 5) && idx % 2 === 0 ? 'bg-[#F4F4F5]' : 'bg-white'}`}>
                    <div
                        className={`h-[44px] flex items-center justify-center text-center ${idx % 2 === 0 ? ' my-4':''}`}>
                        {typeof value === 'boolean' ? (
                            value ? <BlueTick /> : null
                        ) : React.isValidElement(value) ? (
                            value
                        ) : (
                            <span className="text-[#373737] font-semibold text-[15px]">{value}</span>
                        )}
                    </div>
                </div>
            ))}
        </div>
    </>
)
