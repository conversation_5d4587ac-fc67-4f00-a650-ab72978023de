import {useCallback, useState} from "react";
import {useUser} from "../../provider/UserProvider.ts";
import toast from "react-hot-toast";
import {Outlet, useLocation} from "react-router-dom";
import ModalProvider from "../../provider/ModalProvider.tsx";
import {Button, Input} from "@nextui-org/react";
import {validateEmail} from "../shared/util.ts";
import LoginLogo from "../icons/LoginLogo.tsx";
import AuthHeader from "./AuthHeader.tsx";
import BaseLink from "../BaseLink.tsx";
import ForgotPasswordConfirmation from "./ForgotPasswordConfirmation.tsx";

// Validation function that returns error message or undefined
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function validateField(type: string, value: string, _compareValue?: string): string | undefined {
  switch (type) {
    case "email":
      if (!validateEmail(value)) return "Please enter a valid email";
      break;
    default:
      break;
  }
  return undefined;
}

function ForgotPassword() {
  const { api } = useUser();
  const [submitted, setSubmitted] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [mailSent, setMailSent] = useState<boolean>(false);
  const location = useLocation();

  // for signup
  const [email, setEmail] = useState<string>("");

  // Error messages
  const emailError = submitted ? validateField("email", email) : undefined;

  const handleResetPassword = useCallback(async () => {
    setSubmitted(true);

    if (!api) {
      toast.error("API not initialized");
      return;
    }

    // Re-validate fields

    const emailError1 = validateField("email", email);

    // Collect all errors
    const allErrors = [emailError1].filter(Boolean);

    if (allErrors.length > 0) {
      toast.error(allErrors[0] as string);
      return;
    }

    setLoading(true);

    const { error } = await api.requestPasswordReset({
          email,
        })

    setLoading(false);

    if (error) {
      toast.error(`${error}`);
    } else {
      toast.success("Check your email and click on the link to reset your password");
      // todo navigate to some page or show the status properly instead of toast, so people cannot resubmit.
      // setIsLogin(true);
      setMailSent(true);
      // setUser(user);
    }
  }, [email, api]);

  const isAuthRoute = location.pathname.includes("/forgot-password");

  return isAuthRoute ? (
      <>
        <AuthHeader/>
        {!mailSent ? 
          <div className="w-screen h-screen flex justify-center items-center fixed t-0 l-0 z-10 bg-[#F6F6F6]">
            <div className="w-[400px] flex flex-col gap-unit-xl p-7">
              <div className="flex items-center justify-center relative">
                <LoginLogo className="h-8" />
              </div>
              <p className="text-2xl font-bold text-center">{"Reset Password"}</p>

              <form
                  className="flex flex-col gap-unit-xl"
                  onSubmit={(e) => {
                    e.preventDefault();
                    handleResetPassword();
                  }}
              >
                <div className="flex gap-2 items-center h-[38px]">
                  <label className="flex text-sm text-gray-700 w-[100px] justify-end font-semibold">Email</label>
                  <Input
                      required
                      onValueChange={setEmail}
                      value={email}
                      placeholder="Enter your email"
                      variant="bordered"
                      radius="full"
                      classNames={{
                        base: "w-full",
                        input: "bg-transparent text-gray-600 placeholder:text-gray-400",
                        inputWrapper: "bg-white h-[38px] shadow-none border border-gray-200 hover:border-gray-300",
                        innerWrapper: "pr-4",
                        label: "text-sm text-gray-700"
                      }}
                      isInvalid={!!emailError}
                      errorMessage={emailError}
                  />
                </div>
                <div className="flex gap-unit-xl w-full">
                  <Button type="submit" isLoading={loading} className="h-11 w-full rounded-full bg-[#6E72EA] text-white font-medium" color="primary">
                    {"Reset Password"}
                  </Button>
                </div>
                {/* <Button
                    variant="bordered"
                    className="h-12 w-full rounded-full border-gray-300 font-normal"
                    startContent={
                    <img src="https://www.google.com/favicon.ico" className="w-5 h-5" alt="Google" />
                    }
                  >
                    Log in with Google
                  </Button> */}
              </form>
              <div className="text-center">
                <span className="text-[#686868] text-sm">
                  {" < Back to "}
                </span>
                <BaseLink to={"/login"} className="text-[#6E72EA] text-sm underline">
                  {"Log in"}
                </BaseLink>
              </div>
            </div>
          </div>
        :
        <ForgotPasswordConfirmation email={email}/>}
      </>
  ) : (
    <ModalProvider>
      <Outlet />
    </ModalProvider>
  );
}

export default ForgotPassword;
