import { useEffect, useState } from "react";
import { CustomFileData } from "../api/api";
import {  useParams } from "react-router-dom";
import { useUser } from "../provider/UserProvider";
import ViewFile from "../components/ViewFile";
import toast from "react-hot-toast";
import { Header } from "../components/Header";
import { useBrowser } from "../provider/BrowserProvider";
import { ViewType } from "../types/views";


function View() {
  const {fileId} = useParams();
  const {api, isLogin } = useUser();
  const [file, setFile] = useState<CustomFileData | null>(null);
  const [error, setError] = useState("");
  const { currentView, setCurrentView } = useBrowser();

  const handleViewChange = (view: ViewType) => {
      setCurrentView(view);
    };

  useEffect(() => {
    if(!api) return;

    if(fileId) {
      api.viewFile(fileId).then((data) => {
        if(!data.data  || data.error) {
          toast.error("Failed to load file");
          if(isLogin){
            setError("File might have been deleted or you don't have permission to view, please contact the owner");
          }else{
            setError("Please login to view this file");
          }
          console.log(data.error);
          return;
        }
        setFile( prev => prev?.name === data.data?.name  ? prev : data.data);
      });
    }
  }, [fileId, api, isLogin]);


  return (
    <div className="w-screen h-screen overflow-hidden flex flex-col">
      <Header onViewChange={handleViewChange} currentView={currentView} className="fixed top-0 z-[1000]"/>
      {error ? <div className="text-center text-red-500 h-full justify-center items-center flex">{error}</div> : <ViewFile file={file}/>}
    </div>
  );
}

export default View;
