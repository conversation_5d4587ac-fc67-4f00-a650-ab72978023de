import { SVGProps } from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={15}
    height={16}
    viewBox="0 0 15 16"
    fill="currentColor"
    {...props}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.5142 16C10.8197 16 10.2294 15.7569 9.74333 15.2708C9.25722 14.7847 9.01416 14.1944 9.01416 13.5C9.01416 13.3889 9.0211 13.2882 9.03499 13.1979C9.04888 13.1076 9.06972 13.0069 9.09749 12.8958L4.13916 9.875C3.93083 10.0972 3.68534 10.2569 3.4027 10.3542C3.11992 10.4514 2.82374 10.5 2.51416 10.5C1.81972 10.5 1.22944 10.2569 0.743327 9.77083C0.257216 9.28472 0.0141602 8.69444 0.0141602 8C0.0141602 7.30556 0.257216 6.71528 0.743327 6.22917C1.22944 5.74306 1.81972 5.5 2.51416 5.5C2.81972 5.5 3.11485 5.55208 3.39958 5.65625C3.6843 5.76042 3.93083 5.91667 4.13916 6.125L9.09749 3.10417C9.06972 2.99306 9.04888 2.89236 9.03499 2.80208C9.0211 2.71181 9.01416 2.61111 9.01416 2.5C9.01416 1.80556 9.25722 1.21528 9.74333 0.729167C10.2294 0.243056 10.8197 0 11.5142 0C12.2086 0 12.7989 0.243056 13.285 0.729167C13.7711 1.21528 14.0142 1.80556 14.0142 2.5C14.0142 3.19444 13.7711 3.78472 13.285 4.27083C12.7989 4.75694 12.2086 5 11.5142 5C11.2046 5 10.9084 4.95139 10.6256 4.85417C10.343 4.75694 10.0975 4.59722 9.88916 4.375L4.93083 7.39583C4.9586 7.50694 4.97944 7.60764 4.99333 7.69792C5.00722 7.78819 5.01416 7.88889 5.01416 8C5.01416 8.11111 5.00722 8.21181 4.99333 8.30208C4.97944 8.39236 4.9586 8.49306 4.93083 8.60417L9.88916 11.625C10.0975 11.3889 10.343 11.2257 10.6256 11.1354C10.9084 11.0451 11.2046 11 11.5142 11C12.2086 11 12.7989 11.2431 13.285 11.7292C13.7711 12.2153 14.0142 12.8056 14.0142 13.5C14.0142 14.1944 13.7711 14.7847 13.285 15.2708C12.7989 15.7569 12.2086 16 11.5142 16ZM11.5142 3.5C11.7975 3.5 12.035 3.40417 12.2267 3.2125C12.4183 3.02083 12.5142 2.78333 12.5142 2.5C12.5142 2.21667 12.4183 1.97917 12.2267 1.7875C12.035 1.59583 11.7975 1.5 11.5142 1.5C11.2308 1.5 10.9933 1.59583 10.8017 1.7875C10.61 1.97917 10.5142 2.21667 10.5142 2.5C10.5142 2.78333 10.61 3.02083 10.8017 3.2125C10.9933 3.40417 11.2308 3.5 11.5142 3.5ZM2.51416 9C2.79749 9 3.03499 8.90417 3.22666 8.7125C3.41833 8.52083 3.51416 8.28333 3.51416 8C3.51416 7.71667 3.41833 7.47917 3.22666 7.2875C3.03499 7.09583 2.79749 7 2.51416 7C2.23083 7 1.99333 7.09583 1.80166 7.2875C1.60999 7.47917 1.51416 7.71667 1.51416 8C1.51416 8.28333 1.60999 8.52083 1.80166 8.7125C1.99333 8.90417 2.23083 9 2.51416 9ZM11.5142 14.5C11.7975 14.5 12.035 14.4042 12.2267 14.2125C12.4183 14.0208 12.5142 13.7833 12.5142 13.5C12.5142 13.2167 12.4183 12.9792 12.2267 12.7875C12.035 12.5958 11.7975 12.5 11.5142 12.5C11.2308 12.5 10.9933 12.5958 10.8017 12.7875C10.61 12.9792 10.5142 13.2167 10.5142 13.5C10.5142 13.7833 10.61 14.0208 10.8017 14.2125C10.9933 14.4042 11.2308 14.5 11.5142 14.5Z"
      fill="#373737"
    />
  </svg>
);
export default SvgComponent;
