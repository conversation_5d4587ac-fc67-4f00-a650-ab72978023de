import { FC, useState } from "react";
import { Modal } from "../Modal";
import { useModal } from "../../provider/useModal";
import { Button } from "../Button";
import { Input, Select, SelectItem } from "@nextui-org/react";
import Close from "../icons/Close";

interface DeleteAccountModalProps {
  onClose: () => void;
}

const reasons = [
  "I don't use iJewel Drive enough",
  "Other"
];

const DeleteAccountModal: FC<DeleteAccountModalProps> = (props) => {
  const { modalState } = useModal();
  const [password, setPassword] = useState("");
  const [reason, setReason] = useState("");

  const handleDelete = () => {
    // TODO: Implement account deletion logic
    console.log("Deleting account with reason:", reason);
    props.onClose();
  };

  return (
    <Modal isOpen={modalState.modalType === "DELETE_ACCOUNT"} onClose={props.onClose} size="2xl" backdrop="blur" hideCloseButton>
      {() => (
        <div className="w-full bg-white p-6 flex flex-col gap-4 rounded-xl">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Delete account</h2>
            <Button
              varient="light"
              size="sm"
              onClick={props.onClose}
              className="w-8 h-8 min-w-8"
              name={<Close />}
              fullWidth={false}
            />
          </div>

          <div className="space-y-2">
            <p className="text-gray-600">Delete your account permanently?</p>
            <ul className="list-disc text-gray-600 ml-5 space-y-1 text-sm">
              <li>Your files will be gone forever and you'll no longer have access to anything that's been shared with you</li>
              <li>Deleting your account is not reversible. Once deleted, we cannot retrieve it.</li>
            </ul>
          </div>

          <div className="space-y-4 pt-2">
            <p className="text-gray-600">Before you delete your account, we might be able to help. Get in touch with us</p>
            <div className="space-y-3">
              <div>
                <p className="text-sm text-gray-600 mb-1.5">Reason for leaving</p>
                <Select
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  placeholder="Choose an option"
                  className="w-full"
                  variant="bordered"
                  classNames={{
                    trigger: "h-11 rounded-full"
                  }}
                >
                  {reasons.map((r) => (
                    <SelectItem key={r} value={r}>
                      {r}
                    </SelectItem>
                  ))}
                </Select>
              </div>
              
              <div>
                <Input
                  type="password"
                  placeholder="Password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  variant="bordered"
                  classNames={{
                    input: "h-11 rounded-full",
                    inputWrapper: "h-11 rounded-full"
                  }}
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-2 pt-2">
            <Button
              varient="light"
              size="sm"
              name="Cancel"
              onClick={props.onClose}
              className="px-4 py-1.5 rounded-full h-9"
              fullWidth={false}
            />
            <Button
              size="sm"
              name="Delete permanently"
              onClick={handleDelete}
              className="px-4 py-1.5 rounded-full h-9 bg-red-500 hover:bg-red-600 text-white"
              fullWidth={false}
            />
          </div>
        </div>
      )}
    </Modal>
  );
};

export default DeleteAccountModal; 