import { UserPlan } from "./types";

type UserLimitsType = {
    [key in UserPlan]: {
        maxNumberOfModels: number;
        maxFileSizeMb: number;
        bandwithPerMonthGb?: number;
        allowedFileTypes?: string[];
    };
};

export const UserLimits : UserLimitsType = {
    "free" : {
        maxNumberOfModels: 50,
        maxFileSizeMb: 50,
        bandwithPerMonthGb: 40,
    },
    "start-up" : {
        maxNumberOfModels: 200,
        maxFileSizeMb: 100,
        bandwithPerMonthGb: 100,
    },
    "premium" : {
        maxNumberOfModels: 500,
        maxFileSizeMb: 100,
        bandwithPerMonthGb: 200,
    },
    "business" : { //pro
        maxNumberOfModels: 1500,
        maxFileSizeMb: 300,
        bandwithPerMonthGb: 200,
    },
    "enterprise" : {
        maxNumberOfModels: Infinity,
        maxFileSizeMb: 500,
    }
}