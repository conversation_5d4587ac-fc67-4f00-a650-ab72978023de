import { FC, useCallback, useState } from "react";
import { <PERSON><PERSON>, Chip, Progress } from "@nextui-org/react";
import { useBrowser } from "../../provider/BrowserProvider";
import { QueueItem } from "../shared/types";
import DownArrow from "../icons/DownArrow.tsx";
import UpArrow from "../icons/UpArrow.tsx";
import GreenTick from "../icons/GreenTick.tsx";

interface LoaderProps {
//   items?: QueueItem[];
}

const ColorMap = {
  idle: "default",
  downloading: "primary",
  uploading: "primary",
  processing: "warning",
  queued: "default",
  finished: "success",
  error: "danger",
  cancelled: "danger",
};

const UploadProgress: FC<LoaderProps> = () => {
  const { fileQueue, updateQueue, setShowUploadProgress, showUploadProgress } = useBrowser();
  const [icons, setIcons] = useState<any>({});
  const [isCollapsed, setIsCollapsed] = useState(false);

  const onAbort = useCallback((item : QueueItem) => {
    if (item.data.abortController) {
      item.data.abortController.abort(); 
      setTimeout(() => { // hack to update the state after the abort error
        updateQueue(item.file, "cancelled", 0, { abortController: null });
      }, 100);
    }
  }, [updateQueue]);

  const getOrCreateIcon = useCallback((file: File) => {
    if (!icons[file.name]) {
      const icon =  URL.createObjectURL(file);
      setIcons((prev : any) => ({ ...prev, [file.name]: icon })); 
      return icon;
    }
    return icons[file.name];
  }, [icons]);

  const getOverallProgress = useCallback(() => {
    if (!fileQueue?.length) return 0;
    
    // Consider all files that are either uploading or processing
    const activeItems = [...fileQueue]
    if (!activeItems.length) return 0;
    
    // Calculate weighted progress based on file sizes
    let totalSize = 0;
    let totalProgress = 0;
    
    activeItems.forEach(item => {
      const fileSize = item.file.size || 1; // Prevent division by zero
      totalSize += fileSize;
      const isItemDone = item.state === "error" || item.state === "finished" || item.state === "cancelled";
      totalProgress += ((item.state === "uploading" ? item.progress : isItemDone ? 100 : 0 ) / 100) * fileSize;
    });
    
    return totalSize > 0 ? Math.round((totalProgress / totalSize) * 100) : 0;
  }, [fileQueue]);

  const getHeaderInfo = useCallback(() => {
    if (!fileQueue?.length) return { title: 'No files', subtitle: null, showProgress: false, progressValue: 0 };
    
    const uploadingItems = fileQueue.filter(item => item.state === 'uploading');
    const processingItems = fileQueue.filter(item => item.state === 'processing');
    const queuedItems = fileQueue.filter(item => item.state === 'queued');
    const finishedItems = fileQueue.filter(item => item.state === 'finished');
    const errorItems = fileQueue.filter(item => ['error', 'cancelled'].includes(item.state));
    
    const activeItems = [...uploadingItems, ...processingItems];
    
    if (activeItems.length > 0) {
      const progress = getOverallProgress();
      return {
        title: `Uploading • ${progress}%`,
        subtitle: activeItems.length > 1 ? `${activeItems.length} files in progress` : null,
        showProgress: true,
        progressValue: progress
      };
    } else if (queuedItems.length > 0) {
      return {
        title: 'Preparing upload...',
        subtitle: null,
        showProgress: true,
        progressValue: 0
      };
    } else if (errorItems.length) {
      const cancelledItems = fileQueue.filter(item => item.state === 'cancelled');
      const failedItems = fileQueue.filter(item => item.state === 'error');
      
      let subtitle = '';
      if (failedItems.length && cancelledItems.length) {
        subtitle = `${failedItems.length} failed, ${cancelledItems.length} cancelled`;
      } else if (failedItems.length) {
        subtitle = `${failedItems.length} file${failedItems.length > 1 ? 's' : ''} failed`;
      } else {
        subtitle = `${cancelledItems.length} file${cancelledItems.length > 1 ? 's' : ''} cancelled`;
      }

      return {
        title: `${finishedItems.length} files uploaded`,
        subtitle,
        showProgress: false,
        progressValue: 0
      };
    } else if (finishedItems.length > 0) {
      return {
        title: `${finishedItems.length} files uploaded`,
        subtitle: null,
        showProgress: true,
        progressValue: 100
      };
    } else {
      return {
        title: 'Uploading completed',
        subtitle: null,
        showProgress: false,
        progressValue: 0
      };
    }
  }, [fileQueue, getOverallProgress]);

  const getProgressColor = useCallback((progress: number) => {
    return progress === 100 ? "success" : "primary";
  }, []);

  if (!showUploadProgress || !fileQueue?.length) return null;

  const { title, subtitle, showProgress, progressValue } = getHeaderInfo();

  return (
    <div className={`fixed bottom-0 bg-[#F6F6F6] md:right-unit-4xl rounded-xl ${isCollapsed ? 'h-[80px]' : 'h-[340px]'} w-full md:w-[500px] md:bg-white flex flex-col z-50 shadow-2xl transition-all duration-300`}>
      <div className="p-unit-xl flex-shrink-0">
        <div className="flex items-center justify-between mb-unit-md">
          <div>
            <p className="text-[20px] font-medium">{title}</p>
            {subtitle && (
              <p className="text-sm text-danger">{subtitle}</p>
            )}
          </div>
          <div className="flex gap-unit-sm">
            <Button 
              isIconOnly 
              size="sm" 
              variant="light" 
              onClick={() => setIsCollapsed(!isCollapsed)}
            >
              {isCollapsed ? <UpArrow/> : <DownArrow/>}
            </Button>
            <Button 
              isIconOnly 
              size="sm" 
              variant="light"
              onClick={() => setShowUploadProgress(false)}
            >✕</Button>
          </div>
        </div>
        {showProgress && (
          <Progress
            color={getProgressColor(progressValue)}
            value={progressValue}
            className="h-[2px]"
          />
        )}
      </div>

      <div className="flex-1 overflow-hidden bg-white">
        <div className="h-full overflow-y-auto px-unit-xl">
          <p className="text-[15px] font-medium mb-unit-lg">Uploaded files</p>
          <div className="flex flex-col pb-2">
            {fileQueue?.map((item, i) => (
              <div key={item.file.name + i} className="flex items-center gap-unit-md h-[72px]">
                {item.data?.thumbnailFile && (
                  <img
                    className="h-[52px] w-[52px] object-cover rounded-[10px] ml-3"
                    src={getOrCreateIcon(item.data.thumbnailFile)}
                  />
                )}
                <div className="flex-1">
                  <p className={`text-sm font-medium ${item.state === 'error' ? 'text-danger' : ''}`}>
                    {item.file.name}
                  </p>
                  {item.state === 'error' ? (
                    <p className="text-xs text-danger">{item.data.errorMessage || "Upload failed! Please try again."}</p>
                  ) : (
                    <p className="text-xs text-gray-500">{(item.file.size / (1024 * 1024)).toFixed(1)} MB</p>
                  )}
                </div>
                {item.state === 'uploading' ? (
                  <p className="text-sm">{item.progress.toFixed(0)} %</p>
                ) : (
                  <div className={`w-5 h-5 flex items-center justify-center rounded-full 
                    ${item.state === 'finished' ? 'border-1 border-success text-success' : 
                      item.state === 'error' ? 'border-1 border-danger text-danger' : 
                      item.state === 'cancelled' ? 'border-1 border-warning text-warning' : ''}`}>
                    {item.state === 'finished' ? <GreenTick/> : 
                     item.state === 'error' ? '✕' : 
                     item.state === 'cancelled' ? '⊘' : ''}
                  </div>
                )}
                {item.data?.abortController && item.state === "uploading" && (
                  <Button isIconOnly size="sm" variant="light" onClick={() => onAbort(item)}>
                    ✕
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export { UploadProgress };
