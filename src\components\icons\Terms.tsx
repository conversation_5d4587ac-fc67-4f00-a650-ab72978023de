import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g clipPath="url(#clip0_201_4708)">
            <mask id="mask0_201_4708" style={{maskType: "alpha",}} maskUnits="userSpaceOnUse" x="0" y="0" width="20"
                  height="20">
                <rect width="20" height="20" fill="#D9D9D9"/>
            </mask>
            <g mask="url(#mask0_201_4708)">
                <path
                    d="M9.99563 6C10.2069 6 10.3854 5.92854 10.5312 5.78563C10.6771 5.64271 10.75 5.46563 10.75 5.25438C10.75 5.04313 10.6785 4.86458 10.5356 4.71875C10.3927 4.57292 10.2156 4.5 10.0044 4.5C9.79313 4.5 9.61458 4.57146 9.46875 4.71437C9.32292 4.85729 9.25 5.03437 9.25 5.24562C9.25 5.45687 9.32146 5.63542 9.46438 5.78125C9.60729 5.92708 9.78438 6 9.99563 6ZM9.25 12.5H10.75V7H9.25V12.5ZM2 18V3.5C2 3.0875 2.14688 2.73438 2.44063 2.44063C2.73438 2.14688 3.0875 2 3.5 2H16.5C16.9125 2 17.2656 2.14688 17.5594 2.44063C17.8531 2.73438 18 3.0875 18 3.5V13.5C18 13.9125 17.8531 14.2656 17.5594 14.5594C17.2656 14.8531 16.9125 15 16.5 15H5L2 18ZM4.375 13.5H16.5V3.5H3.5V14.375L4.375 13.5Z"
                    fill="#373737"/>
            </g>
        </g>
        <defs>
            <clipPath id="clip0_201_4708">
                <rect width="20" height="20" fill="white"/>
            </clipPath>
        </defs>
    </svg>
)
export default SvgComponent
