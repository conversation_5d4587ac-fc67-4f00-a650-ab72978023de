import { useCallback, useEffect, useMemo, useState } from "react";
import { useBrowser } from "../../provider/BrowserProvider";
import { useUser } from "../../provider/UserProvider";
import useWindowSize from "../../hooks/useWindowSize";
import { useLoaderData, useParams, useSearchParams } from "react-router-dom";
import toast from "react-hot-toast";
import Play from "../../components/icons/Play";
import { CircularProgress } from "@nextui-org/react";
import <PERSON>Viewer from "../../components/MiniViewer";
import { getEllipsisText, getNewUserName } from "../../components/shared/util";
import User from "../../components/icons/User";
import CloseCross from "../../components/icons/CloseCross";
//@ts-ignore
import Logo from "../../assets/logo-md.svg?inline";


interface State {
  profile: any;
  project: any;
  showCoverLoading: boolean;
  showCoverPlayButton: boolean;
  planName: string;
  startLoadingViewer: boolean;
  preview: boolean;
}
export default function EmbeddedViewer() {
  // const router = useRouter();
  const {api} = useUser()
  const {viewer} = useBrowser()
  const [searchParams] = useSearchParams()
  const {fileId} = useParams()
  const pageData : any = useLoaderData();

  const defaultState = {
    planName: "free",
    profile: null,
    project: null,
    preview: true,
    showCoverLoading: true,
    showCoverPlayButton: false,
    startLoadingViewer: false,
  };

  const [state, setState] = useState<State>(defaultState);

  const handleUpateState = useCallback((key: keyof typeof defaultState, value: any) => {
    setState((p) => ({ ...p, [key]: value }));
  }, []);

  const { isBelowBreakpoint: isBelowMd } = useWindowSize("md");
  const { isBelowBreakpoint: isBelowSm, isBelowBreakpointH: isBelowSmH } =
    useWindowSize("sm");

  const setting = useMemo(() => getsettings(searchParams), [searchParams]);

  const handlePreview = (isShow = false) => {
    const updateState: any = { preview: isShow, showCoverLoading: false };
    if (isShow) {
      updateState.showCoverPlayButton = true;
    } else {
      updateState.showCoverPlayButton = false;
    }

    setState((p) => ({ ...p, ...updateState }));
  };

  useEffect(() => {
    if (!api) return; 
    // if(!searchParams.get("slug")){
    //   toast.error("URL is invalid")
    //   return
    // }
    // console.log(searchParams.keys(), searchParams.values())
    const id = fileId ?? searchParams.get("slug") as string;


    if (!id) {
      toast.error("URl is invalid");
      return;
    } 
    // get projects
    handleUpateState("showCoverLoading", true);
    (async () => {
      let fileData = pageData?.file || state?.project;
      if (!fileData) {
        const data = await api.viewFile(id);
        console.log(data)
        if (!data.data || data.error) {
          toast.error("Project not found");
          return;
        }
        fileData = data.data;
      }

      if (!setting.viewer.cameraConfig) {
        delete fileData.config.cameraConfig;
      }

      const updateState: Partial<State> = {
        startLoadingViewer: false,
        showCoverPlayButton: true,
        showCoverLoading: false,
        project: { ...fileData },
        planName: "business",
      };
      if (setting.others.isAutoplay) {
        updateState.showCoverLoading = true;
        updateState.showCoverPlayButton = false;
        updateState.startLoadingViewer = true;
      }
      setState((p) => ({ ...p, ...updateState }));

      // get user
      api.viewUser(fileData.created_by).then((data) => {
        if (!data.data || !data.data.plan || data.error) {
          // default to free plan
          return;
        }
        setState((p) => ({ ...p, profile: data.data , planName : data.data.plan}));
      });


    })();

  }, [setting]);

  useEffect(() => {
    if (viewer) {
      handlePreview(false);
    }
  }, [viewer]);

  // const shareUrl = generateEmbeddingUrl({
  //   baseUrl: window.location.origin + "/embedded",
  //   settings: { ...defaultSetting, ...router.query },
  // });

  const shareUrl = useMemo(() => {
    return generateEmbeddingUrl({
      baseUrl: window.location.href,
      settings: setting,
    });
  }, [setting]);


  const embedLogo = useMemo(
    () => getEmbeddedLogo(state.planName, setting),
    [state, setting]
  );
  const showCard = isBelowSm ? false : setting.viewer.showCard;
  const isBusiness = state.planName !== "free";

  const headTitle = `${state.project?.name || "Embedded Model"} by ${
    state.project?.owner_username || "iJewel Design"
  }`;
  const headDesc=  state.project?.description || headTitle 

  const poster = state?.project?.thumbnailUrl ?? (state?.project?.thumb ? api?.getDownloadUrl(state?.project?.thumb) : state?.project?.config?.posterUrl);
  return (
    <>
    <head>
        <title>{headTitle}</title>
        <meta name="description" content={headDesc} />
        <meta property="og:title" content={headTitle} />
        <meta
          property="og:description"
          content={headDesc}
        />
        <meta name="keywords" content={headDesc} />
        <meta property="og:url" content={`${window.location.href}`} />
        <meta property="og:image" content="/images/3dconfigurator.webp" />
    </head>
    <div className="h-[calc(100dvh)] relative">
      {state.project && (
        <>
          <div
            style={{
              position: "absolute",
              width: "100%",
              height: "100%",
              display: state.preview ? "block" : "none",
              transition: "opacity 300ms ease-in",
              zIndex: 10,
            }}
          >
            {/* <ViewerHeader
              key={" state.preview-header-" + state.project.id}
              project={state.project}
              profile={state.profile}
              handleClose={() => handlePreview(true)}
              isShowPreview={true}
              isTitle={setting.others.isTitle}
              isMobileView={isBelowMd}
            /> */}
            <div
              style={{
                position: "absolute",
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
                cursor: "pointer",
                display: state.preview ? "block" : "none",
              }}
            >
              {state.showCoverPlayButton && (
                <Play
                  onClick={() => {
                    handleUpateState("startLoadingViewer", true);
                    handleUpateState("showCoverLoading", true);
                    handleUpateState("showCoverPlayButton", false);
                    if (viewer) handlePreview(false);
                  }}
                />
              )}
              {state.showCoverLoading && (
                <CircularProgress aria-label="Loading..." />
              )}
            </div>

            <img
              className="w-full h-full object-cover overflow-hidden"
              aria-label="cover"
              alt="cover"
              onError={(e) => {
                e.currentTarget.style.background = "#00000040";
              }}
              src={poster || "src-404"}
            />
            {state.profile && (
              <div
                style={{
                  position: "absolute",
                  bottom: isBelowMd ? 18 : 22,
                  left: isBelowMd ? 18 : 22,
                }}
              >
                {!embedLogo.hidden && <CoverLogo embedLogo={embedLogo} />}
              </div>
            )}
          </div>
          <div
            style={{
              opacity: state.preview ? 0 : 1,
              transition: "opacity 300ms ease-in",
              zIndex: 5,
              width: "100%",
              height: "100%",
            }}
          >
            {state.startLoadingViewer && (
              <MiniViewer
                key={"mini-viewer-" + state.project.id}
                {...setting.viewer}
                file={state.project}
                // headerComponent={
                //   <ViewerHeader
                //     key={"viewer-header-" + state.project.id}
                //     project={state.project}
                //     profile={state.profile}
                //     handleClose={() => handlePreview(true)}
                //     isShowPreview={false}
                //     isTitle={setting.others.isTitle}
                //     isMobileView={isBelowMd}
                //   />
                // }
                showCard={showCard}
                profile={state.profile}
                embedLogo={embedLogo}
                isBusiness={isBusiness}
                shareUrl={shareUrl}
              />
            )}
          </div>
        </>
      )}
    </div>
    </>
  );
}

// const getLogoHeight = (params: {
//   isBelowMd: boolean;
//   isBelowSm: boolean;
//   isBelowSmH: boolean;
//   showCard: boolean;
// }) => {
//   const { isBelowMd, isBelowSmH, showCard } = params;
//   let logoHeight = isBelowMd && showCard ? 0.17 : 0;
//   if (isBelowMd && isBelowSmH && showCard) logoHeight = 0.2;
//   return logoHeight;
// };

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const ViewerHeader = (props: {
  profile: any;
  project: any;
  handleClose: () => void;
  isShowPreview: boolean;
  isTitle: boolean;
  isMobileView: boolean;
}) => {
  const {
    profile,
    project,
    handleClose,
    isShowPreview,
    isTitle,
    isMobileView,
  } = props;
  const { isBelowBreakpoint: isBelowXsMSm } = useWindowSize("xsmsm");

  if (!isTitle || !project || !profile) return null;

  const { name } = project;
  const ownerName = getNewUserName(profile);
  const { avatar_url, username } = profile;

  const getAvatarProps = () => {
    const props: any = {
      src: avatar_url,
      style: { borderRadius: "50%", userSelect: "none" },
    };
    if (isBelowXsMSm) {
      props.width = 38;
      props.height = 38;
    } else {
      props.width = 40;
      props.height = 40;
    }
    return props;
  };

  return (
    <div
      style={{
        position: "absolute",
        top: 0,
        left: 0,
        display: "flex",
        alignContent: "center",
        alignItems: "center",
        width: "100%",
        justifyContent: "space-between",
        padding: isMobileView ? "23px 24px 18px 24px" : "23px 24px 18px 24px",
      }}
    >
      {isTitle ? (
        <a
          href={`https://ijewel.design/profile/${username}`}
          target="blank"
          rel="noreferrer"
        >
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: "10px",
            }}
          >
            {avatar_url ? (
              <img {...getAvatarProps()} />
            ) : (
              <div
                style={{
                  ...getAvatarProps(),
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  backgroundColor: "#D4D4D8",
                }}
              >
                <User />
              </div>
            )}

            <div
              style={{ color: "#FFFFFF", textShadow: "0px 0px 5px #00000099" }}
            >
              <h1
                style={{
                  fontSize: isBelowXsMSm ? "12px" : "14px",
                  fontWeight: 600,
                  lineHeight: "20px",
                }}
              >
                {isBelowXsMSm ? getEllipsisText(name, 20) : name}
              </h1>
              <h3
                style={{
                  fontSize: isBelowXsMSm ? "10px" : "12px",
                  fontWeight: 400,
                  margin: 0,
                  lineHeight: isBelowXsMSm ? "14px" : "16px",
                }}
              >
                {isBelowXsMSm
                  ? getEllipsisText(`by ${ownerName}`, 20)
                  : `by ${ownerName}`}
              </h3>
            </div>
          </div>
        </a>
      ) : (
        <div />
      )}
      {!isShowPreview && (
        <button
          onClick={handleClose}
          style={{
            height: isMobileView ? 33 : 40,
            width: isMobileView ? 33 : 40,
            backgroundColor: "#EFEFEFCC",
            borderRadius: "100%",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <CloseCross
            fill="#898989"
            width={isMobileView ? 12 : 14}
            height={isMobileView ? 12 : 14}
          />
        </button>
      )}
    </div>
  );
};

const CoverLogo = (props: { embedLogo: any }) => {
  const { embedLogo } = props;
  const [isHover, setIsHover] = useState(false);

  const handleMouseEnter = () => {
    setIsHover(true);
  };

  const handleMouseLeave = () => {
    setIsHover(false);
  };
  if (!embedLogo) return null;
  return (
    <div
      onClick={() => {
        if (embedLogo.url) window.open(embedLogo.url, "_blank");
      }}
      style={{
        width:
          isHover && embedLogo.logo
            ? (embedLogo.logoWidth ?? "140") + "px"
            : "40px",
        transitionDuration: "600ms",
        transitionProperty: "width, opacity",
        cursor: embedLogo.url ? "pointer" : "default",
      }}
      onMouseOver={handleMouseEnter}
      onMouseOut={handleMouseLeave}
      className={`perspective-1000 h-10 w-10 md:z-20 flex items-center rounded-full overflow-hidden bg-[#EFEFEFCC]`}
    >
      {embedLogo.iconAmimation === "rotate" && (
        <span
          style={{
            transform: isHover
              ? `translateY(-2px) rotate(45deg)`
              : `translateY(0px) rotate(0deg)`,
            transitionDuration: "600ms",
            transitionProperty: "transform",
          }}
          className={`absolute w-10 h-10 p-unit-lg`}
        >
          <img
            src={embedLogo.icon}
            alt="icon"
            className="object-contain h-full w-full"
          />
        </span>
      )}
      {(embedLogo.iconAmimation === "flip" || !embedLogo.iconAmimation) && (
        <span
          style={{
            transform: isHover ? `rotateY(3.142rad)` : `rotateY(0)`,
            transitionDuration: "600ms",
            transitionProperty: "transform",
          }}
          className={`absolute w-10 h-10 p-unit-md`}
        >
          <img
            src={embedLogo.icon}
            alt="icon"
            className="object-contain h-full w-full"
          />
        </span>
      )}
      {embedLogo.logo && (
        <span className={`pl-10 pr-[6px] h-10`}>
          <img src={embedLogo.logo} className="object-contain h-full w-full" />
        </span>
      )}
    </div>
  );
};

const defaultSetting = {
  isTitle: true,
  isRemoveLogo: false,
  isRemoveLogoLink: false,
  isRemoveLoadingBgImg: true,
  isShowPrompt: true,
  isTurntableAnimation: false,
  isAutoplay: false,
  isTransparentBackground: false,
  isConfigurator: true,
  isEnabledZoom: true,
  isShare: true,
  isShowModelInfo: false,
  isQuality: true,
  isResetView: true,
  isRotateCamera: true,
  isPlayCameraViews: true,
  isPlayAnimations: true,
  isFitObject: true,
  isFullScreen: true,
};
const getEmbeddedLogo = (planName: string, setting: any) => {
  const embedLogo: any = {
    icon: "https://drive.ijewel3d.com/logo-sm.svg",
    logo: Logo,
    url: "https://drive.ijewel3d.com",
    logoWidth: 148,
    iconAmimation: "flip",
    hidden: false,
  };
  if (planName !== "free") {
    if (setting.others.isRemoveLogoLink) embedLogo.url = null;
    // if (planName == "business") {
      embedLogo.logo = setting.others.isRemoveLogo
        ? null
        : embedLogo.logo;
      embedLogo.icon =  setting.others.isRemoveLogo ? "/icons/diamond.png" : embedLogo.icon;
      embedLogo.logoWidth = "112";
      embedLogo.iconAmimation = "flip";
      embedLogo.hidden = setting.others.isRemoveHologram ? true : false;
    // }
  }
  return embedLogo;
};
const getBoolean = (obj: any, key: string) => {
  const value = obj[key];
  if (typeof value === "boolean") return value;
  switch (value) {
    case "true":
      return true;
    case "false":
      return false;
    default:
      return value;
  }
};
const getsettings = (router: URLSearchParams) => {
  const settings : any= {
    ...defaultSetting,
  };

  const keys = Array.from(router.keys());
  keys.forEach((key) => {
    settings[key] = router.get(key as string);
  })

  return {
    viewer: {
      isTransparentBackground: getBoolean(settings, "isTransparentBackground"),
      showCard: getBoolean(settings, "isShowModelInfo"),
      showShareButton: getBoolean(settings, "isShare"),
      showConfigurator: getBoolean(settings, "isConfigurator"),
      showZoomButtons: getBoolean(settings, "isEnabledZoom"),
      hideQuality: !getBoolean(settings, "isQuality"),
      hideRotateCamera: !getBoolean(settings, "isRotateCamera"),
      hideCameraViews: !getBoolean(settings, "isPlayCameraViews"),
      hideGltfAnimations: !getBoolean(settings, "isPlayAnimations"),
      hideFitScene: !getBoolean(settings, "isFitObject"),
      hideFullScreen: !getBoolean(settings, "isFullScreen"),
      showPosterInLoadingScreen: !getBoolean(settings, "isRemoveLoadingBgImg"),
      disableInteractionPrompt: !getBoolean(settings, "isShowPrompt"),
      runRotateCamera: getBoolean(settings, "isTurntableAnimation"),
      cameraConfig: getBoolean(settings, "isResetView"),
      transparentBg: getBoolean(settings, "isTransparentBackground"),
    },
    others: {
      isAutoplay: getBoolean(settings, "isAutoplay"),
      isTitle: getBoolean(settings, "isTitle"),
      isRemoveLogo: getBoolean(settings, "isRemoveLogo"),
      isRemoveLogoLink: getBoolean(settings, "isRemoveLogoLink"),
      isRemoveHologram: getBoolean(settings, "isRemoveHologram"),
    },
  };
};

const generateEmbeddingUrl = ({
  baseUrl = window.location.origin,
  settings,
}: {
  baseUrl: string;
  settings: { [key: string]: any };
}) => {
  const url = new URL(baseUrl);
  const newSetting = { ...settings };

  // Iterate over the object and append each key-value pair as a query parameter.
  Object.entries(newSetting).forEach(([key, value]: [string, any]) => {
    if (
      value !== null &&
      value !== undefined &&
      defaultSetting[key as keyof typeof defaultSetting] !== value
    ) {
      // Exclude null or undefined values.
      url.searchParams.append(key, value);
    }
  });

  return url.toString();
};