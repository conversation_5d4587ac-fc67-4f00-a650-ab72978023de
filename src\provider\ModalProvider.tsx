/* eslint-disable @typescript-eslint/no-unused-vars */
import { createContext, ReactNode, useCallback, useEffect, useMemo, useState } from "react";
import ModalRoot from "../components/ModalRoot";
import { usePrompt } from "../components/Prompt";
import { useConfirm } from "../components/Confirm";
import { useUploadConfirm, UploadConfirmType } from "../components/upload/UploadConfirm";
import { UploadOptions, useUploadPrompt } from "../components/UploadPrompt";

type ModalType = 'TUTORIAL' | 'PREVIEW' | "DELETE" | "DELETE_ACCOUNT" | "EDIT_TAGS" | "SETTINGS" | "PAYMENT_HISTORY" | "CANCEL_SUBSCRIPTION" | "CREATE_FOLDER" | "SHARE" | "EDIT_LAYERS" | "EDIT_FILE_INFO" | "RENAME" | "CHANGE_PASSWORD" | "MOVE_FILES" | "EXPORT_CSV" | null;

interface ModalState {
  modalType: ModalType;
  modalProps: any;
}

export interface ModalContextProps {
  modalState: ModalState;
  openModal: (modalType: ModalType, modalProps?: any) => void;
  closeModal: () => void;
  openPrompt: (message: string , options?: {title?:string, type?:string, placeholder?:string ,defaultValue?:string}) => Promise<string | null>;
  openConfirm: (message: string, options?: { title: string }) => Promise<boolean>;
  openUploadConfirm: (message: string, options?: { title: string }) => Promise<UploadConfirmType>;
  openUploadPrompt: (defaultValues: UploadOptions, has3D?: boolean) => Promise<UploadOptions | null>;
}

export const ModalContext = createContext<ModalContextProps | undefined>({
  modalState: { modalType: null, modalProps: {} },
  openModal: () => {},
  closeModal: () => {},
  openPrompt: async () => "",
  openConfirm: async () => false,
  openUploadConfirm: async () => 'CANCEL',
  openUploadPrompt: async () => null,
});

const ModalProvider = ({ children , onProviderChange }: { children: ReactNode , onProviderChange?: (context: ModalContextProps)=> void }) => {
  const [modalState, setModalState] = useState<ModalState>({ modalType: null, modalProps: {} });
  const {PromptComponent, openPrompt} = usePrompt()
  const {openConfirm , ConfirmComponent} = useConfirm();
  const { openUploadConfirm, UploadConfirmComponent } = useUploadConfirm();
  const {openUploadPrompt , UploadPromptComponent} = useUploadPrompt();

  const openModal = useCallback((modalType: ModalType, modalProps = {}) => {
    setModalState((prev) => {return { modalType, modalProps : {...prev.modalProps , ...modalProps} }});
  }, []);

  const closeModal = useCallback(() => {
    setModalState({ modalType: null, modalProps: {} });
  }, []);

  const context = useMemo(() => {
    return { modalState, openModal, closeModal , openPrompt , openConfirm , openUploadConfirm, openUploadPrompt };
  }, [modalState, openModal, closeModal, openPrompt, openConfirm, openUploadConfirm, openUploadPrompt]);

  //to update the parent provider
  useEffect(() => {
    if(onProviderChange){
      onProviderChange(context);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <ModalContext.Provider value={context}>
      {children}
      <PromptComponent />
      <ConfirmComponent />
      <UploadConfirmComponent/>
      <UploadPromptComponent />
      <ModalRoot />
    </ModalContext.Provider>
  );
};

export default ModalProvider;