import { FC, useCallback, useMemo } from "react";
import { useUser } from "../provider/UserProvider";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";
import { useModal } from "../provider/useModal";
import SyncManager from "./SyncManager";
import { Button } from "@nextui-org/react";

const SettingsView: FC = () => {
  const { user , api } = useUser();
  const navigate = useNavigate();
  const subscribed = useMemo(() => user?.meta?.plan && user?.meta?.plan !== "free", [user?.meta?.plan]);
  const { openModal } = useModal();
  
  const goToPlans = useCallback(() => {
    navigate("../pricing");
  }, [navigate]);
  
  const handleSubscriptionClick = useCallback(async () => {
    if(!api) return;
    if(subscribed){
      const {error} = await api.manageSubscription();
      if(error) {
        toast.error("Error managing subscription");
      }
    } else {
      goToPlans();
    }
  }, [api, goToPlans, subscribed]);
  return (
    <div className="w-full h-full overflow-auto bg-white rounded-2xl">
      <div>
        <div className="md:px-8 p-5">
          <h1 className="text-2xl font-semibold">Settings</h1>
        </div>
        <div className="border-t md:p-8 p-5">
          {/* Basics Section */}
          <div className="mb-12 md:text-[16px] text-sm">
            <h2 className="text-xl font-medium mb-4">Basics</h2>
            <div className="md:hidden border-t"></div>

            {/* Username */}
            <div className="flex items-center py-4 border-b">
              <div className="w-1/4 md:ml-8">
                <p className="text-gray-700">Username</p>
              </div>
              <div className="flex-grow text-right md:text-left">
                <span className="text-gray-900">{user?.user || "Jewel Company"}</span>
              </div>
              <div>
               {/* <Button
                  varient="light"
                  size="sm"
                  name="Edit"
                  className="text-blue-600 underline"
                  fullWidth={false}
                /> */}
              </div>
            </div>

            {/* Photo */}
            {/* <div className="flex items-center py-4 border-b">
              <div className="w-1/4 md:ml-8">
                <p className="text-gray-700">Photo</p>
              </div>
              <div className="flex-grow flex justify-end md:justify-normal items-center">
                <div className="w-10 h-10 rounded-full overflow-hidden">
                  <img
                    src={`https://ui-avatars.com/api/?name=${encodeURIComponent(user?.user || 'User')}&background=random`}
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
                <div className="flex items-center md:gap-4 gap-1">
                <Button
                  varient="light"
                  size="sm"
                  name="Change"
                  className="text-blue-600 underline"
                  fullWidth={false}
                />
                <Button
                  varient="light"
                  size="sm"
                  name="Delete"
                  className="text-blue-600 underline"
                  fullWidth={false}
                />
                </div>
            </div> */}

            {/* Personal email */}
            <div className="flex items-center py-4 md:border-b">
              <div className="md:w-1/4 md:ml-8">
                <p className="text-gray-700">Personal email</p>
              </div>
              <div className="flex-grow text-right md:text-left">
                <span className="text-gray-900">{user?.email}</span>
              </div>
            </div>
          </div>

          {/* Your plan Section */}
          <div className="mb-12">
            <h2 className="text-xl font-medium mb-4">Your plan</h2>
            <div className="md:hidden border-t"></div>

            {/* Premium */}
            <div className="flex justify-between items-center py-4 md:border-b">
              <div className="md:ml-8 flex items-center">
                <div className="md:hidden flex gap-3 mr-1"><img src="/logo-sm.svg" alt="" /><span className="text-[#373737] font-semibold">iJewel Drive</span></div>
                <p className="text-gray-700 font-medium">{user?.meta?.plan ?? "Free"}</p>
              </div>
              <div className="flex items-center gap-4">
                <Button
                    // varient="light"
                    // size="sm"
                    color="primary"
                    onClick={goToPlans}
                    fullWidth={false}
                >
                  Upgrade
                </Button>
                {/* <Button
                    varient="light"
                    size="sm"
                    name="Payment history"
                    className="text-blue-600 underline hidden md:block"
                    fullWidth={false}
                    onClick={() => openModal("PAYMENT_HISTORY")}
                /> */}
                <Button
                    // varient="light"
                    // size="sm"
                    color="primary"
                    onClick={handleSubscriptionClick}
                    // className="text-blue-600 underline hidden md:block"
                    fullWidth={false}
                >
                  {subscribed ? "Manage subscription" : "See Plans"}
                </Button>
              </div>
            </div>
          </div>

          <div className="mb-12">
            <h2 className="text-xl font-medium mb-4">Data Sync</h2>
            <div className="md:hidden border-t"></div>
              <SyncManager />
          </div>

          {/* Security Section */}
          {/* <div className="mb-12">
            <h2 className="text-xl font-medium mb-4">Security</h2>
            <div className="md:hidden border-t"></div>

            <div className="flex justify-between items-center py-4 md:border-b">
              <div className="px-3 py-1 md:ml-8 md:p-0">
                <p className="text-gray-700">Password</p>
                <p className="text-gray-500 text-sm">Set a unique password to protect your account.</p>
              </div>
              <div>
                <Button
                    varient="light"
                    size="sm"
                    name="Change password"
                    className="text-blue-600 underline"
                    fullWidth={false}
                    onClick={() => openModal("CHANGE_PASSWORD")}
                />
              </div>
            </div>
          </div> */}

          {/* Delete account Section */}
          {/* <div className="mb-12">
            <h2 className="text-xl font-medium mb-4">Delete account</h2>
            <div className="md:hidden border-t"></div>

            <div className="flex justify-between items-center py-4 md:border-b">
              <div className="px-3 py-1 md:ml-8 md:p-0">
                <p className="text-gray-700">Delete iJewel Drive</p>
                <p className="text-gray-500 text-sm">If you delete your account, your data will be gone forever.</p>
              </div>
              <div>
                <Button
                    varient="light"
                    size="sm"
                    name="Delete account"
                    className="text-red-600 underline"
                    fullWidth={false}
                    onClick={() => openModal("DELETE_ACCOUNT")}
                />
              </div>
            </div>
          </div> */}
        </div>

      </div>
    </div>
  );
};

export default SettingsView;