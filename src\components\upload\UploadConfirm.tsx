import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>
  } from "@nextui-org/react";
  import React, { useMemo, useState } from "react";
  import ReplaceIcon from "../icons/Replace"
  import KeepBothIcon from "../icons/KeepBoth"

  export type UploadConfirmType = 'CANCEL' | 'KEEP_BOTH' | 'REPLACE' | 'SKIP';
  
  interface IUploadConfirm {
    message: string;
    onConfirm: () => void;
    onCancel: () => void;
    onKeepBoth: ()=> void;
    options: { title: string };
  }
  
  const UploadConfirm: React.FC<IUploadConfirm> = ({
    message,
    onConfirm,
    onCancel,
    onKeepBoth,
    options,
  }) => {
    return (
      <Modal
        isOpen={true}
        placement="center"
        backdrop="blur"
        closeButton={"x"}
        onClose={onCancel}
        shadow="lg"
        className="overflow-hidden"
      >
        <ModalContent>
          {() => (
            <>
              <ModalHeader className="flex flex-col gap-1 text-small text-clip break-words pb-unit-sm text-[20px]">
                {options.title || "Confirmation"}
              </ModalHeader>
              <ModalBody className="overflow-hidden h-unit-3xl pt-0 my-[10px]">
                <p className="text-[#373737]">{message}</p>
                <div className="flex flex-col gap-[10px] mt-4">
                  <Button
                    className="w-full h-24 justify-start font-semibold text-secondary hover:text-white hover:bg-secondary"
                    color="secondary"
                    variant="bordered"
                    onClick={onConfirm}
                    size="lg"
                    startContent={<div className="!w-8"><ReplaceIcon className="w-full"/></div>}
                  >
                    <div className="w-full flex flex-col items-start overflow-hidden gap-1">
                      <p>Replace existing file</p>
                      <p className="text-sm font-normal whitespace-normal text-start">
                        Replace the file in the destination folder by the file you are uploading.
                      </p>
                    </div>                    
                  </Button>
                  <Button
                    className="w-full h-24 justify-start font-semibold text-secondary hover:text-white hover:bg-secondary"
                    color="secondary"
                    variant="bordered"
                    onClick={onKeepBoth}
                    size="lg"
                    startContent={<div className="!w-8"><KeepBothIcon className="w-full"/></div>}
                  >
                    <div className="w-full flex flex-col items-start overflow-hidden gap-1">
                      <p>Keep both files</p>
                      <p className="text-sm font-normal whitespace-normal text-start">
                        Add a suffix to the name and upload file
                      </p>
                    </div>  
                  </Button>
                </div>
              </ModalBody>
              <ModalFooter className="pt-0">
                <Button
                  className=""
                  onClick={onCancel}
                  size="lg"
                  // color="warning"
                >
                  Skip
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    );
  };
  
  interface UseConfirmReturn {
    openUploadConfirm: (message: string, options?: { title: string }) => Promise<UploadConfirmType>;
    UploadConfirmComponent: React.FC;
  }
  
  const useUploadConfirm = (): UseConfirmReturn => {
    const [visible, setVisible] = useState(false);
    const [message, setMessage] = useState("");
    const [options, setOptions] = useState<{ title: string }>({ title: "" });
    const [resolveConfirm, setResolveConfirm] = useState<(value: UploadConfirmType) => void | null>();
  
    const openUploadConfirm = (msg: string, options?: { title: string }): Promise<UploadConfirmType> => {
      setMessage(msg);
      setOptions(options || { title: "" });
      setVisible(true);
      return new Promise<UploadConfirmType>((resolve) => {
        setResolveConfirm(() => resolve);
      });
    };
  
    const handleResult = (result: UploadConfirmType) => {
      setVisible(false);
      if (resolveConfirm) {
        resolveConfirm(result);
      }
    };
  
    const handleConfirm = () => {
      handleResult("REPLACE");
    };
  
    const handleCancel = () => {
      handleResult('SKIP');
    };

    const handleKeepBoth = () => {
        handleResult('KEEP_BOTH')
    }
  
    const UploadConfirmComponent: React.FC = useMemo(()=> () =>
      visible ? (
        <UploadConfirm
          message={message}
          options={options}
          onConfirm={handleConfirm}
          onCancel={handleCancel}
          onKeepBoth={handleKeepBoth}
        />
      ) : null, [visible, message, options]);
  
    return { openUploadConfirm, UploadConfirmComponent };
  };
  
  // eslint-disable-next-line react-refresh/only-export-components
  export { UploadConfirm , useUploadConfirm };
  