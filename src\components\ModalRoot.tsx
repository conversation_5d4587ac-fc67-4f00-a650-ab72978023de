// src/components/ModalRoot.tsx
import React from "react";
import PreviewModal from "./modals/PreviewModal";
import EditTagsModal from "./modals/EditTagsModal";
import SettingsModal from "./modals/SettingsModal";
import ShareModal from "./modals/ShareModal";
import { useModal } from "../provider/useModal";
import EditLayersModal from "./modals/EditLayersModal";
import EditFileInfoModal from "./modals/EditFileInfoModal";
import CreateFolderModal from "./modals/CreateFolderModal.tsx";
import RenameModal from "./modals/RenameModal.tsx";
import DeleteModal from "./modals/DeleteModal.tsx";
import TutorialModal from "./modals/TutorialModal.tsx";
import DeleteAccountModal from "./modals/DeleteAccountModal.tsx";
import ExportCsvModal from "./modals/ExportCsvModal.tsx";
import PaymentHistoryModal from "./modals/PaymentHistoryModal.tsx";
import CancelSubscriptionModal from "./modals/CancelSubscriptionModal.tsx";
import ChangePasswordModal from "./modals/ChangePasswordModal.tsx";
import MoveFilesModal from "./modals/MoveFilesModal.tsx";
// Import other modals as needed

const ModalRoot = () => {
  const { modalState, closeModal } = useModal();

  const renderModal = () => {
    switch (modalState.modalType) {
      case "CREATE_FOLDER":
        return <CreateFolderModal {...modalState.modalProps} onClose={closeModal}/>;
      case "RENAME":
        return <RenameModal {...modalState.modalProps} onClose={closeModal}/>;
      case "DELETE":
        return <DeleteModal {...modalState.modalProps} onClose={closeModal}/>;
      case "MOVE_FILES":
        return <MoveFilesModal {...modalState.modalProps} onClose={closeModal}/>;
      case "EXPORT_CSV":
        return <ExportCsvModal {...modalState.modalProps} onClose={closeModal}/>;
      case "PREVIEW":
        return <PreviewModal {...modalState.modalProps} onClose={closeModal} />;
      case "EDIT_TAGS":
        return <EditTagsModal {...modalState.modalProps} onClose={closeModal} />;
      case "SETTINGS":
        return <SettingsModal {...modalState.modalProps} onClose={closeModal} />;
      case "SHARE":
        return <ShareModal {...modalState.modalProps} onClose={closeModal} />;
      case "EDIT_LAYERS":
        return <EditLayersModal {...modalState.modalProps} onClose={closeModal} />;
      case "EDIT_FILE_INFO":
        return <EditFileInfoModal {...modalState.modalProps} onClose={closeModal} />;
      case "DELETE_ACCOUNT":
          return <DeleteAccountModal onClose={closeModal} />;
      case "PAYMENT_HISTORY":
          return <PaymentHistoryModal onClose={closeModal} />;
      case "CANCEL_SUBSCRIPTION":
          return <CancelSubscriptionModal onClose={closeModal} />;
      case "CHANGE_PASSWORD":
          return <ChangePasswordModal onClose={closeModal} />;
      case "TUTORIAL":
        return <TutorialModal {...modalState.modalProps} onClose={closeModal}/>;
      default:
        return null;
    }
  }

  return (renderModal());
};

export default ModalRoot;
