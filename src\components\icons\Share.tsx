import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <mask id="mask0_135_266" style={{maskType: "alpha",}} maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
            <rect width="20" height="20" fill="#D9D9D9"/>
        </mask>
        <g mask="url(#mask0_135_266)">
            <path d="M14.1665 18.3334C13.4721 18.3334 12.8818 18.0903 12.3957 17.6042C11.9096 17.1181 11.6665 16.5278 11.6665 15.8334C11.6665 15.75 11.6873 15.5556 11.729 15.25L5.87484 11.8334C5.65262 12.0417 5.39567 12.2049 5.104 12.3229C4.81234 12.441 4.49984 12.5 4.1665 12.5C3.47206 12.5 2.88178 12.257 2.39567 11.7709C1.90956 11.2847 1.6665 10.6945 1.6665 10C1.6665 9.30558 1.90956 8.7153 2.39567 8.22919C2.88178 7.74308 3.47206 7.50002 4.1665 7.50002C4.49984 7.50002 4.81234 7.55905 5.104 7.6771C5.39567 7.79516 5.65262 7.95835 5.87484 8.16669L11.729 4.75002C11.7012 4.6528 11.6839 4.55905 11.6769 4.46877C11.67 4.37849 11.6665 4.2778 11.6665 4.16669C11.6665 3.47224 11.9096 2.88196 12.3957 2.39585C12.8818 1.90974 13.4721 1.66669 14.1665 1.66669C14.8609 1.66669 15.4512 1.90974 15.9373 2.39585C16.4234 2.88196 16.6665 3.47224 16.6665 4.16669C16.6665 4.86113 16.4234 5.45141 15.9373 5.93752C15.4512 6.42363 14.8609 6.66669 14.1665 6.66669C13.8332 6.66669 13.5207 6.60766 13.229 6.4896C12.9373 6.37155 12.6804 6.20835 12.4582 6.00002L6.604 9.41669C6.63178 9.51391 6.64914 9.60766 6.65609 9.69794C6.66303 9.78821 6.6665 9.88891 6.6665 10C6.6665 10.1111 6.66303 10.2118 6.65609 10.3021C6.64914 10.3924 6.63178 10.4861 6.604 10.5834L12.4582 14C12.6804 13.7917 12.9373 13.6285 13.229 13.5104C13.5207 13.3924 13.8332 13.3334 14.1665 13.3334C14.8609 13.3334 15.4512 13.5764 15.9373 14.0625C16.4234 14.5486 16.6665 15.1389 16.6665 15.8334C16.6665 16.5278 16.4234 17.1181 15.9373 17.6042C15.4512 18.0903 14.8609 18.3334 14.1665 18.3334ZM14.1665 16.6667C14.4026 16.6667 14.6005 16.5868 14.7603 16.4271C14.92 16.2674 14.9998 16.0695 14.9998 15.8334C14.9998 15.5972 14.92 15.3993 14.7603 15.2396C14.6005 15.0799 14.4026 15 14.1665 15C13.9304 15 13.7325 15.0799 13.5728 15.2396C13.413 15.3993 13.3332 15.5972 13.3332 15.8334C13.3332 16.0695 13.413 16.2674 13.5728 16.4271C13.7325 16.5868 13.9304 16.6667 14.1665 16.6667ZM4.1665 10.8334C4.40261 10.8334 4.60053 10.7535 4.76025 10.5938C4.91998 10.434 4.99984 10.2361 4.99984 10C4.99984 9.76391 4.91998 9.56599 4.76025 9.40627C4.60053 9.24655 4.40261 9.16669 4.1665 9.16669C3.93039 9.16669 3.73248 9.24655 3.57275 9.40627C3.41303 9.56599 3.33317 9.76391 3.33317 10C3.33317 10.2361 3.41303 10.434 3.57275 10.5938C3.73248 10.7535 3.93039 10.8334 4.1665 10.8334ZM14.1665 5.00002C14.4026 5.00002 14.6005 4.92016 14.7603 4.76044C14.92 4.60071 14.9998 4.4028 14.9998 4.16669C14.9998 3.93058 14.92 3.73266 14.7603 3.57294C14.6005 3.41321 14.4026 3.33335 14.1665 3.33335C13.9304 3.33335 13.7325 3.41321 13.5728 3.57294C13.413 3.73266 13.3332 3.93058 13.3332 4.16669C13.3332 4.4028 13.413 4.60071 13.5728 4.76044C13.7325 4.92016 13.9304 5.00002 14.1665 5.00002Z" fill="#373737"/>
        </g>
    </svg>
)
export default SvgComponent

