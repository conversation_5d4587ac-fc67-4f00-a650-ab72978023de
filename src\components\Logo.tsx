import { FC } from "react";
import { useUser } from "../provider/UserProvider";

interface LogoProps {
  className?: string;
}

const LOGO = 'https://drive.ijewel3d.com/logo.svg'

const Logo: FC<LogoProps>= (props) => {
  const {config }= useUser()
  const canCustomize = config?.plan?.["allowed-features"]?.includes("custom-logo") && config?.logo;

  return (
    <img className={`h-8 w-auto ${props.className}`} src={canCustomize ? config.logo : LOGO} alt="logo" />
  );
};

export { Logo };
