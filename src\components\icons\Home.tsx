import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <mask id="mask0_198_619" style={{maskType: "alpha",}} maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
            <rect width="20" height="20" fill="#D9D9D9"/>
        </mask>
        <g mask="url(#mask0_198_619)">
            <path d="M4 17V7.5L10 3L16 7.5V17H4ZM5.5 15.5H14.5V8.25L10 4.875L5.5 8.25V15.5Z" fill="currentColor"/>
            <path d="M13 14.5H7V13H13V14.5Z" fill="currentColor"/>
        </g>
    </svg>
)
export default SvgComponent
