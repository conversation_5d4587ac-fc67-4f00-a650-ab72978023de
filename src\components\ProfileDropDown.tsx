import { FC } from "react";
import Settings from "./icons/Settings.tsx";
import Support from "./icons/Support.tsx";
import Terms from "./icons/Terms.tsx";
import Logout from "./icons/Logout.tsx";
import { useUser } from "../provider/UserProvider.ts";
import { useLocation, useParams } from "react-router-dom";
import { useModal } from "../provider/useModal.ts";
import BaseLink  from "./BaseLink.tsx";
import { useBrowser } from "../provider/BrowserProvider";
import { ViewTypes } from "../types/views";

import { DropdownMenu, DropdownItem } from "@nextui-org/react";
import { useBaseNavigate } from "../hooks/UseBaseNavigate.tsx";

const ProfileDropdown: FC = () => {
    const { logOut, user, isLogin } = useUser();
    const navigate = useBaseNavigate();
    const { pathname } = useLocation();
    const { openModal } = useModal();
    const { setCurrentView } = useBrowser();
    const { basename } = useParams();

    const goToLogin = () => {
        navigate("/login", {
            state: {
                redirect: pathname,
            },
        });
    };

    const handleLogout = () =>{
        logOut();
        navigate(`/login`);
    }

    return (
        <DropdownMenu 
            aria-label="Profile options" 
            className="w-[250px] bg-white rounded-xl"
        >
            {/* Header */}
            <DropdownItem key="header" className="h-[40px] cursor-default">
                <div className="flex justify-between items-center">
                    <h2 className="text-base font-semibold text-gray-900 capitalize">{user?.meta?.plan || "free"} <span className="lowercase">plan</span></h2>
                    <BaseLink 
                        to="/pricing" 
                        className="text-blue-600 hover:text-blue-700 text-sm underline"
                    >
                        Plans
                    </BaseLink>
                </div>
            </DropdownItem>

            {/* User Info */}
            <DropdownItem key="user_info" className="h-auto cursor-default">
                <BaseLink to="/folders">
                    <div className="flex items-center gap-3">
                        <div className="w-8 h-8 rounded-full bg-gray-200 overflow-hidden">
                            <img 
                                src={`https://ui-avatars.com/api/?name=${encodeURIComponent(user?.user || 'User')}&background=random`}
                                alt="Profile" 
                                className="w-full h-full object-cover" 
                            />
                        </div>
                        <div>
                            <div className="font-semibold text-sm">{user?.user || 'No username'}</div>
                            <div className="text-xs text-gray-500">{user?.email || 'No email'}</div>
                        </div>
                    </div>
                </BaseLink>
            </DropdownItem>

            {/* Menu Items */}
            {(user as any)?.role?.endsWith("admin") && (
                <DropdownItem
                    key="settings"
                    startContent={<Settings className="w-5 h-5 text-gray-700" />}
                    className="px-3 h-8 data-[hover=true]:bg-gray-50"
                    onClick={() => openModal("SETTINGS")}
                >
                    <span className="text-gray-700 text-sm">Settings</span>
                </DropdownItem>
            )}

            {/* <DropdownItem
                key="plans"
                startContent={<Settings className="w-5 h-5 text-gray-700" />}
                className="px-3 h-8 data-[hover=true]:bg-gray-50"
                onClick={() => navigate("../pricing")}
            >
                <span className="text-gray-700 text-sm">{(user.meta?.plan && user.meta?.plan !== "free" ) ? "Manage Subscription" : "Plans"}</span>
            </DropdownItem> */}

            <DropdownItem
                key="user_settings"
                startContent={<Settings className="w-5 h-5 text-gray-700" />}
                className="px-3 h-8 data-[hover=true]:bg-gray-50"
                onClick={() => {
                    setCurrentView(ViewTypes.SETTINGS);
                }}
            >
                <span className="text-gray-700 text-sm">User Settings</span>
            </DropdownItem>

            <DropdownItem
                key="support"
                startContent={<Support className="w-5 h-5 text-gray-700" />}
                className="px-3 h-8 data-[hover=true]:bg-gray-50"
            >
                <a href="mailto:<EMAIL>">
                    <span className="text-gray-700 text-sm">Support</span>
                </a>
            </DropdownItem>

            <DropdownItem
                key="terms"
                startContent={<Terms className="w-5 h-5 text-gray-700" />}
                className="px-3 h-8 data-[hover=true]:bg-gray-50"
                onClick={() => navigate("/terms")}
            >
                <span className="text-gray-700 text-sm">Terms and Privacy Policy</span>
            </DropdownItem>

            <DropdownItem
                key="logout"
                startContent={<Logout className="w-5 h-5 text-gray-700" />}
                className="px-3 h-8 data-[hover=true]:bg-gray-50"
                onClick={isLogin ? handleLogout : goToLogin}
            >
                <span className="text-gray-700 text-sm">Logout</span>
            </DropdownItem>
        </DropdownMenu>
    );
};

export { ProfileDropdown };