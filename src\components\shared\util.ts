import { CustomFileData, FileAPI } from "../../api/api";

export const validateEmail = (email: string) => {
    const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return re.test(String(email).toLowerCase());
};

// Utility function to load an external script
export const loadScript = (src: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    const existingScript = document.querySelector(`script[src="${src}"]`);
    if (existingScript) {
      resolve();
      return;
    }

    const script = document.createElement("script");
    script.src = src;
    script.async = true;
    script.onload = () => resolve();
    script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
    document.body.appendChild(script);
  });
};


export const joinPaths = (basePath: string, path: string): string => {
  // Join paths with a slash, handles cases where one or both might be empty
  const joined = path.startsWith(basePath) ? path : `${basePath}/${path}`;

  // Replace multiple consecutive slashes with a single slash
  let normalizedPath = joined.replace(/\/+/g, '/');

  // Ensure the path starts with a slash
  if (!normalizedPath.startsWith('/')) {
    normalizedPath = '/' + normalizedPath;
  }

  // Ensure the path ends with a slash, unless it's just the root path "/"
  if (normalizedPath !== '/' && !normalizedPath.endsWith('/')) {
    normalizedPath = normalizedPath + '/';
  }

  return normalizedPath;
}

export const dateToHumanize = (date: Date | string) => {
  const now = new Date();
  const then = new Date(date);
  const seconds = Math.floor((now.getTime() - then.getTime()) / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  const weeks = Math.floor(days / 7);
  const months = Math.floor(days / 30);
  const years = Math.floor(days / 365);

  if (years > 0) {
    return `${years} year${years > 1 ? "s" : ""} ago`;
  } else if (months > 0) {
    return `${months} month${months > 1 ? "s" : ""} ago`;
  } else if (weeks > 0) {
    return `${weeks} week${weeks > 1 ? "s" : ""} ago`;
  } else if (days > 0) {
    return `${days} day${days > 1 ? "s" : ""} ago`;
  } else if (hours > 0) {
    return `${hours} hour${hours > 1 ? "s" : ""} ago`;
  } else if (minutes > 0) {
    return `${minutes} minute${minutes > 1 ? "s" : ""} ago`;
  } else if (seconds > 0) {
    return `${seconds} second${seconds > 1 ? "s" : ""} ago`;
  } else {
    return "Just now";
  }
};


export const getNewUserName = (profile: any = {}) => {
  const { full_name, username } = profile ?? {};
  const name = full_name === "New user" ? username : full_name;
  return name;
};

export function debounce<T extends (...args: any[]) => void>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout>;
  return (...args: Parameters<T>): void => {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func(...args);
    }, wait);
  };
}

export const isEncryptProject = (project: any) => project.tags.includes("_encrypted");

export const generateUsername = (name: string) => {
  if (name === "") return "";

  // Remove non-alphabetic characters, convert to lowercase, and trim any whitespace
  const cleanName = name
    .toLowerCase()
    .replace(/[^a-z\s]/g, "")
    .trim();

  // Split the name into parts and join them with underscores
  const parts = cleanName.split(/\s+/);
  let username = parts.join("_");

  // Append a random number if username is too short
  if (username.length < 3) {
    username += Math.floor(Math.random() * 100);
  }

  // Limit username to 15 characters
  username = username.slice(0, 15);

  // if end has _ then remove it, then only remove end _
  if (username.endsWith("_")) username = username.slice(0, -1);

  return username;
};

export const sanitizeFileName = (fileName: string): string => {
  if (!fileName || fileName.trim() === "") {
    return "Untitled";
  }

  // Remove or replace problematic characters for URLs and file systems
  let sanitized = fileName
    .trim()
    // Replace spaces with underscores
    .replace(/\s+/g, "_")
    // Remove or replace special characters that can break URLs
    .replace(/[<>:"/\\|?*]/g, "")
    // Replace other problematic characters with underscores
    .replace(/[#%&{}+^~`[\]@!$'()*,;=]/g, "_")
    // Remove multiple consecutive underscores
    .replace(/_+/g, "_")
    // Remove leading/trailing underscores and dots
    .replace(/^[._]+|[._]+$/g, "");

  // Ensure the filename is not empty after sanitization
  if (sanitized === "") {
    sanitized = "Untitled";
  }

  // Limit length to prevent issues (keeping reasonable length for display)
  if (sanitized.length > 100) {
    sanitized = sanitized.substring(0, 100);
  }

  // Remove trailing underscore if it exists
  if (sanitized.endsWith("_")) {
    sanitized = sanitized.slice(0, -1);
  }

  return sanitized;
};

export const convertHtmlToPlainText = (html: string) => {
  // Create a new div element
  const tempDivElement = document.createElement("div");

  // Set the HTML content with the given value
  tempDivElement.innerHTML = html;

  // Retrieve the text property of the element
  return tempDivElement.textContent || tempDivElement.innerText || "";
};

export const getEllipsisText = (text: string, charCount: number) => {
  return text.length > charCount ? text.slice(0, charCount).trim() + "..." : text;
};
export const userHasPaidPlan = (profile: any) => {
  let isPaid = false;

  if (profile?.plan !== "free") {
    isPaid = true;
  }
  return isPaid;
};

export const isUnlistedTypeProject = (project: any) => {
  return project.tags.includes("_unlisted");
};

export const getBgBasedOnPlan = (profile: any, isClass = true) => {
  if (!profile) return "";

  let bg = "";
  const plan = profile.plan;
  if (plan === "free") bg = "linear-gradient(99.49deg, #B3B3B3 6.23%, #A6A6A6 88.32%)";
  else if (plan === "premium") bg = "#6E72F2";
  else if (plan == "business") bg = "linear-gradient(229.19deg, #FCC253 -33.33%, #FFE0A4 18.24%, #B5821F 69.81%, #FFD788 121.38%)";
  else bg = "";

  return isClass ? `bg-[${bg}]`.replaceAll(" ", "_") : bg;
};

export const updateFileName = (data: { file: File; prefix?: string | number; suffix?: string | number; delimiter?: string }) => {
  const { file, prefix, suffix, delimiter = "_" } = data;
  const fileNameParts = file.name.split(".");
  const fileName = fileNameParts[0];
  const fileExtension = fileNameParts[1];

  let newFileName = fileName;
  if (prefix) newFileName = prefix + delimiter + newFileName;
  if (suffix) newFileName = newFileName + delimiter + suffix;
  return new File([file], `${newFileName}.${fileExtension}`, {
    type: file.type,
  });
};
export const sanitizeCustomAsset = (asset: CustomFileData) => {
  // const { asset_url, id, isCustom = true, name, size, poster_url, meta: asset_data } = asset;
  const asset_url = FileAPI.getInstance().getDownloadUrl(asset) || "";
  const icon = FileAPI.getInstance().getDownloadUrl(asset.thumb) || "";
  return {
    id: asset.id,
    path: asset_url,
    size: asset.meta?.size || 0,
    name: `${asset.name}`,
    icon: icon,
    isCustom : true,
    meta: { asset_data : asset.meta, name : asset.name },
  };
};


export const getProjectBasePath = (version: string) => {
  switch (version) {
    case "v1":
    case "v2":
    case "v3":
      return "https://assets.ijewel.design/" + version + "/";
    default:
      return "https://packs.ijewel3d.com/files/";
  }
};

export const isModelFile = (file: File) => {
  const modelExtensions = ["glb", "gltf", "obj", "fbx", "3ds", "stl","3dm"];
  return modelExtensions.includes(file.name.split(".").pop()?.toLowerCase() || "");
}

export const getShareLink = (file: CustomFileData , baseName : string) => {
  const link = `${window.location.origin + "/" + baseName  + (file?.isDir ? "/folders/" : "/files/") + file?.id + (file?.isDir ? "" : "/view")} `;
  return link;
}

export const handleShare = (file : CustomFileData , baseName : string) => {
  const url = getShareLink(file, baseName);

  navigator.clipboard.writeText(url);

  //if phone then use navigator.share
  if (navigator.share) {
    navigator.share({
      title: file.name,
      url: url,
      text: "Check out this model on iJewel.design \n" + file.config?.description,
    });
  }
}

export const getIdFromPath = (path: string): string => {
  const segments = path.split('/').filter(Boolean);
  return segments.pop()!;
};

export const is3dFile = (
  file: CustomFileData | File | (CustomFileData | File)[]
): boolean => {
  const validExtensions = ['obj', 'fbx', 'gltf', 'glb', 'stl', '3dm', '3ds'];

  const getExtension = (f: CustomFileData | File): string => {
    const fileName = f instanceof File ? f.name : f.file;
    return fileName ? fileName.split('.').pop()?.toLowerCase() || "" : "";
  };

  const checkFile = (f: CustomFileData | File) =>
    validExtensions.includes(getExtension(f));

  return Array.isArray(file) ? file.some(checkFile) : checkFile(file);
}

export const has3dConfig = (file: CustomFileData) => {
  return file.config && (file.config.sceneConfig || file.config.materialConfig || file.config.glbUpdated || file.config.isPlayground)
}