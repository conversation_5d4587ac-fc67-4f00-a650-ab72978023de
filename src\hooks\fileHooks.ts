import { <PERSON><PERSON><PERSON>y, Chonky<PERSON>ileActionData, ChonkyActions, FileAction, CustomVisibilityState, ChonkyIconName } from "chonky";
import { useMemo, useCallback, useEffect, useRef, useState, useContext, createContext, createElement } from "react";
import { CustomFileData } from "../api/api";
import { useUser } from "../provider/UserProvider";
import { useBrowser } from "../provider/BrowserProvider";
import { useDebounce } from "use-debounce";
import FuzzySearch from "fuzzy-search";
import toast from "react-hot-toast";
import { useNavigate, useParams } from "react-router-dom";
import { fileMetaData, QueueItem } from "../components/shared/types";
import { UploadOptions } from "../components/UploadPrompt";
import { useModal } from "../provider/useModal";
import { is3dFile, joinPaths } from "../components/shared/util";
import { CustomAssetsBaseFolderTag, FILE_CONFIG_3D_PROPERTIES, MAX_RECENT_FILES, RECENT_FILES_KEY } from "../components/shared/variables";
import { ModalContextProps } from "../provider/ModalProvider";
import { UploadConfirmType } from "../components/upload/UploadConfirm";
import { getDuplicateName } from "../utils/getDuplicateName";



export interface CustomFileMap {
  [fileId: string]: CustomFileData;
}

let LIMIT = 1000; // default limit for file listing

if(typeof window !== "undefined") {
  //check url query params for limit
  const limitParam = Number(new URLSearchParams(window.location.search).get('limit'))
  LIMIT = limitParam ? limitParam : 1000; 
}

interface CustomFileMapContextProps {
  fileMap: CustomFileMap;
  currentPath: string;
  setCurrentPath: (path: string) => void;
  deleteFiles: (files: CustomFileData[], permanent: boolean) => Promise<void>;
  duplicateFile: (file: CustomFileData, newName: string) => Promise<void>;
  moveFiles: (files: CustomFileData[], newParentId: string) => Promise<void>;
  createFolder: (folderName: string) => Promise<void>;
  uploadFiles: (files: { file: File; path: string }[], options: UploadOptions) => Promise<void>;
  downloadFiles: (files: CustomFileData[]) => Promise<void>;
  previewFile: (file: CustomFileData) => void;
  searchFiles: () => Promise<void>;
  getFile: (fileId: string) => Promise<CustomFileData | null | undefined>;
  renameFile: (file: CustomFileData, newName: string) => Promise<void>;
  toggleFileLock: (file: CustomFileData) => Promise<void>;
  updateFilePermissions: (file: CustomFileData, permissions: Partial<fileMetaData>) => Promise<void>;
  CustomDownloadAction: FileAction;
  CustomViewAction: FileAction;
  CustomEditAction: FileAction;
  CustomEditTagsAction: FileAction;
  CustomRenameAction: FileAction;
  CustomShareAction: FileAction;
  CustomOpenFolderAction: FileAction;
  customLockFileAction: FileAction;
  customUnlockFileAction: FileAction;
  customDeleteAction: FileAction;
  customOverrideTagsAction: FileAction;
  CustomUpdateThumbnailAction: FileAction;
  customDuplicateAction: FileAction;
  customEditFileInfoAction: FileAction;
  CustomOpenInPlaygroundAction: FileAction;
  CustomCopyDownloadLinkAction: FileAction;
  CustomCopyFileIdAction: FileAction;
  CustomCreateFolder:FileAction;
  CustomPermanentDeleteAction: FileAction;
  dragging: boolean;
  updateTags: (file: CustomFileData | CustomFileData[], tags: string, refresh?: boolean) => Promise<void>;
  updateLayers: (file: CustomFileData, tags: string) => Promise<void>;
  customModifyLayersAction: FileAction;
  customSetCategoryAction: FileAction;
  updateCategory: (files: CustomFileData[], categoryName: string) => Promise<void>;
  CustomMakeDefaultAction: FileAction;
  updateDefault: (file: CustomFileData) => Promise<void>;
  updatePremium: (files: CustomFileData[]) => Promise<void>;
  CustomMakeFreeAction: FileAction;
  CustomMakePremiumAction: FileAction;
  updateThumbnail: (files: CustomFileData[]) => Promise<void>;
  getRecentFiles: () => CustomFileData[];
  addToRecentFiles: (file: CustomFileData) => void;
  restoreFiles: (files: CustomFileData[]) => Promise<void>;
  customRestoreAction: FileAction;
  CustomFileInfoAction: FileAction;
  CustomEditDefaultSettingsAction: FileAction;
  clear3DConfig: (file: CustomFileData) => Promise<void>;
  customClear3dAction: FileAction;
  CustomExportCsvAction: FileAction;
  updateNote: (file: CustomFileData, note: string) => Promise<void>;
  updateDescription: (file: CustomFileData, config: any) => Promise<void>;
  refreshFileMap: (force?: boolean, reset?: boolean) => Promise<boolean | null>;
  setHasMore: (hasMore: boolean) => void;
  hasMore: boolean;
}

const CustomFileMapContext = createContext<CustomFileMapContextProps>({} as CustomFileMapContextProps);

export const useCustomFileMap = () => useContext(CustomFileMapContext);

export function CustomFileMapProvider({ children , modalContext }: { children: any , modalContext : ModalContextProps }) {
  const value = useSetupCustomFileMap(modalContext);
  return createElement(CustomFileMapContext.Provider, { value }, children);
}

export const useSetupCustomFileMap = (modalContext : ModalContextProps) => {
  const { fileMap, setFileMap } = useBrowser();
  const filemapRef = useRef<CustomFileMap>(fileMap);
  const { isLogin, api, canUpload , can, user , config , incrementModelsCount } = useUser();
  const {
    setPreview,
    updateQueue,
    searchString,
    setSearchString,
    setSearchResults,
    setLoading,
    getThumbnailGenerator,
    dragging,
    fileQueueRef,
    currentPath,
    setCurrentPath,
    selectedFiles,
    currentView,
  } = useBrowser();
  const lastSearchId = useRef(0);
  const [debouncedSearchString] = useDebounce(searchString, 300);
  const { folderId , basename } = useParams();
  const navigate = useNavigate();
  const { openConfirm } = modalContext || { openConfirm: async () => false };
  const { openUploadConfirm } = modalContext || { openUploadConfirm: async () => false };

  useEffect(() => {
    searchFiles();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedSearchString]);

  useEffect(() => {
    filemapRef.current = fileMap;
  }, [fileMap]);

  useEffect(() => {
    if (!isLogin) {
      return;
    }
    openFolder();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [folderId, isLogin]);

  const lastRefreshId = useRef(0);
  const offsetRef       = useRef(0); // for infinite scroll
  const [hasMore , setHasMore] = useState(true); // for infinite scroll
  const refreshFileMap = useCallback(
    async (force?: boolean, reset: boolean = false): Promise<boolean | null> => {
      if (!api || !isLogin || !currentPath) return null;

      let limit  : number;
      let offset : number;
  
      if (force) {                                // FULL refresh
        limit  = Math.max(offsetRef.current, LIMIT);
        offset = 0;
      } else if (reset) {                         // first page of new path
        limit  = Math.max(offsetRef.current, LIMIT);
        offset = 0;
      } else {                                    // ordinary “load next”
        limit  = LIMIT;
        offset = offsetRef.current;
      }

      if (reset) {
        offsetRef.current = 0;
        setHasMore(true);
      }

      setLoading(true);
      const refreshId = ++lastRefreshId.current;
  
      const { data: files, error } =
        currentView === "trash"
          ? await api.listDeletedFiles(limit, offset)
          : await api.listFiles(currentPath, limit, offset);
  
      if (refreshId !== lastRefreshId.current) return null;
      if (error) {
        toast.error("Failed to load files");
        setLoading(false);
        return false;
      }
  
      if (reset) {
        offsetRef.current = files.length - 1       // we just fetched 1st page
      } else if (!force) {
        offsetRef.current += files.length - 1       // fetched *next* page
      } /* if force is true keep offset as-is (same page-count) */
  
      setFileMap((prev: CustomFileMap) =>
        (force)
          ? files.reduce((acc, f) => ({ ...acc, [f.id]: f }), {})
          : files.reduce((acc, f) => ({ ...acc, [f.id]: f }), prev)
      );
  
      setLoading(false);
      console.log("refreshed")
      const hasMore = (files.length -1) === limit;
      setHasMore(hasMore);
      return hasMore;
    },
    [api, isLogin, currentPath, currentView, setFileMap, setLoading, setHasMore]
  );

  useEffect(() => {
    if (!isLogin || !api) return;
    refreshFileMap(false , true);
  }, [currentPath, isLogin, api, refreshFileMap]);

  const getFile = useCallback(
    async (fileId: string) => {
      if (!isLogin || !api) return undefined;
      let file: CustomFileData | null = fileMap[fileId] ?? null;
      if (!file) {
        const response = await api?.getFile(fileId);
        if (!response.data || response.error) {
          console.log(fileId + " not found");
          return null;
        }
        file = response.data;
        setFileMap((fileMap: CustomFileMap) => ({ ...fileMap, [file!.id]: file! }));
      }
      return file;
    },
    [isLogin, api, fileMap, setFileMap]
  );

  const getFileByName = useCallback(
    async (fileName: string, path: string): Promise<{ data: CustomFileData | null }> => {
      if (!isLogin || !api) return { data: null };
      // First, check in the fileMap
      const fileInMap = Object.values(fileMap).find((f) => f.name === fileName && f.path === path && !f.isDir);
      if (fileInMap) return { data: fileInMap };
      try {
        const { data: file } = await api.getFileByName(fileName, path);
        if (file) {
          setFileMap((fileMap: CustomFileMap) => ({ ...fileMap, [file.id]: file }));
          return { data: file };
        } else {
          return { data: null };
        }
      } catch (error) {
        console.error("Error checking file existence on backend:", error);
        return { data: null };
      }
    },
    [isLogin, api, fileMap, setFileMap]
  );

  const openFolder = useCallback(async () => {
    if (!isLogin || !api) return;
    if (folderId) {
      const folder = await getFile(folderId);
      if (folder) {
        setCurrentPath(folder.path + folder.id + "/");
      } else {
        toast.error("Folder not found" , {id : "folder-not-found"});
        setCurrentPath("/");
        navigate("../folders");
      }
    } else {
      setCurrentPath("/");
    }
  }, [folderId, getFile, isLogin, api, setCurrentPath, navigate]);

  const deleteFiles = useCallback(
    async (files: CustomFileData[], permanent: boolean) => {
      if(files.length > 1000){
        toast.error("Can't delete more than 1000 files at once");
        return;
      }
      if (!isLogin || !api) return;
      const toastId = "deleting";
      toast.loading("Deleting files...", { id: toastId });
      let successCount = 0;
      const errors: string[] = [];

      await Promise.all(
        files.map(async (file) => {
          try {
            // Permission check
            const permissionContext = { file };
            const canDelete = file.isDir
              ? can('delete-folder', undefined, permissionContext)
              : can('delete-file', undefined, permissionContext);

              //check if folder is empty or not 
              if (file.isDir){
                const childCount = await getFolderChildCount(file.id);
                if (childCount > 0 && !config?.features?.["delete-folder"]?.enabled && false) {
                  toast.error(`Deleting non-empty folders is not allowed` , {id : "delete-folder"});
                  errors.push(`${file.name} (Folder not empty)`);
                  return; // Skip to the next file
                }
              }

            if (!canDelete) {
              console.error(`Permission denied to delete ${file.isDir ? 'folder' : 'file'}: ${file.name}`);
              errors.push(`${file.name} (Permission Denied)`);
              return; // Skip to the next file
            }

            const { error } = await api.deleteFile(file, permanent);
            if (error) {
              console.error(`Failed to delete ${file.name}:`, error);
              errors.push(file.name);
            } else {
              successCount++;
            }
          } catch (e) {
            console.error(`Exception during deletion of ${file.name}:`, e);
            errors.push(file.name);
          }
        })
      );

      await refreshFileMap(true);

      const fileWord = successCount > 1 ? "files" : "file";
      const errorFileWord = errors.length > 1 ? "files" : "file";

      if (errors.length === 0) {
        toast.success(`selected files deleted successfully.`, { id: toastId });
      } else if (successCount > 0) {
        toast.error(
          `Deleted ${successCount} ${fileWord}, but failed to delete ${errors.length} ${errorFileWord}`,
          { id: toastId, duration: 5000 }
        );
      } else {
        toast.error(`Failed to delete selected files`, { id: toastId, duration: 5000 });
      }
    },
    [refreshFileMap, isLogin, api]
  );

  


  const clear3DConfig = useCallback(
    async (file: CustomFileData) => {
      if (!api) return;
      const newConfig = { ...(file.config || {}) };
      FILE_CONFIG_3D_PROPERTIES.forEach((prop) => {
        newConfig[prop] && delete newConfig[prop];
      });

      if(file.isDir){ //remove isDefault from folder config, so it doesn't act as default folder
        newConfig["isDefault"] && delete newConfig["isDefault"];
      }

      const { data, error } = await api.updateFile({ id: file.id, config: newConfig });
      if (error || !data) {
        toast.error("Failed to clear 3D config", { id: "clear3DConfig" });
        return;
      }
      toast.success("3D config cleared", { id: "clear3DConfig" });
      setFileMap((prev: CustomFileMap) => ({ ...prev, [data.id]: data }));
    },
    [api, setFileMap]
  );

  //todo : make it accept multiple files and put a check for 1000 limit
  const duplicateFile = useCallback(
    async (file: CustomFileData, newName: string) => {
      if (!isLogin || !api) return;
      toast.loading("Duplicating file...", { id: "duplicating" });
      
      // If file is not a directory and newName doesn't have an extension, add the original extension
      if (!file.isDir) {
        const originalExt = file.file?.split('.').pop();
        if (originalExt && !newName.includes('.')) {
          newName = `${newName}.${originalExt}`;
        }
      }

      const response = await api.copyFile(file, file.path, newName);
      if (response.error) {
        toast.error("Failed to duplicate file", { id: "duplicating" });
        return;
      }
      await refreshFileMap(true);
      toast.success("File duplicated", { id: "duplicating" });
    },
    [isLogin, api, refreshFileMap]
  );

  const moveFiles = useCallback(
    async (files: CustomFileData[], newParentId: string) => {
      if (!isLogin || !api) return;
      if(files.length > 1000){
        toast.error("Can't move more than 1000 files at once")
        return;
      }
      toast.loading("Moving files", { id: "moving" });
      // const fileIds = files.map((file) => file.id);

      const parent = await getFile(newParentId);
      if (!parent) {
        toast.error("can't find parent");
        return;
      } else if (parent.locked) {
        toast.error("Parent folder is locked", { id: "moving" });
        return;
      }
      let isError = false;
      const newPath = parent.id === "/" ? "/" : parent.path + parent.id + "/";
      await Promise.all(
        files.map((file) =>
          api.moveFile(file, newPath).then((data) => {
            if (data.error) {
              toast.error("Failed to move " + file.name, { id: "moving" });
              isError = true;
            }
          })
        )
      );
      await refreshFileMap(true);
      if (!isError) toast.success("Finished Moving", { id: "moving" });
    },
    [isLogin, api, getFile, refreshFileMap]
  );

  const getFolderChildCount = useCallback(
    async (folderId: string) => {
      if (!isLogin || !api || !folderId) return 0;
      const {data: count, error} = await api.getFolderChildCount(folderId);
      if (error) return 0;
      if (count) return count;
      return 0;
    },
    [isLogin, api]
  );

  const getFolderByName = useCallback(
    async (folderName: string, path: string): Promise<CustomFileData | null> => {
      if (!isLogin || !api) return null;
      // First, check in the fileMap
      const folderInMap = Object.values(filemapRef.current || fileMap).find((f) => f.name === folderName && f.path === path && f.isDir);
      if (folderInMap) return folderInMap;

      // If not found in fileMap, check the backend
      try {
        const { data: file } = await api.getFileByName(folderName, path);
        if (file && file.isDir) {
          setFileMap((fileMap: CustomFileMap) => ({ ...fileMap, [file.id]: file }));
          return file;
        } else {
          return null;
        }
      } catch (error) {
        console.error("Error checking folder existence on backend:", error);
        return null;
      }
    },
    [isLogin, api, fileMap, setFileMap]
  );

  const getFilesByTag = useCallback(
    async (tag: string , parent: string) => {
      if (!isLogin || !api) return;
      const { data: files, error } = await api.getFilesBytag(tag , parent);
      if (error) {
        toast.error("Failed to fetch files", { id: "fetching" });
        return;
      }
      setFileMap((fileMap: CustomFileMap) => files.reduce((acc, file) => ({ ...acc, [file.id]: file }), fileMap));
      return files;
    },
    [isLogin, api, setFileMap]
  );

  const createFolder = useCallback(
    async (folderName: string) => {
      if (!isLogin || !api) return;
      toast.loading("Creating Folder", { id: "createFolder" });

      //check if folder already exists
      const folder = await getFolderByName(folderName, currentPath);
      if (folder) {
        toast.error("A folder with the same name already exists", { id: "createFolder" });
        return;
      }

      const parents = currentPath.split("/");
      parents.pop();
      const parent = await getFile(parents.pop() || "");
      if (parent && parent.locked) {
        toast.error("Parent folder is locked", { id: "createFolder" });
        return;
      }

      const res = await api.createDirectory(folderName, currentPath);
      if (!res || res.error) {
        toast.error("Failed to create folder", { id: "createFolder" });
      } else {
        toast.success("Folder created", { id: "createFolder" });
      }
      await refreshFileMap(true);
    },
    [isLogin, api, getFolderByName, currentPath, getFile, refreshFileMap]
  );

  const isProcessing = useRef(false);
  const processFiles = useCallback(
    async (fileList: File[], uploadOptions: UploadOptions) => {
      while (isProcessing.current) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
      isProcessing.current = true;

      const thumbGen : any= await getThumbnailGenerator();

      for (let i = 0; i < fileList.length; i++) {
        const file = fileList[i];
        updateQueue(file, "processing", 0);

        const ext = file.name.split(".").pop();

        if (ext === "3dm" || ext === "fbx" || ext === "obj" || ext === "stl" || ext === "glb" || ext === "gltf") {
          try{
            const glb = await thumbGen.process3D(file, uploadOptions).catch((err : any) => {
              console.error("Error processing 3D file", err);
              updateQueue(file, "error", 0 , { id: file.name , errorMessage : "Failed to process file"});
            });
            if (glb && glb.file) {
              updateQueue(glb.file, "queued", 0, { id: file.name, ...glb.data });
            }
          }catch(e){
            console.log("error processing file", e);
            updateQueue(file, "error", 0 , { id: file.name , errorMessage : "Failed to process file"});
            continue;
          }
        } else {
          try{
            const thumbnailFile = await thumbGen.snapFile(file);
            updateQueue(file, "queued", 0, { thumbnailFile });
          }catch(e){
            console.log("error processing file", e);
            updateQueue(file, "queued", 0);
          }
        }
      }

      isProcessing.current = false;
    },
    [getThumbnailGenerator, updateQueue]
  );

  const updateNote = useCallback(async (file: CustomFileData, note: string) => {
    if (!api) {
      console.error("API not available");
      return;
    }
    toast.loading("Saving note...", { id: "updateNote" });
    const { data, error } = await api.updateFile({ id: file.id, notes : note });
    if (error || !data) {
      toast.error("Failed to save note", { id: "updateNote" });
      return;
    }
    toast.success("Note saved", { id: "updateNote" });
    setFileMap((prev: CustomFileMap) => ({ ...prev, [data.id]: data }));
  }, [api, setFileMap]);

  const updateDescription = useCallback(async (file: CustomFileData, config: any) => {
    if (!api) {
      console.error("API not available");
      return;
    }
    toast.loading("Saving description...", { id: "updateDescription" });
    const { data, error } = await api.updateFile({ id: file.id, config : config });
    if (error || !data) {
      toast.error("Failed to save description", { id: "updateDescription" });
      return;
    }
    toast.success("Description saved", { id: "updateDescription" });
    setFileMap((prev: CustomFileMap) => ({ ...prev, [data.id]: data }));
  }, [api, setFileMap]);
  

  const updateThumbnail = useCallback(
    async (files: CustomFileData[]) => {
      if (!isLogin || !api) return;
      toast.loading("Updating Thumbnails", { id: "updateThumbnail" });
      const thumbGen = await getThumbnailGenerator();
      for (let i = 0; i < files.length; i++) {
        const filedata = files[i];
        try{
          const file = await fetch(api.getDownloadUrl(filedata)).then((r) => r.blob()).then((blob) => new File([blob], filedata.name));
          if(!file){
            toast.error("Failed to update thumbnail", { id: "updateThumbnail" });
            continue;
          }
          const thumbnailFile = await (thumbGen as any).snapFile(file);
          if (!thumbnailFile) {
            toast.error("Failed to update thumbnail", { id: "updateThumbnail" });
            continue;
          }
          await api.updateFile({ id: filedata.id } , thumbnailFile);
        }catch(e){
          console.log("error updating thumbnail", e);
          toast.error("Failed to update thumbnail", { id: "updateThumbnail" });
        }
      }
      toast.success("Thumbnails updated", { id: "updateThumbnail" });
      refreshFileMap(true);
  }, [isLogin, api, getThumbnailGenerator, refreshFileMap]);

  async function handleReplace(
    item: QueueItem,
    currentCopy: CustomFileData,
    meta: any,
    abortController: AbortController
  ) {
    if(!api){ toast.error("API not initialized"); return;}
    updateQueue(item.file, "uploading", 1);
  
    const { data, error } = await api.updateFile(
      {
        id: currentCopy.id,
        file: item.file,
        meta: { ...currentCopy.meta, ...meta },
      },
      item.data.thumbnailFile,
      abortController.signal
    );
  
    if (error) {
      if (error.name === "AbortError") {
        updateQueue(item.file, "cancelled", 0);
      } else {
        console.error("Failed to update existing file", error);
        updateQueue(item.file, "error", 0);
      }
      return null;
    }
    return data;
  }
  
  async function handleKeepBoth(
    item: QueueItem,
    fullPath: string,
    meta: any,
    progressCallback: (file: File, progress: number) => void,
    abortController: AbortController
  ) {
    if(!api){ toast.error("API not initialized"); return;}
    updateQueue(item.file, "uploading", 1);
  
  let newName = getDuplicateName(item.file.name);
  
  async function getNewName(): Promise<string> {
    let iterations = 0;
    const maxIterations = 10;
    
      while (iterations < maxIterations) {
        const { data: newCopy } = await getFileByName(newName, fullPath);
        if (!newCopy) {
          return newName;
        }
        newName = getDuplicateName(newName);
        iterations++;
      }
      console.error("Failed to upload file", error);
      toast.error("Max same File name reached")
      updateQueue(item.file, "error", 0);
    throw new Error("Max same File name reached")
  }

  newName = await getNewName();
  
    const { data, error } = await api.uploadFile(
      item.file,
      fullPath,
      item.data?.thumbnailFile,
      meta,
      progressCallback,
      abortController,
      undefined,
      newName
    );
  
    if (error) {
      console.error("Failed to upload file", error);
      updateQueue(item.file, "error", 0);
      return null;
    }
    return data;
  }
  
  async function handleNewUpload(
    item: QueueItem,
    fullPath: string,
    meta: any,
    progressCallback: (file: File, progress: number) => void,
    abortController: AbortController
  ) {
    if(!api){ toast.error("API not initialized"); return;}
    const { data, error } = await api.uploadFile(
      item.file,
      fullPath,
      item.data?.thumbnailFile,
      meta,
      progressCallback,
      abortController
    );
  
    if (error) {
      console.error("Failed to upload file", error);
      updateQueue(item.file, "error", 0);
      return null;
    }
    return data;
  }

  //todo: this function and processFiles are very important and need to be refactored into a proper class,
  //with better error handling and state management
  const uploading = useRef(false);
  const uploadFiles = useCallback(
    async (files: { file: File; path: string }[], options: UploadOptions) => {
      if(files.length > 1000){
        toast.error("Can't upload more than 1000 files at once");
        return;
      }
      if (!isLogin || !api) return;

      const parents = currentPath.split("/");
      parents.pop();
      const destinationRoot = await getFile(parents.pop() || "");
      if (destinationRoot?.locked) {
        toast.error("Destination folder is locked");
        return;
      }

      for (const file of files) {
        if (!can("upload-file")) {
          updateQueue(file.file, "error", 0, { id: file.file.name, path: file.path });
          toast.error(`Yu don't have permission to upload files` , {id : "uploading"});
          continue;
        }
        updateQueue(file.file, "idle", 0, { id: file.file.name, path: file.path });
      }
      processFiles(
        files.map(({ file }) => file),
        options
      );

      const progressCallback = (file: File, progress: number) => {
        updateQueue(file, "uploading", progress);
      };
      
      let replaceAll : undefined | UploadConfirmType = undefined;
      const upload = async (item: QueueItem) => {
        try {
          const relativePath = item.data.path;
          const pathParts = relativePath.split("/");
          pathParts.pop();
          let parentPath = currentPath;
          const abortController = new AbortController();
          updateQueue(item.file, "uploading", 0, { abortController });
          const error = await canUpload(item.file);
          if (error) {
            updateQueue(item.file, "error", 0);
            toast.error(`${error}` , {id : "uploading"});
            return;
          }

          // Create directories if they don't exist
          for (const folderName of pathParts) {
            if(!folderName) {
              continue;
            }
            let folder = await getFolderByName(folderName, parentPath);
            if (!folder) {
              const response = await api.createDirectory(folderName, parentPath);
              if (response.error || !response.data) {
                console.error("Failed to create directory");
                updateQueue(item.file, "error", 0);
                return;
              }
              refreshFileMap(false , true);
              folder = response.data;
            } else if (folder.locked) {
              console.error("Parent folder is locked");
              updateQueue(item.file, "error", 0);
              return;
            }

            if (!folder || folder.error) {
              console.error("Failed to create directory");
              updateQueue(item.file, "error", 0);
              return;
            }
            parentPath = folder.path + folder.id + "/";
          }

          // Now parentPath is the directory where we need to upload the file
          const fullPath = parentPath;
          const { data: currentCopy } = await getFileByName(item.file.name, fullPath);
          let res;

          const meta : any = {size : item.file.size}
          if(item.data?.polyCount) meta.polyCount = item.data.polyCount;
          if(item.data?.materials) meta.materials = item.data.materials.map((m : any) => m.name).
            filter((m : any) => m && m !== "BaseGroundMaterial");    
          if(item.data?.swichNodesCount) meta.swichNodesCount = item.data.swichNodesCount;      
          
          if (currentCopy) {
            if(replaceAll === undefined){
              replaceAll = await openUploadConfirm(`Some files in the destination folder have the same name. Choose an option to continue.`, {
                title: "Upload options",
              });
            }
            if (replaceAll === 'CANCEL') {
              updateQueue(item.file, "cancelled", 0);
              return;
            }
            else if(replaceAll === "REPLACE"){
              res = await handleReplace(item, currentCopy, meta, abortController);
            }
            else if(replaceAll === 'KEEP_BOTH'){
              res = await handleKeepBoth(item, fullPath, meta, progressCallback, abortController);
            }else if (replaceAll === 'SKIP'){
              updateQueue(item.file, "cancelled", 0);
              return;
            }
          } else {
            res = await handleNewUpload(item, fullPath, meta, progressCallback, abortController);
          }
          if (res?.name) {
            incrementModelsCount();
            updateQueue(item.file, "finished", 100);
          }
        } catch (e) {
          updateQueue(item.file, "error", 0);
          console.log("Error uploading file", e);
        }
        refreshFileMap(false , true);
      };

      const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

      await sleep(1000);
      if (uploading.current) return;
      uploading.current = true;
      const maxParallelUploads = 5; //todo add this to drive-config
      // eslint-disable-next-line no-constant-condition
      while (true) {
        if (!fileQueueRef?.length) break;
        const queuedItem = fileQueueRef.find((item) => item.state === "queued");
        const uploadingItems = fileQueueRef.filter((item) => item.state === "uploading");
        const preUploadingItems = fileQueueRef.filter((item) => item.state === "uploading" && item.progress === 0);
        const remainingItems = fileQueueRef.filter((item) => item.state !== "finished" &&
         item.state !== "error" && item.state !== "cancelled").length;

        if(uploadingItems.length >= maxParallelUploads) {
          await sleep(1000);
          continue;
        }

        // If there are items that have not yet started uploading, wait for them to start
        if(preUploadingItems.length > 0) {
          await sleep(1000);
          continue;
        }

        if (queuedItem) {
          upload(queuedItem);
          await sleep(100);
        } else if (remainingItems > 0) {
          await sleep(1000);
        } else {
          break;
        }
      }
      uploading.current = false;
    },
    [api, canUpload, currentPath, fileQueueRef, getFile, getFileByName, getFolderByName, incrementModelsCount, isLogin, openConfirm, processFiles, refreshFileMap, updateQueue]
  );

  const downloadFiles = useCallback(
    async (files: CustomFileData[]) => {
      if (!isLogin || !api) return;
      for(const file of files){
        await api.downloadFile(file);
        //todo: this is a hack to wait for the download to start, need to find a better way
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
    },
    [isLogin, api]
  );

  const addToRecentFiles = useCallback((file: CustomFileData) => {
    if (file.isDir) return;
    
    try {
      const recentFiles = JSON.parse(localStorage.getItem(RECENT_FILES_KEY + "_" + user?.id) || "[]");

      const filtered = recentFiles.filter((f: CustomFileData) => f.id !== file.id);

      filtered.unshift(file);

      const trimmed = filtered.slice(0, MAX_RECENT_FILES);
      
      localStorage.setItem(RECENT_FILES_KEY + "_" + user?.id, JSON.stringify(trimmed));
    } catch (error) {
      console.error('Error updating recent files:', error);
    }
  }, []);

  const previewFile = useCallback(
    (file: CustomFileData) => {
      setPreview(file);
      addToRecentFiles(file);
    },
    [setPreview, addToRecentFiles]
  );

  const searchFiles = useCallback(async () => {
    if (!isLogin || !api) return;
    if (searchString === "") {
      setSearchResults([]);
      return;
    }

    const searcher = new FuzzySearch(Object.values(fileMap), ["name"], {
      caseSensitive: false,
    });

    //@ts-ignore
    setSearchResults([...searcher.search(searchString).filter((s) => (s?.path as string)?.startsWith(currentPath)), null]);

    const searchId = ++lastSearchId.current;

    const { data: files, error } = await api.search(searchString, currentPath);
    if (searchId !== lastSearchId.current) return;
    if (error || !files) {
      toast.error("Failed to search files");
      return;
    }
    setFileMap((fileMap: CustomFileMap) => files.reduce((acc, file) => ({ ...acc, [file.id]: file }), fileMap));

    //add the result to the prev results
    setSearchResults((prev: CustomFileData[]) => {
      //remove null at the end
      prev.pop();

      const newResults = files.filter((file: CustomFileData) => !prev.find((f) => f?.id === file?.id));
      return [...prev, ...newResults];
    });
  }, [api, currentPath, fileMap, isLogin, searchString, setFileMap, setSearchResults]);

  const updateTags = useCallback(
    async (file: CustomFileData | CustomFileData[], tags: string , refresh = true) => {
      if (!api) return;
      toast.loading("Saving tags", { id: "loading" });
      let response;
      if (Array.isArray(file)) {
        await Promise.all(file.map((f) => api.updateFile({ id: f.id, tags })));
      } else {
        response = await api.updateFile({ id: file.id, tags });
      }
      if (response?.error || !response?.data) {
        console.error("Failed to save tags");
        toast.error("Failed to save tags", { id: "loading" });
        return;
      }

      toast.success("Tags saved", { id: "loading" });
      refresh && refreshFileMap(true);
    },
    [api, refreshFileMap]
  );

  const updateLayers = useCallback(
    async (file: CustomFileData, tags: string) => {
      if (!api) return;
  
      const layerTags = tags.split(",").filter((tag) => tag.startsWith("_layer:"));
  
      // For each new layer, check if there is an existing default in any material
      for (const layerTag of layerTags) {
        const layerName = layerTag.replace("_layer:", "").trim();
        // Find other files in the 'assets' folder that have this layer
        const existingLayerFiles = await getFilesByTag(layerTag, file.path.split("/")?.[1] || "/") || [];
        if(existingLayerFiles.findIndex((f) => f.id === file.id) !== -1) {
          existingLayerFiles.splice(existingLayerFiles.findIndex((f) => f.id === file.id), 1);
        }
        if (existingLayerFiles.length > 0) {
          const proceed = await openConfirm(
            `A default already exists for layer "${layerName}". Do you want to override it?`
          );
          if (!proceed) {
            // Remove the layer from the newLayerTags
            const index = tags.indexOf(layerTag);
            if (index > -1) {
              layerTags.splice(index, 1);
            }
            continue;
          }
  
          // Remove tag from existing default files for this layer
          for (const existingFile of existingLayerFiles) {
            const existingFileTagsArray = existingFile.tags
              ? existingFile.tags.split(",").map((tag) => tag.trim())
              : [];
            const newExistingFileTagsArray = existingFileTagsArray.filter((tag) => tag !== layerTag);
            const newExistingFileTags = newExistingFileTagsArray.join(",");
            await updateTags(existingFile, newExistingFileTags);
          }
        }
      }

      const otherTags = file.tags?.split(",").filter((tag) => !tag.startsWith("_layer:")) || [];
  
      const updatedTagsArray = [...otherTags, ...layerTags];
      const updatedTags = updatedTagsArray.join(",");
      await updateTags(file, updatedTags);
    },
    [api, fileMap, updateTags, openConfirm, config]
  );

  const updateCategory = useCallback(
    async (files: CustomFileData[], categoryName: string) => {
      if (!api) return;
  
      // Loop over each file
      for (const file of files) {
        const tagsArray = file.tags ? file.tags.split(",").map((tag) => tag.trim()) : [];
        const newTagsArray = tagsArray.filter((tag) => !tag.startsWith("_category:"));
        newTagsArray.push(`_category:${categoryName}`);
        const newTags = newTagsArray.join(",");
        await updateTags(file, newTags , false);
      }
      toast.success("Category set successfully");
      refreshFileMap(true); // Refresh the file map to reflect changes
    },
    [api, updateTags, refreshFileMap]
  );

  const updateDefault = useCallback(
    async (file: CustomFileData) => {
      if (!api) return;
  
      // Get the category of the file
      const categoryTag = file.tags?.split(",").find((tag) => tag.trim().startsWith("_category:")) || "";
      const categoryName = categoryTag.replace("_category:", "").trim();
      const existingDefaultFiles = await getFilesByTag("_default", file.path.split("/")?.[1] || "/") || [];
  
      const filesInSameCategory = existingDefaultFiles.filter((f) => {
        if (f.id === file.id) return false;
        const tagsArray = f.tags?.split(",").map((tag) => tag.trim()) || [];
        return (tagsArray.includes(categoryTag) || ((f.path === file.path) && !categoryTag) ) && tagsArray.includes("_default");
      });
  
      if (filesInSameCategory.length > 0) {
        // Ask for confirmation to override
        const proceed = await openConfirm(
          `A default already exists for category "${categoryName}". Do you want to override it?`
        );
        if (!proceed) {
          return;
        }
  
        // Remove _default tag from existing default files in the same category
        for (const existingFile of filesInSameCategory) {
          const existingFileTagsArray = existingFile.tags
            ? existingFile.tags.split(",").map((tag) => tag.trim())
            : [];
          const newExistingFileTagsArray = existingFileTagsArray.filter((tag) => tag !== "_default");
          const newExistingFileTags = newExistingFileTagsArray.join(",");
          await updateTags(existingFile, newExistingFileTags, false);
        }
      }
  
      // Add _default tag to the selected file if not already present
      const fileTagsArray = file.tags ? file.tags.split(",").map((tag) => tag.trim()) : [];
      if (!fileTagsArray.includes("_default")) {
        fileTagsArray.push("_default");
      }
      const newTags = fileTagsArray.join(",");
      await updateTags(file, newTags);
      toast.success("File set as default successfully.");
      refreshFileMap(true);
    },
    [api, updateTags, getFilesByTag, config, openConfirm, refreshFileMap]);

    const updatePremium = useCallback( async (files : CustomFileData[]) => {
      if (!api) return;
      for (const file of files) {
        const tagsArray = file.tags ? file.tags.split(",").map((tag) => tag.trim()) : [];

        const newTagsArray = tagsArray.filter((tag) => tag !== "_premium");
        !tagsArray.includes("_premium") && newTagsArray.push("_premium");
        const newTags = newTagsArray.join(",");
        await updateTags(file, newTags , false);
      }
      toast.success("Premium set successfully");
      refreshFileMap(true); // Refresh the file map to reflect changes
    }, [api, updateTags, refreshFileMap])

  const renameFile = useCallback(
    async (file: CustomFileData, newName: string) => {
      if (!isLogin || !api) return;
      file.isDir ? toast.loading("Renaming folder...", { id: "renaming" }) : toast.loading("Renaming file...", { id: "renaming" });
      //check if name has the extension or add it
      if (newName.split(".").pop() != file.file?.split(".").pop() && !file.isDir) {
        newName += "." + file.file?.split(".").pop();
      }
      await api.renameFile(file, newName);
      await refreshFileMap(true);
      file.isDir ? toast.success("Folder renamed", { id: "renaming" }) : toast.success("file renamed", { id: "renaming" });
    },
    [refreshFileMap, isLogin, api]
  );

  const toggleFileLock = useCallback(
    async (file: CustomFileData) => {
      if (!api) return;
      toast.loading(file.locked ? "Unlocking file..." : "Locking file...", { id: "lock" });
      const { data, error } = await api.toggleFileLock(file);
      if (!data || error) {
        console.error(error);
        toast.error("Failed to lock file", { id: "lock" });
        setLoading(false);
        return;
      }
      toast.success(file.locked ? "File unlocked" : "File locked", { id: "lock" });
      refreshFileMap(true);
    },
    [api, refreshFileMap, setLoading]
  );

  const updateFilePermissions = useCallback(
    async (file: CustomFileData, permissions: Partial<fileMetaData>) => {
      if (!api) return;
      toast.loading("Updating permissions...", { id: "permissions" });
      setLoading(true);
      const meta = { ...file.meta, ...permissions };
      if (!meta.pub) {
        delete meta.pub;
      }
      const { data, error } = await api.updateFile(
        {
          id: file.id,
          meta,
        },
        undefined
      );

      if (!data || error) {
        console.error(error);
        toast.error("Failed to update permissions", { id: "permissions" });
        setLoading(false);
        return;
      }

      setLoading(false);
      toast.success("Permissions updated", { id: "permissions" });
      setFileMap((fileMap: CustomFileMap) => ({ ...fileMap, [data.id]: data }));
    },
    [api, setFileMap, setLoading]
  );

  //actions
  const showOpenInFolder = useCallback(() => {
    if (selectedFiles.size !== 1 || !searchString) return CustomVisibilityState.Hidden;
    return CustomVisibilityState.Default;
  }, [searchString, selectedFiles]);

  const showLockFile = useCallback(() => {
    if (selectedFiles.size !== 1 || !user?.role?.endsWith("admin") || fileMap[[...selectedFiles][0]].locked) return CustomVisibilityState.Hidden;
    return CustomVisibilityState.Default;
  }, [fileMap, selectedFiles, user]);

  const showUnlockFile = useCallback(() => {
    if (selectedFiles.size !== 1 || !user?.role?.endsWith("admin") || !fileMap[[...selectedFiles][0]].locked) return CustomVisibilityState.Hidden;
    return CustomVisibilityState.Default;
  }, [fileMap, selectedFiles, user]);

  const showOverrideFile = useCallback(() => {
    if(can("edit-tags") && selectedFiles.size > 1) return CustomVisibilityState.Default;
    return CustomVisibilityState.Hidden;
  } , [can, selectedFiles.size])

  const showModifyLayers = useCallback(() => {
    if(!can("edit-tags") || selectedFiles.size < 1) return CustomVisibilityState.Hidden;
    const materialsFolder = Object.values(fileMap).find((f) => f.name === "materials" && f.isDir && f.path.includes("/" + config?.["assets-path"]));
    const selectedFile = fileMap[[...selectedFiles][0]];
    if(selectedFile &&  materialsFolder && selectedFile.path.includes(materialsFolder.id)) return CustomVisibilityState.Default;
    return CustomVisibilityState.Hidden;
  } , [can, selectedFiles.size , fileMap])

  const showEditFileInfo = useCallback(() => {
    if(selectedFiles.size !== 1) return CustomVisibilityState.Hidden;
    if(!config || !config.features?.["edit-file-info"]?.enabled) return CustomVisibilityState.Hidden; 
    return CustomVisibilityState.Default;
  } , [selectedFiles , config])

  const showClear3dConfig = useCallback(() => {
    const selectedFile = fileMap[[...selectedFiles][0]];
    if(!selectedFile) return CustomVisibilityState.Hidden;
    if(!can("edit-file" , undefined , {file : selectedFile}) || selectedFiles.size !== 1) return CustomVisibilityState.Hidden;
    //check if it's a 3d file or folder
    if(selectedFile && (is3dFile(selectedFile) || selectedFile.isDir)) return CustomVisibilityState.Default;
    return CustomVisibilityState.Hidden;
  }, [can, selectedFiles, fileMap])

  const showEditDefaultSettings = useCallback(() => {
    if(selectedFiles.size !== 1) return CustomVisibilityState.Hidden;
    //show only for folders
    if(fileMap[[...selectedFiles][0]]?.isDir) return CustomVisibilityState.Default;
    return CustomVisibilityState.Hidden;
  } , [selectedFiles, fileMap])

  const showExportCsv = useCallback(() => {
    if(selectedFiles.size !== 1) return CustomVisibilityState.Hidden;
    //show only for folders
    if(fileMap[[...selectedFiles][0]]?.isDir) return CustomVisibilityState.Default;
    return CustomVisibilityState.Hidden;
  } , [selectedFiles, fileMap])

  const showSetCategory = useCallback(() => {
    if(!can("edit-tags") || selectedFiles.size < 1) return CustomVisibilityState.Hidden;
    const assetsFolder = fileMap[config?.["assets-path"] || ""];
    const selectedFile = fileMap[[...selectedFiles][0]];
    if(selectedFile &&  assetsFolder && selectedFile.path.includes(assetsFolder.id)) return CustomVisibilityState.Default;
    return CustomVisibilityState.Hidden;
  } , [can, selectedFiles, fileMap, config])

  const showMakeDefault = useCallback(() => {
    if(!can("edit-tags") || selectedFiles.size !== 1) return CustomVisibilityState.Hidden;
    const assetsFolder = fileMap[config?.["assets-path"] || ""];
    const selectedFile = fileMap[[...selectedFiles][0]];
    if(selectedFile &&  assetsFolder && selectedFile.path.includes(assetsFolder.id)) return CustomVisibilityState.Default;
    return CustomVisibilityState.Hidden;
  }, [can, config, fileMap, selectedFiles]);

  const showMakePremium = useCallback(() => {
    if(!can("edit-tags") || selectedFiles.size < 1) return CustomVisibilityState.Hidden;
    const assetsFolder = fileMap[config?.["assets-path"] || ""];
    const selectedFile = fileMap[[...selectedFiles][0]];
    if(selectedFile &&  assetsFolder && selectedFile.path.includes(assetsFolder.id) && !selectedFile.tags?.includes("_premium")) return CustomVisibilityState.Default;
    return CustomVisibilityState.Hidden;
  }, [can, config, fileMap, selectedFiles]);

  const showMakeFree = useCallback(() => {
    if(!can("edit-tags") || selectedFiles.size < 1) return CustomVisibilityState.Hidden;
    const assetsFolder = fileMap[config?.["assets-path"] || ""];
    const selectedFile = fileMap[[...selectedFiles][0]];
    if(selectedFile &&  assetsFolder && selectedFile.path.includes(assetsFolder.id) && selectedFile.tags?.includes("_premium")) return CustomVisibilityState.Default;
    return CustomVisibilityState.Hidden;
  }, [can, config, fileMap, selectedFiles]);

  const CustomShareAction: FileAction = {
    id: "share",
    button: { name: "Share", iconOnly:true, contextMenu: true, toolbar: true, icon: ChonkyIconName.share },
    requiresSelection: true,
  };

  const CustomDownloadAction: FileAction = {
    ...ChonkyActions.DownloadFiles,
    button: { ...ChonkyActions.DownloadFiles.button, name: "Download", iconOnly: true },
    fileFilter: (file: CustomFileData | null) => {
      return file?.isDir === false;
    },
  };

  const customDeleteAction: FileAction = {
    ...ChonkyActions.DeleteFiles,
    button: { ...ChonkyActions.DeleteFiles.button, name: "Delete" },
    requiresSelection: true,
    fileFilter: (file: CustomFileData | null) => {
      return (!file?.locked && user?.role?.endsWith("admin")) || user?.role === "editor";
    },
  };

  const customClear3dAction: FileAction = {
    id: "clear_3d",
    button: { name: "Clear 3D Settings" , contextMenu: true, icon: ChonkyIconName.clear_3d },
    requiresSelection: true,
    fileFilter: (file: CustomFileData | null) => {
      return !file?.locked;
    },
    customVisibility: showClear3dConfig,
  };

  const customDuplicateAction: FileAction = {
    id : "duplicate",
    button: { name: "Duplicate", contextMenu: true, icon: ChonkyIconName.duplicate },
    requiresSelection: true,
    fileFilter: useCallback(
      (file: CustomFileData | null) => {
        return selectedFiles.size === 1 && !file?.locked;
      },
      [selectedFiles]
    ),
  };

  const CustomRenameAction: FileAction = {
    id: "rename",
    button: { name: "Rename", contextMenu: true, icon: ChonkyIconName.rename },
    requiresSelection: true,
    fileFilter: useCallback(
      (file: CustomFileData | null) => {
        return selectedFiles.size === 1 && !file?.locked;
      },
      [selectedFiles]
    ),
  };

  const CustomViewAction: FileAction = {
    id: "view",
    button: { name: "Viewer", contextMenu: true, nestedGroup: 'Open In'},
    fileFilter: useCallback(
      (file: CustomFileData | null) => {
        return file?.isDir === false && selectedFiles.size === 1;
      },
      [selectedFiles]
    ),
    requiresSelection: true,
    effect: (data: any) => {
      window.open(`/${basename}/files/${data.state.selectedFilesForAction![0].id}/view`, "_blank");
    },
  };

  const CustomEditAction: FileAction = {
    id: "edit",
    button: { name: "Editor", contextMenu: true, nestedGroup: 'Open In' },
    fileFilter: useCallback(
      (file: CustomFileData | null) => {
        return file?.isDir === false && selectedFiles.size === 1 && !file.locked;
      },
      [selectedFiles]
    ),
    requiresSelection: true,
    effect: (data: any) => {
      window.open(`/${basename}/files/${data.state.selectedFilesForAction![0].id}/edit`, "_blank");
    },
  };

  const CustomOpenInPlaygroundAction: FileAction = {
    id: "open_in_playground",
    button: { name: "Playground", contextMenu: true, nestedGroup: 'Open In' },
    fileFilter: useCallback(
      (file: CustomFileData | null) => {
        return file?.isDir === false && selectedFiles.size === 1 && !file.locked && is3dFile(file);
      },
      [selectedFiles]
    ),
    requiresSelection: true,
    effect: (data: any) => {
      window.open(`/${basename}/files/${data.state.selectedFilesForAction![0].id}/playground`, "_blank");
    },
  };


  const CustomEditDefaultSettingsAction: FileAction = {
    id: "edit-defaults",
    button: { name: "Edit Default Settings", contextMenu: true},
    fileFilter: useCallback(
      (file: CustomFileData | null) => {
        return (file !== null && selectedFiles.size === 1 && !file?.locked && file.isDir) ?? false;
      },
      [selectedFiles]
    ),
    customVisibility: showEditDefaultSettings,
    requiresSelection: true,
    effect: (data: any) => {
      window.open(`/${basename}/files/${data.state.selectedFilesForAction![0].id}/edit`, "_blank");
    },
  };

  const CustomEditTagsAction: FileAction = {
    id: "edit_tags",
    button: { name: "Edit Tags" , contextMenu: true, icon: ChonkyIconName.edit },
    fileFilter: useCallback(
      (file: CustomFileData | null) => {
        return selectedFiles.size === 1 && !file?.locked;
      },
      [selectedFiles]
    ),
    requiresSelection: true,
  };

  const CustomOpenFolderAction: FileAction = {
    id: "show_in_folder",
    button: { name: "Show in Folder", contextMenu: true },
    effect: (data: any) => {
      setSearchString("");
      setCurrentPath(data.state.selectedFilesForAction![0].path);
    },
    customVisibility: showOpenInFolder,
  };

  const customLockFileAction: FileAction = {
    id: "lock",
    button: { name: "Lock", contextMenu: true },
    requiresSelection: true,
    customVisibility: showLockFile,
  };

  const customUnlockFileAction: FileAction = {
    id: "unlock",
    button: { name: "Unlock", contextMenu: true },
    requiresSelection: true,
    customVisibility: showUnlockFile,
  };

  const customOverrideTagsAction: FileAction = {
    id: "override_tags",
    button: { name: "Override Tags", contextMenu: true },
    requiresSelection: true,
    fileFilter: (file: CustomFileData | null) => !file?.locked,
    customVisibility: showOverrideFile 
  };

  const customModifyLayersAction: FileAction = {
    id: "modify_layers",
    button: { name: "Modify Layers", contextMenu: true },
    requiresSelection: true,
    fileFilter: (file: CustomFileData | null) => {
      if (!file || file.isDir || file.locked) return false;
      return true;
    },
    customVisibility: showModifyLayers
  };
  const customEditFileInfoAction: FileAction = {
    id: "edit_file_info",
    button: { name: "Edit File Info", contextMenu: true },
    requiresSelection: true,
    fileFilter: (file: CustomFileData | null) => {
      return !file?.locked && !file?.isDir;
    },
    customVisibility: showEditFileInfo
  };
  const customSetCategoryAction: FileAction = {
    id: "set_category",
    button: { name: "Set Category", contextMenu: true },
    requiresSelection: true,
    fileFilter: (file: CustomFileData | null) => {
      return !file?.locked  && !file?.isDir;
    },
    customVisibility: showSetCategory
  };

  const CustomPermanentDeleteAction: FileAction = {
    id: "permanent_delete",
    button:{ name: "Permanent Delete", contextMenu: true, toolbar: true, icon: ChonkyIconName.trash },
    hotkeys:['shift+delete'],
    requiresSelection: true,
    customVisibility: useCallback(() => {
      return currentView === 'trash' ? CustomVisibilityState.Default : CustomVisibilityState.Hidden;
    }, [currentView]),
    fileFilter: (file: CustomFileData | null) => {
      return (!file?.locked && user?.role?.endsWith("admin")) || user?.role === "editor";
    }
  }

  const CustomCopyDownloadLinkAction: FileAction = {
    id: "copy_download_link",
    button: { name: "Copy Download Link", contextMenu: true, icon: ChonkyIconName.copy },
    requiresSelection: true,
    fileFilter: useCallback(
      (file: CustomFileData | null) => !!file && !file.isDir,
      []
    ),
    effect: async (data: any) => {
      const file = data.state.selectedFilesForAction![0];
      if (!api || !file) return;
      const downloadUrl = api.getDownloadUrl(file , false);
      try {
        await navigator.clipboard.writeText(downloadUrl);
        toast.success("Download link copied to clipboard");
      } catch (error) {
        toast.error("Failed to copy download link");
      }
    },
  };

  const CustomCopyFileIdAction: FileAction = {
    id: "copy_file_id",
    button: { name: "Copy File Id", contextMenu: true, icon: ChonkyIconName.copy },
    requiresSelection: true,
    fileFilter: useCallback(
      (file: CustomFileData | null) => !!file && selectedFiles.size === 1,
      [selectedFiles]
    ),
    effect: async (data: any) => {
      const file = data.state.selectedFilesForAction![0];
      if (!file) return;
      try {
        await navigator.clipboard.writeText(file.id);
        toast.success("File Id copied to clipboard");
      } catch (error) {
        toast.error("Failed to copy File Id");
      }
    },
  };

  const CustomMakeDefaultAction: FileAction = {
    id: "make_default",
    button: { name: "Make Default", contextMenu: true },
    requiresSelection: true,
    customVisibility: showMakeDefault,
  };

  const CustomMakePremiumAction: FileAction = {
    id: "make_premium",
    button: { name: "Make Premium", contextMenu: true },
    requiresSelection: true,
    customVisibility: showMakePremium,
  };

  const CustomMakeFreeAction: FileAction = {
    id: "make_free",
    button: { name: "Make Free", contextMenu: true },
    requiresSelection: true,
    customVisibility: showMakeFree,
  };

  const CustomUpdateThumbnailAction: FileAction = {
    id: "update_thumbnail",
    button: { name: "Update Thumbnail", contextMenu: true, icon: ChonkyIconName.update },
    requiresSelection: true,
  };

  const customRestoreAction: FileAction = {
      id: "restore",
      button: {
          name: "Restore",
          contextMenu: true,
          toolbar: true,
          icon: (ChonkyIconName as any).restore
      },
      requiresSelection: true,
      customVisibility: useCallback(() => {
          return currentView === 'trash' ? CustomVisibilityState.Default : CustomVisibilityState.Hidden;
        }, [currentView])
    };

    const CustomCreateFolder: FileAction = {
      id: "create_new_folder",
      button: { name:"Create Folder", contextMenu: true, icon: ChonkyIconName.folderCreate},
      customVisibility: () => {
        return selectedFiles.size === 0 ? CustomVisibilityState.Default : CustomVisibilityState.Hidden
      }
    }

  const getRecentFiles = useCallback(() => {
    try {
      return JSON.parse(localStorage.getItem(RECENT_FILES_KEY + "_" + user?.id) || '[]');
    } catch (error) {
      console.error('Error getting recent files:', error);
      return [];
    }
  }, []);

  const restoreFiles = useCallback(
    async (files: CustomFileData[]) => {
      if (!api) return;
      const toastId = "restoring";
      toast.loading("Restoring files...", { id: toastId });
      let successCount = 0;
      const errors: string[] = [];

      await Promise.all(
        files.map(async (file) => {
          try {
            const { data, error } = await api.restoreFile(file);
            if (error || !data) {
              console.error(`Failed to restore ${file.name}:`, error);
              errors.push(file.name);
            } else {
              successCount++;
            }
          } catch (e) {
            console.error(`Exception during restoration of ${file.name}:`, e);
            errors.push(file.name);
          }
        })
      );

      await refreshFileMap(true);

      const fileWord = successCount > 1 ? "files" : "file";
      const errorFileWord = errors.length > 1 ? "files" : "file";

      if (errors.length === 0) {
        toast.success(`Selected files restored successfully.`, { id: toastId });
      } else if (successCount > 0) {
        toast.error(
          `Restored ${successCount} ${fileWord}, but failed to restore ${errors.length} ${errorFileWord}`,
          { id: toastId, duration: 5000 }
        );
      } else {
        toast.error(`Failed to restore selected files`, { id: toastId, duration: 5000 });
      }
    },
    [api, refreshFileMap]
  );

  const CustomFileInfoAction: FileAction = {
    id: "file_info",
    button: { 
      name: "File Info", 
      contextMenu: true,
      icon: ChonkyIconName.fileInfo
    },
    fileFilter: useCallback(
      (file: CustomFileData | null) => {
        return file?.isDir === false && selectedFiles.size === 1;
      },
      [selectedFiles]
    ),
    requiresSelection: true,
  };

  const CustomExportCsvAction: FileAction = {
    id: "export_csv",
    button: { name: "Export as CSV", contextMenu: true, icon: ChonkyIconName.download },
    requiresSelection: true,
    fileFilter: useCallback(
      (file: CustomFileData | null) => {
        return file?.isDir === true && selectedFiles.size === 1;
      },
      [selectedFiles]
    ),
    customVisibility: showExportCsv,
  };

  return {
    fileMap,
    currentPath,
    setCurrentPath,
    deleteFiles,
    duplicateFile,
    moveFiles,
    createFolder,
    uploadFiles,
    downloadFiles,
    previewFile,
    searchFiles,
    getFile,
    renameFile,
    toggleFileLock,
    updateFilePermissions,
    CustomDownloadAction,
    CustomViewAction,
    CustomEditAction,
    CustomEditTagsAction,
    CustomRenameAction,
    CustomShareAction,
    CustomOpenFolderAction,
    customLockFileAction,
    customUnlockFileAction,
    customDeleteAction,
    customOverrideTagsAction, 
    CustomUpdateThumbnailAction,
    customDuplicateAction,
    customEditFileInfoAction,
    CustomOpenInPlaygroundAction,
    CustomCopyDownloadLinkAction,
    CustomCopyFileIdAction,
    CustomCreateFolder,
    CustomPermanentDeleteAction,
    dragging,
    updateTags,
    updateLayers,
    customModifyLayersAction,
    customSetCategoryAction,
    updateCategory,
    CustomMakeDefaultAction,
    updateDefault,
    updatePremium,
    CustomMakeFreeAction,
    CustomMakePremiumAction,
    updateThumbnail,
    getRecentFiles,
    addToRecentFiles,
    restoreFiles,
    customRestoreAction,
    CustomFileInfoAction,
    CustomEditDefaultSettingsAction,
    CustomExportCsvAction,
    clear3DConfig,
    customClear3dAction,
    updateNote,
    updateDescription,
    refreshFileMap,
    hasMore,
    setHasMore,
  };
};

export const useFiles = () => {
  const { searchResults, searchString, loading, fileMap, currentPath, currentView } = useBrowser();
  const { api, config , user } = useUser();

  return useMemo(() => {
    if (Object.keys(fileMap).length === 0) return [null];
    let files;

    if (searchString) {
      files = [...searchResults];
    } else {
      if (currentView === 'trash') {
        files = Object.values(fileMap).filter((file) => 
          !file.deleted && file.deleted_by && 
          //don't show any file that has a parent folder that's deleted, show the parent folder instead
          !Object.values(fileMap).find((f) => file.path.includes(f.id) && f.deleted_by)
        );
      } else {
        files = Object.values(fileMap).filter(
          (file) => file.path === joinPaths(api?.basePath ?? "", currentPath) && !file.deleted_by
        );
      }

    }

    files = files.filter((f) => f?.id && !f?.tags?.includes(CustomAssetsBaseFolderTag) && f?.id !== user?.meta?.assets);

    if (loading) files = [...files, null];
    
    return files;
    //only update when searchResults or path changes, or when loading is flagged to prevent flickering
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPath, searchResults, loading, currentView, fileMap, searchString, api?.basePath, config]);
};

export const useFolderChain = (getFile: any): FileArray => {
  const { currentPath, fileMap, currentView, searchString  } = useBrowser();
  const { api } = useUser();

  return useMemo(() => {
    const folderChain: FileArray = [{
      name: "root",
      id: "/",
      isDir: true,
      view: currentView,
      searchString: searchString || undefined
    }];

    if(currentView === "trash") {
      return folderChain;
    }


    const path = currentPath.split("/");

    path.forEach((folderId) => {
      if (folderId === "" || api?.basePath.includes(folderId)) return;
      const folder = fileMap[folderId];
      if (folder) {
        folderChain.push(folder);
        return;
      } else {
        folderChain.push(null);
        getFile(folderId);
      }
    });

    return folderChain;
  }, [api?.basePath, currentPath, fileMap, getFile, currentView, searchString]);
};

export const useFileActionHandler = (
  deleteFiles: (filesIds: CustomFileData[], permanent: boolean) => void,
  moveFiles: (files: CustomFileData[], newParentId: string) => void,
  createFolder: (folderName: string) => void,
  downloadFiles: (files: CustomFileData[]) => void,
  updateTags: (file: CustomFileData | CustomFileData[], tags: string) => void,
  renameFile: (file: CustomFileData, newName: string) => void,
  updateFilePermissions: (file: CustomFileData, permissions: Partial<fileMetaData>) => void,
  toggleFileLock: (file: CustomFileData) => void,
  updateLayers: (file: CustomFileData, tags: string) => void,
  updateCategory: (files: CustomFileData[], categoryName: string) => void,
  updateDefault: (file: CustomFileData) => void,
  updatePremium: (files: CustomFileData[]) => void,
  updateThumbnail: (files: CustomFileData[]) => void,
  duplicateFile: (file: CustomFileData, newName: string) => void,
  addToRecentFiles: (file: CustomFileData) => void,
  restoreFiles: (files: CustomFileData[]) => void,
  clear3DConfig: (file: CustomFileData) => Promise<void>,
) => {
  const { setSearchString, setDragging, setSelectedFiles, setIsSideBarOpen , currentView } = useBrowser();
  const navigate = useNavigate();
  const { openModal, openPrompt, openConfirm } = useModal();

  return useCallback(
    async (data: ChonkyFileActionData) => {
      switch (data.id) {
        case ChonkyActions.ChangeSelection.id: {
          setSelectedFiles(data.payload.selection);
          break;
        }
        case ChonkyActions.OpenFiles.id: {
          const { targetFile, files } = data.payload;
          const fileToOpen = targetFile ?? files[0];
          if (!fileToOpen) return;

          if(currentView === "trash"){
            if (await openConfirm("Are you sure you want to restore this files?")) {
              await restoreFiles(data.state.selectedFilesForAction!);
            }
            break;
          }

          if (isDirectory(fileToOpen)) {
            setSearchString("");
            if (fileToOpen.id === "/") {
              navigate("../folders/")
              return;
            }
            navigate(`../folders/${fileToOpen.id}` , {replace : true})
              // setCurrentPath(`${fileToOpen.path}${fileToOpen.id}/`);
            return;
          } else {
            addToRecentFiles(fileToOpen);
            openModal("PREVIEW", { file: fileToOpen , updateFilePermissions });
          }
          break;
        }
        case ChonkyActions.DeleteFiles.id: {
          openModal("DELETE", {file: data.state.selectedFilesForAction!});
          break;
        }
        case ChonkyActions.MoveFiles.id: { // todo: replace openModal with openConfirm and openPrompt wherever possible
          if (await openConfirm("Are you sure you want to move these files?")) {
            moveFiles(data.payload.files, data.payload.destination.id);
          }
          break;
        }
        case ChonkyActions.CreateFolder.id: {
          openModal("CREATE_FOLDER")
          break;
        }
        case "create_new_folder": {
          openModal("CREATE_FOLDER")
          break;
        }
        case ChonkyActions.DownloadFiles.id: {
          downloadFiles(data.state.selectedFilesForAction!);
          break;
        }
        case ChonkyActions.StartDragNDrop.id: {
          setDragging(true);
          break;
        }
        case ChonkyActions.EndDragNDrop.id: {
          setDragging(false);
          break;
        }
        case "edit_tags" as any: {
          openModal("EDIT_TAGS", { file: data.state.selectedFilesForAction![0], updateTags });
          break;
        }
        case "override_tags" as any: {
          if (await openConfirm("This will clear all tags for selected items, are you sure you want to continure?")) {
            openModal("EDIT_TAGS", {
              file: {tags: ""}, updateTags: (file: CustomFileData, tags: string) => {
                updateTags(data.state.selectedFilesForAction!, tags);
              },
            });
          }
          break;
        }
        case "update_thumbnail" as any: {
          if (await openConfirm("Are you sure you want to update the thumbnails for these files?")) {
            updateThumbnail(data.state.selectedFilesForAction!);
          }
          break;
        }
        case "modify_layers" as any: {
          const selectedFile = data.state.selectedFilesForAction![0];
        
          // Open the modal to edit layers
          openModal("EDIT_LAYERS", {
            file: selectedFile,
            updateTags: async (file: CustomFileData, tags: string) => {
              await updateLayers(selectedFile, tags);
            },
          });
          break;
        } case "edit_file_info" as any: {
          const selectedFile = data.state.selectedFilesForAction![0];
        
          // Open the modal to edit info
          openModal("EDIT_FILE_INFO", {
            file: selectedFile,
            updateTags: async (file: CustomFileData, tags: string) => {
              await updateTags(selectedFile, tags);
            },
          });
          break;
        } case "set_category" as any: {
          const selectedFiles = data.state.selectedFilesForAction!;
          openPrompt("Enter category name", { title: "Set Category",
            placeholder: "Category name",
            defaultValue : selectedFiles[0].tags?.split(",").find((tag : string) => tag.startsWith("_category:"))?.replace("_category:" , "")
           }).then(async (categoryName) => {
            if (categoryName === null) return;
            if (categoryName) {
              await updateCategory(selectedFiles, categoryName);
            } else {
              //delete category
              await updateCategory(selectedFiles, "");
            }
          });
          break;
        } case "make_default" as any: {
          const selectedFile = data.state.selectedFilesForAction![0];
          if (await openConfirm("Are you sure you want to make this file the default for its category?")) {
            await updateDefault(selectedFile);
          }
          break;
        } case "make_premium" as any: {
          if (await openConfirm("Are you sure you want to make these files premium?")) {
            await updatePremium(data.state.selectedFilesForAction!);
          }
          break;
        } case "make_free" as any: {
          if (await openConfirm("Are you sure you want to make these files free?")) {
            await updatePremium(data.state.selectedFilesForAction!);
          }
          break;
        }
        case "rename" as any: {
          console.log("rename file");
          openModal("RENAME", {file: data.state.selectedFilesForAction[0]})
          break;
        }
        case "permanent_delete" as any: {
          openModal("DELETE", {file: data.state.selectedFilesForAction! , permanent: true});
          break;
        }
        case "unlock" as any:
        case "lock" as any: {
          if (await openConfirm(`Are you sure you want to ${data.id} these files?`)) {
            toggleFileLock(data.state.selectedFilesForAction![0]);
          }
          break;
        }
        case "share" as any: {
          console.log("share file");
          openModal("SHARE", {
            file: data.state.selectedFilesForAction![0],
            updateFilePermissions,
            filePermissions: data.state.selectedFilesForAction![0].meta?.permissions,
          });
          break;
        }
        case "duplicate" as any: {
          const selectedFile = data.state.selectedFilesForAction![0];
          //prompt for new name
          openPrompt("Enter new name without extension", { title: "Duplicate File", placeholder: "new name" }).then((newName) => {
            if (newName === null) return;
            if (newName) {
              duplicateFile(selectedFile, newName);
            }
          });
          break;
        }
        case "clear_3d" as any: {
          const selectedFile = data.state.selectedFilesForAction![0];
          if(await openConfirm("Are you sure you want to clear the 3D configuration for this file?")) {
            await clear3DConfig(selectedFile);
          }
          break;
        }
        case "export_csv" as any: {
          const selectedFolder = data.state.selectedFilesForAction![0];
          openModal("EXPORT_CSV", { folder: selectedFolder });
          break;
        }
        case "restore" as any: {
          if (await openConfirm("Are you sure you want to restore these files?")) {
            await restoreFiles(data.state.selectedFilesForAction!);
          }
          break;
        }
        case "file_info" as any: {
          setIsSideBarOpen(true);
          break;
        }
        default:
          break;
      }

      // console.log(data);
    },
    [setSelectedFiles, currentView, openConfirm, restoreFiles, setSearchString, navigate, addToRecentFiles, openModal, updateFilePermissions, moveFiles, downloadFiles, setDragging, updateTags, updateThumbnail, updateLayers, openPrompt, updateCategory, updateDefault, updatePremium, toggleFileLock, duplicateFile, clear3DConfig, setIsSideBarOpen]
  );
};

export const isDirectory = (file: CustomFileData) => {
  if (file.isDir) return true;
  return file.meta?.type && file.meta.type === "folder";
};

export const usePreviewNavigation = (files: CustomFileData[], initialFile: CustomFileData) => {
  const { currentView } = useBrowser();
  const [currentFile, setCurrentFile] = useState<CustomFileData | null>(initialFile);

  const [currentIndex, setCurrentIndex] = useState(0);
  // Filter files based on currentView and same directory
  const filteredFiles = useMemo(() => {
    if (!initialFile?.path) return [];
    
    const filtered = files.filter(file => {
      if (file.isDir) return false;
      
      if (file.path !== initialFile.path) return false;
      
      return currentView === 'trash' ? file.deleted : !file.deleted;
    });
    setCurrentIndex(filtered.findIndex(f => f.id === initialFile?.id) + 1)
    return filtered;
  }, [initialFile, files, currentView]);




  const onNext = useCallback(() => {
      setCurrentIndex(prev => {
        const i = Math.min(filteredFiles.length, prev + 1);
        setCurrentFile(filteredFiles[i - 1]);
        return i;
      });
  }, [filteredFiles]);

  const onPrevious = useCallback(() => {
    setCurrentIndex(prev => {
      const i = Math.max(1, prev - 1);
      setCurrentFile(filteredFiles[i - 1]);
      return i;
    });
  }, [filteredFiles]);

  // const currentFile = filteredFiles[currentIndex - 1] || null;
  const totalFiles = filteredFiles.length;

  return {
    currentIndex,
    totalFiles,
    onNext,
    onPrevious,
    currentFile,
  };
};
