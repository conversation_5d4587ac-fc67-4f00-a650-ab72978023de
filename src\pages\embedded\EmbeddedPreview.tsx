import { useMemo } from "react";
import { useSearchParams } from "react-router-dom";

export default function EmbeddedPreview() {

  const [params,] = useSearchParams()

//   const { width, height, ...settings } = {...params}

  const settings = useMemo(() => {
    const obj: { [key: string]: any } = {};
    params.keys().forEach((key) => {
        obj[key] = params.get(key);
    });
    return obj;
  } , [params]);

  const iframeSrc = useMemo(() => generateEmbeddingUrl({
    baseUrl: window.location.href.replace(/\/embedded\/preview/, "/embedded"),
    settings : settings,
  }), [settings]);

  return (
    <>
    <head>
        <title>Embedded Preview - iJewel Design </title>
        <meta
          name="description"
          content="Embedded 3D Jewelry Designs Preview"
        />
        <meta property="og:title" content="Embedded Preview - iJewel Design" />
        <meta property="og:description" content="Embedded 3D Jewelry Designs Preview" />
        <meta name="keywords" content="3D Jewelry Designs, Embedded Viewer, iJewel Design" />
        <meta property="og:image" content="/images/3dconfigurator.webp" />
    </head>
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        width: "100%",
        height: "calc(100dvh)",
        overflow: "auto",
      }}
    >
      <iframe
        xr-spatial-tracking
        execution-while-out-of-viewport
        execution-while-not-rendered
        web-share
        allow="autoplay; fullscreen; xr-spatial-tracking; web-share"
        width={(params.get("width") as string) ?? "100%"}
        height={(params.get("height") as string) ?? "100%"}
        src={iframeSrc}
      />
    </div>
    </>
  );
}


const generateEmbeddingUrl = ({
    baseUrl = window.location.origin,
    settings,
  }: {
    baseUrl: string;
    settings: { [key: string]: any };
  }) => {
    const url = new URL(baseUrl);
    const newSetting = { ...settings };
  
    // Iterate over the object and append each key-value pair as a query parameter.
    Object.entries(newSetting).forEach(([key, value]: [string, any]) => {
      if (
        value !== null &&
        value !== undefined &&
        defaultSetting[key as keyof typeof defaultSetting] !== value &&
        key !== "width" && key !== "height"  
      ) {
        // Exclude null or undefined values.
        url.searchParams.append(key, value);
      }
    });


  
    return url.toString();
};

const defaultSetting = {
    isTitle: true,
    isRemoveLogo: false,
    isRemoveLogoLink: false,
    isRemoveLoadingBgImg: true,
    isShowPrompt: true,
    isTurntableAnimation: false,
    isAutoplay: false,
    isTransparentBackground: false,
    isConfigurator: true,
    isEnabledZoom: true,
    isShare: true,
    isShowModelInfo: false,
    isQuality: true,
    isResetView: true,
    isRotateCamera: true,
    isPlayCameraViews: true,
    isPlayAnimations: true,
    isFitObject: true,
    isFullScreen: true,
};