import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <rect width="22" height="22" rx="11" fill="white"/>
        <mask id="mask0_95_2813" style={{maskType: "alpha",}} maskUnits="userSpaceOnUse" x="4" y="4" width="14"
              height="14">
            <rect x="4.40002" y="4.39999" width="13.2" height="13.2" fill="#D9D9D9"/>
        </mask>
        <g mask="url(#mask0_95_2813)">
            <path
                d="M12.3097 16.3096L7.00012 11L12.3097 5.69043L13.0903 6.47102L8.56116 11L13.0903 15.529L12.3097 16.3096Z"
                fill="#373737"/>
        </g>
    </svg>
)
export default SvgComponent

