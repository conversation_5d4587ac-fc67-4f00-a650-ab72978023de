import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg width="29" height="29" viewBox="0 0 29 29" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path fillRule="evenodd" clipRule="evenodd" d="M0.5 14.8784H1.98852C8.52328 15.244 13.758 20.4776 14.1241 27.011V28.5H0.5V14.8784ZM0.5 14.1216V0.5H14.1387L14.1445 1.25676C14.1445 8.12752 8.76572 13.7424 1.98852 14.1216H0.5ZM14.8605 0.504526V1.25676C14.8605 8.12752 20.2393 13.7424 27.0165 14.1216H28.5C28.3026 6.68348 22.3027 0.691222 14.8605 0.504526ZM28.5 14.8784H27.0165C20.4818 15.244 15.2471 20.4775 14.881 27.011V28.495C22.3137 28.2979 28.3028 22.3098 28.5 14.8784Z"
        fill={props.color ?? "url(#paint0_linear_203_1370)"}/>
        <defs>
            <linearGradient id="paint0_linear_203_1370" x1="23" y1="3.5" x2="-1.4502e-06" y2="28" gradientUnits="userSpaceOnUse">
                <stop stop-color="#FCC253"/>
                <stop offset="0.333333" stop-color="#FFE0A4"/>
                <stop offset="0.666667" stop-color="#B5821F"/>
                <stop offset="1" stop-color="#FFD788"/>
            </linearGradient>
        </defs>
    </svg>
)
export default SvgComponent