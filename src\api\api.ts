import { FileData } from "chonky";
import { fileMetaData } from "../components/shared/types";
import { getIdFromPath, is3dFile, joinPaths, sanitizeFileName } from "../components/shared/util";
import { downloadBlob } from "../utils/downloadBlob";
import { CustomAssetsBaseFolderTag } from "../components/shared/variables";
import { generateRandomString } from "../utils/generateRandomString";
import { getDuplicateName } from "../utils/getDuplicateName";
import { extractFilenameFromUrl } from "../utils/fileUtils";
import { OldDriveClient, OldDriveFile } from "./oldDriveClient";
export interface CustomFileData extends FileData {
  parentId?: string;
  meta?: fileMetaData;
  tags?: string;
  file?: string;
  color?: string;
  url?: string;
  notes?:string;
  locked?: boolean;
  isDir?: boolean;
  config?:{
    modelUrl?: string;
    posterUrl?: string;
    logo?: string;
    name?: string;
    description?: string;
    textEditorState?: any;
    basePath?: string;
    sceneConfig?: { [key: string]: any };
    cameraConfig?: any;
    materialConfig?: any;
    configuratorConfig?: any;
    plugins?: any;
    // sceneSettings?: string; // vjson url
    tags?: string[];
    category?: string;
    version?: string;
    slug?:string;
    currentPose?: { position: number[]; rotation: [number, number, number, any] } | null;
    glbUpdated?: boolean;
    isPlayground?: boolean;
    [key: string]: any;
  }
}
type CustomFileDataFileUnion = Omit<CustomFileData, 'file'> & {
  file?: File | string;
};

// const base = (import.meta as any).env.VITE_API_URL as string;

const propsList = ["id", "updated", "path", "name", "thumb", "file", "meta", "tags", "deleted","deleted_by", "locked" , "created_by"] as const
type TPropsList = typeof propsList[number];
export type TFileData = Record<TPropsList, any>&{config?: any, notes?: any};
export type TDriveConfigData = {id: string, val: string};

export interface SyncProgress {
  stage: 'authenticating' | 'fetching' | 'creating_folder' | 'syncing' | 'complete';
  message: string;
  progress: number;
  currentItem?: string;
  error?: string;
  profile?: {
    name?: string;
    email?: string;
    avatar?: string;
  };
}




export interface AuthResult {
  success: boolean;
  token?: string;
  accessToken?: string;
  user?: any;
  error?: string;
}

export interface FetchResult {
  success: boolean;
  data?: any[];
  error?: string;
}

export class FileAPI {
  private static instance: FileAPI;
  private baseApiUrl: string = `${this.getBase()}raw/v1/`;
  private baseDownloadUrl: string = `${this.getBase()}files/files/`;
  fileApi : string = ""

  private token: string | null = null;
  user: any = null;
  basePath: string = '/';
  private stopDesignSync: boolean = false;
  private stopInstanceSync: boolean = false;

  private constructor() {
    this.checkLogin();
  }

  public static getInstance(): FileAPI {
    if (!FileAPI.instance) {
      FileAPI.instance = new FileAPI();
    }
    return FileAPI.instance;
  }

  public getToken() {
    return this.token;
  }
  public getBasePath(){
    return this.fileApi || this.baseDownloadUrl
  }

  private setCookie(name: string, value: string, expires: number) {
    const expiresDate = new Date(expires * 1000).toUTCString();
    document.cookie = `${this.getBaseName()}-${name}=${value}; expires=${expiresDate}; path=/`;
  }

  private deleteCookie(name: string) {
    document.cookie = `${this.getBaseName()}-${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/`;
  }

  private getCookie(name: string): string | undefined {
    const nameEQ = `${this.getBaseName()}-${name}=`;
    const cookie = document.cookie
      .split(";")
      .map((cookie) => cookie.trim())
      .find((cookie) => cookie.startsWith(nameEQ));
    return cookie?.split("=")[1];
  }

  getDriveConfig = async () : Promise<{ data: TDriveConfigData[] | null; error: any }> => { 
    const queryParams = new URLSearchParams({ select: "id,val" });
    if(!this.getBaseName()) return { data: null, error: "Base name not found" };
    const { data, error } = await this.fetch("drive_config/select?" + queryParams , undefined, false);
    return { data : data as TDriveConfigData[], error };
  }

  private async checkLogin() {
    const tokenCookie = this.getCookie("token");
    const userCookie = this.getCookie("user");
    if (tokenCookie && userCookie) {
      this.setAuthData(tokenCookie);
    }else{
      this.logOut();
    }
  }

  parseJwt(token: string) {
    try {
      return JSON.parse(atob(token.split(".")[1]));
    } catch (e) {
      return null;
    }
  }

   private async setAuthData(token: string): Promise<{error : string | null}> {
    this.token = token;

    // Extract expiry from JWT token
    const jwtPayload = JSON.parse(atob(token.split(".")[1]));
    const expiry = jwtPayload.exp;
    this.user = jwtPayload;
    this.user.email = this.user.sub;
    this.user.role = this.user?.aud?.[0];

    if(this.user.meta.base) {
      this.basePath = this.user.meta.base;
      // const {data , error} = await this.getFile(this.user.meta.base);
      // if(data && !error) {
      //   this.basePath = joinPaths(data.path, data.id);
      //   console.log("Setting base path", this.basePath);
      // }else{
      //   console.error("Error setting base path", error);
      //   return {error: "Error setting base path"};
      // }
    }else{
      this.basePath = '';
    }

    // Set cookies
    if (token) this.setCookie("token", token, expiry);
    if (this.user) this.setCookie("user", JSON.stringify(this.user), expiry);
    return {error : null};
  }

  private async fetch<T>(
    endpoint: string,
    options?: RequestInit,
    authenticated: boolean = true,
    processData?: (data: any) => T,
    signal?: AbortSignal,
    useBaseApi = true
  ): Promise<{ data: T|null; error: any, errorData?: any }> {
    const headers: any = {
      ...options?.headers,
    };

    if (authenticated) {
      if (!this.token) {
        return { data: null, error: "Not authenticated" };
      }
    }
    headers.Authorization = `Bearer ${this.token}`;

    let response;
    try {
      response = await fetch(`${useBaseApi ? this.baseApiUrl : ""}${endpoint}`, { ...options, headers , signal });
    } catch (fetchError) {
      return { data: null, error: fetchError };
    }

    let data;
    try {
      data = await response.json();
    } catch (error) {
      return { data: null, error: `Failed to parse response` };
    }

    if (!response.ok) {
      const errorMsg = data?.error || data?.message || response.statusText || "Unknown error";
      return { data: null, error: errorMsg , errorData : data.data};
    }

    if (data && (data.error || data.message)) {
      const errorMsg = data.error || data.message;
      return { data: null, error: errorMsg };
    }

    if (processData) {
      data = processData(data);
    }

    return { data, error: null };
  }

  //process file data for frontend
  private processFile(data: TFileData): CustomFileData|null {
    try{
      if(!data) return null
      const meta = typeof data.meta === "string" ? JSON.parse(data.meta) : data.meta
      const isDir = meta?.type === "folder"
      const thumbnailUrl = data.thumb && !data.thumb.startsWith("thumb_") ? this.getDownloadUrl({ id: data.id, file: data.thumb }) : undefined
      return {
        ...data,
        meta: meta,
        config: typeof data.config === "string" ? JSON.parse(data.config) : data.config,
        isDir: isDir,
        size: meta?.size,
        // format and hide year
        modDate: data.updated,
        thumbnailUrl: thumbnailUrl,
        color: (isDir || !thumbnailUrl) ? undefined: "transparent",
        icon: thumbnailUrl,
        locked: !!data.locked,
      } as CustomFileData;
    } catch (error) {
      console.error("Error processing file", data, error);
      return null
    }
  }

  private processFiles(data: TFileData[]) {
    if (Array.isArray(data)) {
      return data.map((file) => this.processFile(file)).filter((file) => file) as CustomFileData[];
    // } else if (data && typeof data === "object" && data.id) {
    //   return this.processFile(data);
    } else throw new Error("Invalid data");
    // return data;
  }

  async login(email: string, password: string): Promise<{ error: any; user: any }> {
    if (this.token && this.user) {
      // Already logged in
      return { error: null, user: this.user };
    }

    const { data, error } = await this.fetch<{token: string}>(
      `users/auth/login-password`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password }),
      },
      false
    );

    if (error || !data?.token) {
      return { error: error || 'Unable to login', user: null };
    }

    const {error: error2} = await this.setAuthData(data.token);

    return { error: error2, user: this.user };
  }

  async logOut() {
    this.user = null;
    this.token = null;
    this.deleteCookie("token");
    this.deleteCookie("user");
  }

  async signUp(params: { username: string; email: string; name: string; password: string; passwordConfirm: string }, sendConfirm = true): Promise<{ error: any; user: any }> {
    if (this.token && this.user) {
      // Already logged in
      return { error: null, user: this.user };
    }

    const { data, error , errorData } = await this.fetch<{token: string}>(
      `users/auth/sign-up`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({...params,
          role: 'guest',
          meta: '{"base": null}'
        }),
      },
      false
    );

    if (error || !data?.token) {
      if(errorData?.error?.includes?.("UNIQUE constraint failed")){
        return { error: "User already exists", user: null };
      }
      return { error: error || 'Unable to sign up', user: null };
    }

    await this.setAuthData(data.token);

    if(sendConfirm) {
      const { error: error2  } = await this.requestUserVerification();
      console.log("Request verification", error2);
      if (error2) {
        return { error: error2, user: this.user };
      }
    }
    return { error: null, user: this.user };
  }

  async requestUserVerification(): Promise<{ error: any }> {
    const { error } = await this.fetch(
      "users/auth/request-verification",
      {
        method: "POST",
      }
    );

    return { error };
  }

  async changePassword(params: { password: string; passwordConfirm: string, passwordCurrent: string }): Promise<{ error: any }> {
    const { error } = await this.fetch(
        "users/auth/change-password",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({...params}),
        },
        true,
    );

    return { error };
  }


  async requestPasswordReset(params: { email: string; }): Promise<{ error: any; }> {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { data, error } = await this.fetch(
      `users/auth/request-password-reset`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({...params}),
      },
      !!this.token, // works when both auth and not auth
    );

    if (error) {
      return { error };
    }

    // this.setAuthData(data.token);

    return { error: null };
  }

  async confirmPasswordReset(params: { token: string, password: string, passwordConfirm: string }): Promise<{ error: any; user: any; }> {
    const { data, error } = await this.fetch<{token: string}>(
      `users/auth/confirm-password-reset`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({...params}),
      },
      !!this.token, // works when both auth and not auth
    );

    if (error || !data?.token) {
      return { error: error || 'Unable to reset password', user: null };
    }

    await this.setAuthData(data.token);

    return { error: null, user: this.user };
  }

  async requestEmailVerification(): Promise<{ error: any; }> {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { data, error } = await this.fetch(
        `users/auth/request-verification`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
        },
        true,
    );

    if (error) {
      return { error };
    }

    // this.setAuthData(data.token);

    return { error: null };
  }

  async confirmEmailVerification(params: { token: string }): Promise<{ error: any; user: any; }> {
    this.token = params.token;
    //skip verification if email is already verified

    const parsedToken = this.parseJwt(this.token)
    if(parsedToken && parsedToken.verified){
      return { error: "Email already verified", user: null };
    }

    const { data, error } = await this.fetch<{token: string}>(
      `users/auth/confirm-verification`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({...params}),
      },
      true, // right now only setup to work with auth.
    );

    if (error || !data?.token) {
        this.token = null;
        return { error: error || 'Unable to verify email', user: null };
    }

    await this.setAuthData(data.token);

    return { error: null, user: this.user };
  }

  async setupUser(): Promise<{ error: any; user: any; }> {
    const { data, error } = await this.fetch<{token: string}>(
      `${this.getBase()}drive/v1/setup-drive`,
      {
        method: "POST",
        // headers: {"X-Authorization": this.token},
      },
      true,undefined , undefined, false
    );

    if (error || !data?.token) {
        return { error: error || 'Unable to setup user', user: null };
    }

    const user = this.parseJwt(data.token)
    if(!user) {
      return { error: "Invalid user", user: null };
    }
    await this.setAuthData(data.token);

    //create base folder for user path is root and id is meta.base without /
    //base will be the path in the form /path/id/
    if(!this.user?.meta?.base){
      this.logOut();
      return { error: "Base folder not set in meta", user: this.user };
    }
    const baseId = getIdFromPath(this.user.meta.base);

    const { data: baseFolder, error: error2 } = await this.createDirectory(this.user.user, "/" , baseId, false);
    if(error2 || !baseFolder) {
      this.logOut();
      return { error: error2 || 'Unable to setup user', user: this.user };
    }

    if(!this.user?.meta?.assets){
      return { error: "Assets folder not set in meta", user: this.user };
    }
    //create custom assets folder
    const { data: customAssetsFolder, error: error3 } = await this.createDirectory("cst_assets_" + this.user?.user, this.user.meta.base, this.user.meta.assets , false , CustomAssetsBaseFolderTag);
    if(error3 || !customAssetsFolder) {
      return { error: error3 || 'Unable to create custom assets folder', user: this.user };
    }

    return { error: null, user: this.user };
  }

  async listUsers(): Promise<{ data: any; error: any }> {
    const queryParams = new URLSearchParams({ offset: "0", limit: "1000", where: "", select: "*" , order: "-created" });
    const { data, error } = await this.fetch(`users/select?` + queryParams);
    return { data, error };
  }

  async getUser(id: string): Promise<{ data: any; error: any }> {
    const queryParams = new URLSearchParams({ select: "*", where : `id=${JSON.stringify(id)}` });
    const { data, error } = await this.fetch(`users/select?` + queryParams , undefined, false);
    return { data, error };
  }

  async viewUser(id: string): Promise<{ data: any; error: any }> {
    const queryParams = new URLSearchParams({ id: id });
    const { data, error } = await this.fetch(`${this.getBase()}drive/v1/user?` + queryParams 
    , undefined, false , undefined, undefined, false);
    return { data, error };
  }
  

  async viewUserName(id: string): Promise<{ data: any; error: any }> {
    const queryParams = new URLSearchParams({ select: "name" });
    const { data, error } = await this.fetch(`users/view/${id}?` + queryParams , undefined, false);
    return { data, error };
  }

  async updateUser(props: { id: string; role?: string, meta?:any }): Promise<{ data: any; error: any }> {
    const formData = new FormData();
    Object.entries(props).forEach(([key, value]) => {
      if (key === "id") return;
      if (key === "meta") {
        formData.append(`setValues.${key}`, JSON.stringify(value));
      } else {
        formData.append(`setValues.${key}`, value as any);
      }
    });
    formData.append("where", `id=${JSON.stringify(props.id)}`);
    formData.append("returning", "id,role");

    const { data, error } = await this.fetch("users/update", {
      method: "POST",
      body: formData,
    });

    return { data, error };
  }

  async getModelsCount(userId?: string): Promise<{ data: any; error: any }> {
    const modelExtensions = ["glb", "gltf", "obj", "fbx", "3ds", "stl","3dm"]; // todo : add all possible extensions

    //count all the models that are insdie the user's base folder and the ones created by the user
    //todo: add count to the api 
    const params = { 
      where: `(created_by=${JSON.stringify(userId || this.user.id)} ${this.user.meta.base ? `| path ~ ${JSON.stringify(this.user.meta.base + '%')}` : ""}) & (file ~ '%.${modelExtensions.join("' | file ~ '%.")}')`,
      select: "name",
    }

    const queryParams = new URLSearchParams(params);
    const { data, error } = await this.fetch(`files/select?` + queryParams , undefined, true);
    return { data, error };
  }

  async listFiles(path: string = "", limit: number = 1000 , offset : number = 0): Promise<{ data: CustomFileData[]; error: any }> {
    const fullPath = joinPaths(this.basePath, path);
    const filter = `path=${JSON.stringify(fullPath)}`;
    const queryParams = new URLSearchParams({
      offset: `${offset}`,
      limit: `${limit}`,
      // perPage: "1000",
      where: filter,
      select: propsList.join(","),
    });
    const { data, error } = await this.fetch(`files/select?${queryParams}`, undefined, true, (data) => {
      const files = this.processFiles(data as TFileData[])
      // Add root folder
      // todo maan add root folder only when we are in the root folder.
      files.push({ id: "/", name: "root", isDir: true });
      return files;
    });

    return { data, error } as { data: CustomFileData[]; error: any };
  }

  async listDeletedFiles(limit: number = 1000, offset: number = 0): Promise<{ data: CustomFileData[]; error: any }> {
    const path = joinPaths(this.basePath, "/deleted/");
    const filter = `path~'${path}%' | deleted_by!=null`;
    const queryParams = new URLSearchParams({
      offset: `${offset}`,
      limit: `${limit}`,
      perPage: "1000",
      where: filter,
      select: propsList.join(","),
    });

    const { data, error } = await this.fetch(`files/select?${queryParams}`, undefined, true, (data) => {
      const files = this.processFiles(data as TFileData[])
      return files;
    });

    return { data, error } as { data: CustomFileData[]; error: any };
  }

  async getFolderChildCount(folderId: string): Promise<{ data: number | null; error: any }> {
    const filter = `path~'%${folderId}%' & deleted_by=null`;
    const queryParams = new URLSearchParams({
      where: filter,
      select: "id", 
      limit: "3000", 
    });

    // Fetch the children
    const { data, error } = await this.fetch<TFileData[]>(`files/select?${queryParams}`, undefined, true);

    if (error) {
      return { data: null, error };
    }

    // Return the count
    const count = Array.isArray(data) ? data.length : 0;
    return { data: count, error: null };
  }

  async getAllNestedChildren(folderId: string): Promise<{ data: CustomFileData[]; error: any }> {
    const filter = `path~'%${folderId}%' & deleted_by=null`;
    const queryParams = new URLSearchParams({
      where: filter,
      select: [...propsList, 'config', 'notes', 'created_by'].join(","),
      limit: "5000",
      offset: "0"
    });

    const { data, error } = await this.fetch<TFileData[]>(`files/select?${queryParams}`, undefined, true);

    if (error) {
      return { data: [], error };
    }

    if (!data || !Array.isArray(data)) {
      return { data: [], error: null };
    }

    const processedFiles = this.processFiles(data);
    return { data: processedFiles, error: null };
  }

  async getFile(fileId: string): Promise<{ data: CustomFileData | null; error: any }> {
    if(!fileId || fileId === "undefined") {
      console.log("File ID is required");
      return { data: null, error: "File ID is required" };
    }
    const queryParams = new URLSearchParams({
      select: [...propsList, 'config'].join(','),
    });
    const { data, error } = await this.fetch(`files/view/${fileId}?${queryParams}`, undefined, false, (data) => this.processFile(data as TFileData));

    return { data, error };
  }

  async getFileInfo(fileId: string): Promise<{ data: CustomFileData | null; error: any }> {
    if(!fileId || fileId === "undefined") {
      console.log("File ID is required");
      return { data: null, error: "File ID is required" };
    }
    const queryParams = new URLSearchParams({
      select: ['id','name','config','notes'].join(','),
    });
    const { data, error } = await this.fetch(`files/view/${fileId}?${queryParams}`, undefined, false, (data) => this.processFile(data as TFileData));

    return { data, error };
  }

  async viewFile (fileId: string): Promise<{ data:( CustomFileData & {defaultConfig?: any}) | null; error: any }> {
    const { data, error } = await this.getFile(fileId);
    if(error || !data) return { data: null, error };

    //check if it's a 3d file
    if(!is3dFile(data) || !data.path){
      return { data: {...data}, error: null };
    }

    //get parents 
    const {data: parents, error :error2} = await this.getParentsByPath(data.path);
    if(error2 || !parents) return { data: null, error : error2 };
    //get default config from nearest parent that has it
    parents.sort((a,b) => b.path!.length - a.path!.length);
    const defaultConfig = parents.find(p => p.config?.isDefault)?.config 
    return { data: {...data, defaultConfig}, error: null };
  }

  async getFileByName(name: string, path: string): Promise<{ data: CustomFileData | null; error: any }> {
    const filter = `name=${JSON.stringify(name)} & path=${JSON.stringify(joinPaths(this.basePath, path))}`;
    const queryParams = new URLSearchParams({
      where: filter,
      select: [...propsList, 'config'].join(','),
    });
    const url = `files/select?${queryParams}`;

    const { data, error } = await this.fetch(url, undefined, true, (data) => this.processFile(data[0]));

    if (error) {
      return { data: null, error };
    }

    return { data, error: null };
  }

  async getFilesBytag(tag: string, parent = "/"): Promise<{ data: CustomFileData[]; error: any }> {
    // todo use JSON.stringify like
    //  ((',' || tags || ',') ~ ('%,' || ${JSON.stringify(tag)} || ',%'))
    const filter = `(tags~'%,${tag}%' | tags~'%${tag},%' | tags='${tag}') & path~'%${parent}%'`;
    const queryParams = new URLSearchParams({
      where: filter,
      select: [...propsList, 'config'].join(','),
    });
    const url = `files/select?${queryParams}`;

    const { data, error } = await this.fetch(url, undefined, true, (data) => this.processFiles(data));

    if (error) {
      return { data: [], error };
    }

    return { data: data as CustomFileData[], error: null };
  }

  async getParentsByPath(path : string): Promise<{ data: CustomFileData[]; error: any }> {
    const parts = path.split("/").filter(Boolean);
    const parents = []
    for (const part of parts) {
      const { data, error } = await this.getFile(part)
      if (error || !data) {
        console.error("Error getting parent", error);
        continue;
      }
      parents.push(data);
    }
    return { data: parents, error: null };
  }


  async createDirectory(name: string, path: string , id? :string , relative = true , tags = "", meta = {}): Promise<{ data: CustomFileData | null; error: any }> {
    const formData = new FormData();
    id && formData.append("values.id", id);
    formData.append("values.name", name);
    formData.append("values.path", joinPaths(relative ? this.basePath : "", path));
    formData.append("values.meta", JSON.stringify({ type: "folder", ...meta }));
    formData.append("values.file", new File(["test"], "file.txt"), "file.txt");
    formData.append("values.tags", tags);
    formData.append("values.thumb", new File(["test"], "thumb.png"), "thumb.png");
    formData.append("values.notes", "");
    formData.append("values.config", JSON.stringify({}));
    // formData.append("values.deleted", null as any);
    formData.append("values.created_by", this.user.id);
    formData.append("returning", [...propsList, 'config'].join(','));

    const { data, error } = await this.fetch(
      "files/insert",
      {
        method: "POST",
        body: formData,
      },
      true,
      (data) => this.processFile(data[0])
    );

    return { data, error };
  }

  async updateFile(file: Partial<CustomFileDataFileUnion>, thumbnailFile?: File, signal?: AbortSignal): Promise<{ data: CustomFileData | null; error: any }> {
    if (!file.id) {
      return { data: null, error: "File ID is required" };
    }else if (file.locked) {
      return { data: null, error: "File is locked" };
    }

    const formData = new FormData();
    Object.entries(file).forEach(([key, value]) => {
      if (key === "id") return;
      if( key === "deleted" && !value) return; // make sure not to set deleted
      else if (key === "config" || key === "meta") {
        formData.append(`setValues.${key}`, JSON.stringify(value));
      } else if (key === "file"){
        if (typeof value === "string") {
          console.error("File is not a file", value);
          return;
        }
        formData.append(`setValues.${key}`, value as any);
        if(!Object.keys(file).includes("meta")){ //change meta.size only if not set in the file object
          formData.append(`setValues.meta`, JSON.stringify({ ...file.meta, size: value.size }));
        }
      } else {
        formData.append(`setValues.${key}`, value as any);
      }
    });
    if (thumbnailFile) formData.append("setValues.thumb", thumbnailFile);
    formData.append("where", `id=${JSON.stringify(file.id)}`);
    formData.append("returning", [...propsList, 'config'].join(','));

    const { data, error } = await this.fetch(
      "files/update",
      {
        method: "POST",
        body: formData,
      },
      true,
      (data) => this.processFile(data[0]),
      signal
    );

    return { data: data, error };
  }

  async uploadFile(
    file: File,
    path: string,
    thumbnailFile?: File,
    fileMetaData?: fileMetaData,
    progressCallback?: any,
    signal?: { abort: () => void },
    tags?: string,
    name?: string,
    config?: any
  ): Promise<{ data: CustomFileData | null; error: any }> {
    return new Promise((resolve, reject) => {
      const data = new FormData();
      data.append("values.created_by", this.user.id);
      data.append("values.file", file);
      data.append("values.path", joinPaths(this.basePath, path));
      data.append("values.name", name ?? file.name);
      data.append("values.meta", JSON.stringify(fileMetaData || { size: file.size }));
      data.append("values.tags", tags || "");
      if (thumbnailFile) data.append("values.thumb", thumbnailFile);
      else data.append("values.thumb", new File(["test"], "thumb.png"), "thumb.png");
      data.append("values.notes", "");
      data.append("values.config", JSON.stringify(config || {}));
      // data.append("values.deleted", "0");
      data.append("returning", [...propsList, 'config'].join(','));

      const xhr = new XMLHttpRequest();

      // Monitor progress events
      if (progressCallback) {
        xhr.upload.addEventListener("progress", (event) => {
          if (event.lengthComputable) {
            const percentComplete = (event.loaded / event.total) * 100;
            progressCallback(file, percentComplete);
          }
        });
      }

      xhr.onreadystatechange = () => {
        if (xhr.readyState === 4) {
          // Request completed
          if (xhr.status === 200) {
            const responseData = JSON.parse(xhr.responseText);
            const processedData = this.processFile(responseData[0]);
            resolve({ data: processedData, error: null });
          } else {
            reject({ data: null, error: xhr.statusText });
            console.log("Error", xhr.statusText);
          }
        }
      };

      // Setup the request
      xhr.open("POST", `${this.baseApiUrl}files/insert`, true);
      if (this.token) xhr.setRequestHeader("Authorization", this.token);

      // Support cancellation
      if (signal) {
        signal.abort = () => {
          xhr.abort();
        };
      }

      try {
        xhr.send(data);
      } catch (err) {
        reject({ data: null, error: err });
      }
    });
  }

  async renameFile(file: CustomFileData, newName: string): Promise<{ data: CustomFileData | null; error: any }> {
    if (!file.id) {
      return { data: null, error: "File ID is required" };
    } else if (file.locked) {
      return { data: null, error: "File is locked" };
    }

    const formData = new FormData();
    formData.append("setValues.name", newName);
    formData.append("where", `id=${JSON.stringify(file.id)}`);
    formData.append("returning", [...propsList, 'config'].join(','));

    const { data, error } = await this.fetch(
      "files/update",
      {
        method: "POST",
        body: formData,
      },
      true,
      (data) => this.processFile(data[0])
    );

    return { data: data, error };
  }

  async deleteFile(file: CustomFileData, permanent?: boolean): Promise<{ data: CustomFileData | null; error: any }> {
    if (!file.id) {
      return { data: null, error: "File ID is required" };
    } else if (file.locked) {
      return { data: null, error: "File is locked" };
    } else if (permanent && !this.user.id) {
      return { data: null, error: "User ID is required" };
    }

    const alreadyInTrashBin = file.path?.includes("/deleted/");

    const randomStr = generateRandomString(8);
    let newPath = file.path?.replace(this.basePath, ""); // remove basepath
    newPath = joinPaths(this.basePath, `/deleted/${randomStr}/${newPath}`); // construct new path

    // First, delete the  file or folder
    const formData = new FormData();
    permanent && formData.append("setValues.deleted", JSON.stringify(Date.now()));
    !alreadyInTrashBin && formData.append("setValues.path", newPath);
    formData.append("setValues.deleted_by", this.user.id);
    formData.append("where", `id=${JSON.stringify(file.id)}`);
    formData.append("returning", "id,created,updated,path,name,thumb,file,config,meta,tags,deleted,deleted_by,locked");

    const { data, error } = await this.fetch(
      "files/update",
      {
        method: "POST",
        body: formData,
      },
      true,
      (data) => this.processFile(data[0])
    );

    if (error || !data || !file.isDir) {
      return { data, error };
    }

    // if it's a folder, update all children paths in a separate call
    const childFormData = new FormData();
    
    permanent && childFormData.append("set.deleted", JSON.stringify(Date.now()));
    childFormData.append("set.deleted_by", JSON.stringify(this.user.id));
    
    const oldPathPrefix = file.path + file.id + "/";
    const newPathPrefix = newPath + file.id + "/";
    !alreadyInTrashBin && childFormData.append("set.path", `replace(path, ${JSON.stringify(oldPathPrefix)}, ${JSON.stringify(newPathPrefix)})`);

    childFormData.append("where", `path~${JSON.stringify("%" + file.id + "%")} ${!permanent ? `& deleted_by=null` : ""}`);
    childFormData.append("returning", "id")

    const { error: childError } = await this.fetch(
      "files/update",
      {
        method: "POST",
        body: childFormData,
      },
      true
    );

    // If updating children failed, revert
    if (childError) {
      console.error("Failed to update children, attempting to revert parent folder:", childError);
      
      const { error: revertError } = await this.restoreFile(data);
      if (revertError) {
        console.error("Failed to revert parent folder change:", revertError);
      }

      return { data: null, error: `Failed to delete children: ${childError}` };
    }

    return { data, error: null };
  }

  async restoreFile(file: CustomFileData): Promise<{ data: CustomFileData | null; error: any }> {
    if (!file.id) {
      return { data: null, error: "File ID is required" };
    }

    //deleted files will have a path like basepath/deleted/randomstring/path/id
    //we need to remove the deleted part and set the path to basepath/path/id
    const originalPath = joinPaths( //use joing paths to remove any duplicate slashes
      (file.path || "").replace(/\/deleted\/[^/]+/, "/").replace(/\/$/, "")
      , ""
    );
    const randomStr = file.path?.split("/deleted/")[1].split("/")[0];
    const formData = new FormData();
    // formData.append("set.deleted", "0");
    formData.append("set.deleted_by", "null");
    formData.append("set.path", `replace(path, ${JSON.stringify(file.path)}, ${JSON.stringify(originalPath)})`)
    formData.append("where", `id=${JSON.stringify(file.id)} ${file.isDir ? `| path~'%/${randomStr}%'` : ""}`);
    formData.append("returning", "id,created,updated,path,name,thumb,file,config,meta,tags,deleted,deleted,deleted_by,locked");

    const { data, error } = await this.fetch(
      "files/update",
      {
        method: "POST",
        body: formData,
      },
      true,
      (data) => this.processFile(data[0])
    );

    return { data, error };
  }

  async moveFile(item: CustomFileData, newPath: string): Promise<{ data: CustomFileData[] | null; error: any }> {
    if (!item.id) {
      return { data: null, error: "File ID is required" };
    } else if (item.locked) {
      return { data: null, error: "File is locked" };
    }

    const formData = new FormData();
    const isFolder = item.isDir;
    newPath = joinPaths(this.basePath, newPath);
    if (isFolder) {
      //move children and parent
      formData.append('set.path', `replace(path, ${JSON.stringify(item.path)}, ${JSON.stringify(newPath)})`);
      formData.append('where', `(path ~ ('%' || ${JSON.stringify(item.path + item.id)} || '%')) | id=${JSON.stringify(item.id)}`);
    } else {
      formData.append('setValues.path', newPath);
      formData.append('where', `id=${JSON.stringify(item.id)}`);
    }

    formData.append('returning', [...propsList, 'config'].join(','));

    const { data, error } = await this.fetch(
      'files/update', {
        method: 'POST',
        body: formData,
      },
      true,
      (data) => {
        if (Array.isArray(data)) {
          return this.processFiles(data);
        } else {
          return [this.processFile(data)].filter((file) => file) as CustomFileData[];
        }
      }
    );


    if(!data || error) {
      console.error('Error moving file/folder', error);
    }
    return { data, error };
  }

  async copyFile(item: CustomFileData, newPath: string, newName?: string): Promise<{ data: CustomFileData[] | null; error: any }> {
    if (!item.id) {
      return { data: null, error: "File ID is required" };
    } else if (item.locked) {
      return { data: null, error: "File is locked" };
    }

    const isFolder = item.isDir;

    // check if dest file/folder exists
    const { data: destFile, error: destError } = await this.getFileByName(newName || item.name, newPath);
    if(destError || !destFile) {
      // return {data: null, error: 'Unable to copy file/folder, cannot check for existing, try again later'};
      const is404 = true // todo - check error and only continue if it says file doesnt exists.
      if (!is404) {
        return {data: null, error: 'Unable to copy file/folder, cannot check for existing, try again later'};
      }
    }else {
      // already exists
      if(isFolder) return {data: null, error: 'Destination folder already exists'}

      // todo - if file - ask user if they want to replace (only for files)
      return {data: null, error: 'Destination file already exists'}
    }

    // todo check if more than 1000 files in the tree. we can also get the api to return the count(*) along with this.
    const queryParams = new URLSearchParams({
      offset: "0",
      limit: "1000",
      perPage: "1000",
      // same as moveFile
      where: `(path ~ ('%' || ${JSON.stringify(item.id)} || '%')) | id=${JSON.stringify(item.id)}`,
      // todo make sure any new properties are added here, or should we use *?
      select: [...propsList, 'config', 'notes', 'deleted_by', 'created_by'].join(","),
    });
    const { data: listData, error: listError } = await this.fetch<TFileData[]>(`files/select?${queryParams}`, undefined, true);
    if(listError || !listData) return {data: null, error: listError};

    if(!listData.length) return {data: null, error: "No files found to copy"};

    const root = listData.find((dat) => dat.id === item.id);
    if(!root) {
      // todo handle error @maan
      throw new Error("Root file/folder not found");
    }
    if(newName) root.name = newName;
    traverse(root, item.path as string, newPath, listData, this.user.id);

    const insertBody = {
      values: listData,
      returning: [...propsList].join(','),
    }

    const { data, error } = await this.fetch(
      'files/insert', {
        method: 'POST',
        body: JSON.stringify(insertBody),
        headers: { "Content-Type": "application/json",  'x-check-file-references' : "false" },
      },
      true,
      (data) => {
        if (Array.isArray(data)) {
          return this.processFiles(data);
        } else {
          return [this.processFile(data)].filter((file) => file) as CustomFileData[];
        }
      }
    );
    // console.log(structuredClone(data))

    if(!data || error) {
      console.error('Error copying file/folder', error);
    }
    return { data, error };
  }

  async toggleFileLock(file: CustomFileData): Promise<{ data: CustomFileData | null; error: any }> {
    if (!file.id) {
      return { data: null, error: "File ID is required" };
    }

    const formData = new FormData();
    const locked = file.locked;

    formData.append("setValues.locked", locked ? "false" : "true");
    formData.append("where", `id=${JSON.stringify(file.id)} ${file.isDir ? `| path ~ '%${file.id}%'` : ""}`);
    formData.append("returning", [...propsList, 'config'].join(','));

    const { data, error } = await this.fetch(
      "files/update",
      {
        method: "POST",
        body: formData,
      },
      true,
      (data) => this.processFile(data[0])
    );

    return { data, error };
  }

  //todo implement this
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async countFilesByLogo(logoUrl: string): Promise<number> {
    // const filter = `config.logo=${JSON.stringify(logoUrl)}`;
    // const queryParams = new URLSearchParams({
    //   where: filter,
    //   select: "id",
    // });
    // const { data, error } = await this.fetch(`files/select?${queryParams}`, undefined, true);
    // if (error || !data) {
    //   console.error("Error fetching file count by logo:", error);
    //   return 0;
    // }
    return 0
    // return Array.isArray(data) ? data.length : 0;
  }

  async downloadFile(file: CustomFileData): Promise<{ error: any , data?: Blob }> {
    try {
      const response = await fetch(this.getDownloadUrl(file));

      if (!response.ok) {
        const errorText = await response.text();
        return { error: errorText };
      }

      const blob = await response.blob();
      downloadBlob(blob, file.name);
      return { error: null , data : blob};
    } catch (error) {
      return { error };
    }
  }

  getDownloadUrl(file: {id: string, file?: string}, withOrigin = true): string {
    if(!file.file) return ''
    // Note host is added because CF doesn't work properly with Vary, so this is required for CORS issue
    const host = window.location.host
    const hostSuffix = withOrigin ? ((file.file.includes('?') ? '&' : '?') + `h=${host}`) : ""
    if(this.fileApi) return `${this.fileApi}${file.file}${hostSuffix}`;
    return `${this.baseDownloadUrl}${file.id}/${file.file}${hostSuffix}`;
  }

  async search(query: string, path: string): Promise<{ data: CustomFileData[]; error: any }> {
    const fullPath = joinPaths(this.basePath, path); //ensure we are only searching inside the base path
    const filter = `(name ~ ('%' || ${JSON.stringify(query)} || '%')) & (path ~ (${JSON.stringify(fullPath.split("/").slice(-2,-1)[0] || "/")} || '%'))`;
    const queryParams = new URLSearchParams({
      offset: "0",
      limit: "1000",
      where: filter,
      select: propsList.join(','),
    });
    const { data, error } = await this.fetch(`files/select?${queryParams}`, undefined, true, (data) => {
      return this.processFiles(data);
    });

    if (error || !data) {
      return { data: [], error };
    }

    return { data: data as CustomFileData[], error: null };
  }

  async checkout (lookupKey: string, pollingTimeInSeconds: number = 0) {
      try {
        const formData = new FormData();
        formData.append("lookup_key", lookupKey);
        formData.append("email", this.user.email);
        formData.append("return_url", `https://drive.ijewel3d.com/${this.getBaseName()}`);
  
        const response = await fetch(`${this.baseApiUrl}/billing/checkout`, {
          method: "POST",
          body: formData,
          headers: { Authorization: `Bearer ${this.token}` },
        });
  
        const { url , error, message } = await response.json();
  
        if(!url || error || message){
          console.error("Error getting checkout url:", error, message);
          return { error: error || message };
        }

        //open in new tab 
        if(pollingTimeInSeconds){
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const checkoutWindow = window.open(url, "_blank", "width=500,height=600");

          const pollInterval = 3000; // Poll every 3 seconds
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const maxTime = pollingTimeInSeconds * 1000; // Convert seconds to milliseconds
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          let currentTime = 0;

          const pollPaymentStatus = async () => {
            currentTime += pollInterval;
            try {
              // TODO check updated user plan and update Locally
              return {error :"Payment not implemented Fully"};
            } catch (error) {
              console.log("Error in polling status: ", error);
              return { error };
            }
          }

          return pollPaymentStatus();
        }
        else{
          window.open(url, "_blank");
          return { error: null };
        }
      } catch (error) {
        console.error("Error upgrading user:", error);
        return { error };
      }
  }

  async manageSubscription() {
      try {
        const formData = new FormData();
        formData.append("email", this.user.email);
        formData.append("return_url", `https://drive.ijewel3d.com/${this.getBaseName()}`);
  
        const response = await fetch(`${this.baseApiUrl}/billing/portal`, {
          method: "POST",
          body: formData,
          headers: { Authorization: `Bearer ${this.token}` },
        });
  
        const { url , error, message } = await response.json();
  
        if(!url || error || message){
          console.error("Error getting checkout url:", error, message);
          return { error: error || message };
        }

        //open in new tab 
        window.open(url, "_blank");
        return { error: null };
      } catch (error) {
        console.error("Error upgrading user:", error);
        return { error };
      }
  }

  async getAssets(parentId?: string): Promise<{ data: any; error: any }> {
    if(!parentId) return {data : null, error : "Assets parent ID is required" };
    const queryParams = new URLSearchParams({ select: "id,name,path,thumb,file,tags,deleted" , where : `path~'%${parentId}%'`});
    const { data, error } = await this.fetch("files/select?" + queryParams, undefined, true, (data) => {
      return this.processFiles(data);
    });

    // if(data?.length === 0 || !data){
    //   return {data : assets, error : null };
    // }

    return { data, error };
  }

  async getUserCustomAssetsFolder(): Promise<{ data: CustomFileData | null; error: any }> {
    const baseFolderId = this.user?.meta?.assets;

    if(baseFolderId) {
      const {data, error} = await this.getFile(baseFolderId);
      if(data && !error) {
        return { data, error: null };
      }
      // if folder not found with the ID in meta, continue to search by tag
    }

    const { data: baseFolder, error: baseFolderError } = await this.getFilesBytag(CustomAssetsBaseFolderTag, "/");
    if (!baseFolderError && baseFolder && baseFolder.length > 0) {
      return {data: baseFolder[0], error: null};
    }

    // no folder found - create new one
    const folderName = "cst_assets_" + this.user?.user;
    const { data: newFolder, error: createError } = await this.createDirectory(
      folderName,
      this.basePath,
      undefined,
      true,
      CustomAssetsBaseFolderTag
    );

    if (createError || !newFolder) {
      return { data: null, error: createError || "Failed to create assets folder" };
    }

    return { data: newFolder, error: null };
  }

  async getUserCustomAssets(): Promise<{ data: CustomFileData[] | null; error: any }> {
    const { data: folder, error: folderError } = await this.getUserCustomAssetsFolder();
    if (folderError || !folder) {
      return { data: null, error: folderError || "Could not get or create assets folder" };
    }
    return this.getAssets(folder.id);
  }

  stopDesignSyncOperation(): void {
    this.stopDesignSync = true;
  }

  stopInstanceSyncOperation(): void {
    this.stopInstanceSync = true;
  }

  private stopOldDriveSync: boolean = false;

  stopOldDriveSyncOperation(): void {
    this.stopOldDriveSync = true;
  }

  async syncFromOldDrive(
    oldDriveUrl: string,
    username: string,
    password: string,
    progressCallback?: (progress: SyncProgress) => void
  ): Promise<{ success: boolean; message: string; filesCount: number; error?: string }> {
    this.stopOldDriveSync = false;

    try {
      progressCallback?.({ stage: 'authenticating', message: 'Authenticating with old drive...', progress: 0 });

      const oldDriveAPI = new OldDriveClient(oldDriveUrl);

      const authResult = await oldDriveAPI.authenticate(username, password);
      if (!authResult.success) {
        return {
          success: false,
          message: 'Old drive authentication failed: ' + authResult.error,
          filesCount: 0,
          error: authResult.error
        };
      }

      progressCallback?.({
        stage: 'fetching',
        message: 'Fetching files from old drive...',
        progress: 10,
        profile: {
          name: authResult.user?.name || username,
          email: authResult.user?.email || username,
          avatar: authResult.user?.avatar
        }
      });

      // Get all files from old drive
      const filesResult = await oldDriveAPI.getAllFiles('/');
      if (!filesResult.success || !filesResult.files) {
        return {
          success: false,
          message: 'Failed to fetch files from old drive: ' + filesResult.error,
          filesCount: 0,
          error: filesResult.error
        };
      }
      console.log("filesResult", filesResult.files);

      progressCallback?.({ stage: 'creating_folder', message: 'Creating sync folder...', progress: 20 });

      const syncFolder = await this.createSyncFolder('Migrated from old drive', 'old drive', {
        sourceUrl: oldDriveUrl,
        sourceUsername: username
      });

      if (!syncFolder) {
        return {
          success: false,
          message: 'Failed to create sync folder',
          filesCount: 0,
          error: 'Failed to create sync folder'
        };
      }

      progressCallback?.({ stage: 'syncing', message: 'Syncing files...', progress: 30 });

      const copiedCount = await this.importOldDriveFiles(
        oldDriveAPI,
        filesResult.files,
        syncFolder.id!,
        progressCallback
      );

      progressCallback?.({ stage: 'complete', message: 'Sync completed successfully', progress: 100 });

      return {
        success: true,
        message: copiedCount === 0 ? 'No new files to sync' : `Successfully migrated ${copiedCount} files from old drive`,
        filesCount: copiedCount
      };

    } catch (error) {
      console.error('Old drive sync error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Internal server error during sync';
      return { success: false, message: errorMessage, filesCount: 0, error: errorMessage };
    }
  }

  async importOldDriveFiles(
    oldDriveAPI: OldDriveClient,
    sourceFiles: OldDriveFile[],
    targetFolderId: string,
    progressCallback?: (progress: SyncProgress) => void
  ): Promise<number> {
    // Convert OldDriveFile[] to TFileData[] format for traverse function
    const listData: TFileData[] = sourceFiles.map(file => ({
      id: file.path, // Use path as initial ID (will be replaced by traverse)
      name: file.name,
      path: file.path,
      created_by: this.user.id,
      updated: null,
      thumb: null,
      deleted: null,
      deleted_by: null,
      locked: null,
      meta: JSON.stringify({
        type: file.type === 'dir' ? 'folder' : 'file',
        source: 'filegator',
        sourceId: file.path,
        sourcePath: file.path,
        size: file.size || 0,
        originalTimestamp: file.timestamp
      }),
      tags: '',
      config: '{}',
      notes: '',
      file: file.type === 'file' ? file.path : undefined // Store original path for downloading
    }));

    // dummy just for traverse to work, will be removed before uploading
    const root = { 
      id : "/",
      path: "/", 
      name: "root",
      created_by: this.user.id,
      updated: null,
      thumb: null,
      deleted: null,
      deleted_by: null,
      locked: null,
      meta: JSON.stringify({
        type: 'folder',
        source: 'filegator',
        sourceId: '/',
        sourcePath: '/',
        size: 0,
        originalTimestamp: 0
      }),
      tags: '',
      config: '{}',
      notes: '',
      file: undefined
    };
    listData.push(root);

    const targetPath = `${this.basePath}${targetFolderId}/`;
    const sourcePath = '/';

    // Use traverse to give all files new IDs and paths
    traverse(root, sourcePath, targetPath, listData, this.user.id);
    listData.shift(); // remove dummy root
    // Separate folders and files for processing
    const folders = listData.filter(file => {
      const meta = typeof file.meta === 'string' ? JSON.parse(file.meta) : file.meta;
      return meta?.type === 'folder';
    });

    const files = listData.filter(file => {
      const meta = typeof file.meta === 'string' ? JSON.parse(file.meta) : file.meta;
      return meta?.type !== 'folder';
    });

    let copiedCount = 0;
    const totalFiles = files.length;

    const findBySourceId = (files: any[], id: string) => {
      return files.find(file => {
        const meta = typeof file.meta === 'string' ? JSON.parse(file.meta) : file.meta;
        return meta?.sourceId === id;
      });
    };

    // Get existing files to check for duplicates
    const { data: existingFiles } = await this.getAllNestedChildren(targetFolderId);
    console.log("existingFiles", existingFiles);

    try {
      // Create folders first
      if (folders.length > 0) {
        progressCallback?.({
          stage: 'syncing',
          message: 'Creating folder structure...',
          progress: 30,
          currentItem: 'Folders'
        });

        // If folder already exists, use its ID instead of the ID given from traverse
        existingFiles.forEach((folder: any) => {
          const meta = typeof folder.meta === 'string' ? JSON.parse(folder.meta) : folder.meta;
          const oldId = folder.id;
          const newId = findBySourceId(listData, meta.sourceId)?.id;
          if (meta?.type !== 'folder') return;

          if (!oldId || !newId) return;

          // Replace the new ID with existing ID in all file and folder paths
          files.forEach((file: any) => {
            file.path = file.path.replace(newId, oldId);
          });
          folders.forEach((folder: any) => {
            folder.path = folder.path.replace(newId, oldId);
          });
        });

        // Filter out folders that already exist
        const newFolders = folders.filter(folder => {
          const meta = typeof folder.meta === 'string' ? JSON.parse(folder.meta) : folder.meta;
          return !findBySourceId(existingFiles, meta.sourceId);
        });

        // Create new folders using bulk insert
        if (newFolders.length > 0) {
          const insertBody = {
            values: newFolders.map(folder => ({
              ...folder,
              meta: typeof folder.meta === 'string' ? folder.meta : JSON.stringify(folder.meta)
            })),
            returning: [...propsList].join(','),
          };
          console.log("folders body" , insertBody);
          const { data: insertedFolders, error: insertError } = await this.fetch<TFileData[]>(
            'files/insert',
            {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(insertBody),
            },
            true
          );

          if (insertError) {
            console.error('Error creating folders:', insertError);
            throw new Error('Failed to create folder structure');
          }

          console.log(`Created ${insertedFolders?.length || 0} new folders`);
        }
      }

      // Process files
      for (let i = 0; i < files.length; i++) {
        if (this.stopOldDriveSync) {
          console.log('Old drive sync stopped by user');
          break;
        }

        const file = files[i];
        const fileProgress = Math.round(40 + (i / totalFiles) * 50);

        try {
          const filePath = file.path; // This is the new path from traverse
          const { data: existingFile } = await this.getFileByName(file.name, filePath);
          if (existingFile && !existingFile.deleted) {
            progressCallback?.({
              stage: 'syncing',
              message: `Skipping existing file: ${file.name}`,
              progress: fileProgress,
              currentItem: file.name
            });
            continue;
          }

          // Get original file path for downloading
          const meta = typeof file.meta === 'string' ? JSON.parse(file.meta) : file.meta;
          const originalPath = meta.sourceId || file.file;

          if (!originalPath) {
            progressCallback?.({
              stage: 'syncing',
              message: `Skipping file without source path: ${file.name}`,
              progress: fileProgress,
              currentItem: file.name
            });
            continue;
          }

          // Download file from old drive
          const downloadResult = await oldDriveAPI.downloadFile(originalPath);
          if (!downloadResult.success || !downloadResult.blob) {
            console.warn(`Failed to download file ${file.name}: ${downloadResult.error}`);
            continue;
          }

          const downloadedFile = new File([downloadResult.blob], file.name, {
            type: downloadResult.blob.type || 'application/octet-stream'
          });

          const { data: uploadedFile, error } = await this.uploadFile(
            downloadedFile,
            filePath,
            undefined, // no thumbnail
            {
              ...meta,
              size: downloadResult.blob.size
            },
            undefined, // progress callback
            undefined, // signal
            file.tags || '',
            file.name,
            (typeof file.config === 'string' ? JSON.parse(file.config) : file.config) || {}
          );
          console.log("files body" , uploadedFile);

          if (error || !uploadedFile) {
            console.error(`Failed to upload file ${file.name}:`, error);
            continue;
          }

          copiedCount++;

          progressCallback?.({
            stage: 'syncing',
            message: `Synced: ${file.name}`,
            progress: fileProgress,
            currentItem: file.name
          });

        } catch (error) {
          console.error(`Error processing file ${file.name}:`, error);
        }
      }

    } catch (error) {
      console.error('Error in importOldDriveFiles:', error);
      throw error;
    }

    return copiedCount;
  }





  async syncFromDesign(password: string, progressCallback?: (progress: SyncProgress) => void): Promise<{ success: boolean; message: string; projectsCount: number; folderId?: string; error?: string }> {
    this.stopDesignSync = false;
    try {
      progressCallback?.({ stage: 'authenticating', message: 'Authenticating with iJewel Design...', progress: 0 });

      const designAuth = await this.authenticateWithExternalService(
        'https://api.ijewel.design/auth/v1/token?grant_type=password',
        this.user.email,
        password,
        { 'apikey': '0' }
      );

      if (!designAuth.success || !designAuth.accessToken || !designAuth.user.id) {
        return { success: false, message: 'Design authentication failed', projectsCount: 0, error: designAuth.error };
      }

      progressCallback?.({
        stage: 'fetching',
        message: 'Fetching projects from iJewel Design...',
        progress: 10,
        profile: {
          name: designAuth.user.user_metadata?.name || designAuth.user.user_metadata?.full_name,
          email: designAuth.user.email,
          avatar: designAuth.user.user_metadata?.avatar_url || designAuth.user.user_metadata?.avatar
        }
      });

      const queryParams = {
        "owner_id" : "eq." + designAuth.user.id,
        "order" : "updated_at.desc",
        "select" : "id,name,description,slug,created_at,updated_at,project_data,poster_url"
      }

      const projects = await this.fetchExternalFiles(
        'https://api.ijewel.design/rest/v1/projects?' + new URLSearchParams(queryParams),
        {
          'Authorization': `Bearer ${designAuth.accessToken}`,
          'apikey': '0'
        }
      );

      if (!projects.success) {
        return { success: false, message: 'Failed to fetch projects: ' + projects.error, projectsCount: 0, error: projects.error };
      }

      progressCallback?.({ stage: 'creating_folder', message: 'Creating sync folder...', progress: 20 });

      const syncFolder = await this.createSyncFolder('iJewel Design', 'design');
      if (!syncFolder) {
        return { success: false, message: 'Failed to create sync folder', projectsCount: 0, error: 'Failed to create sync folder' };
      }

      progressCallback?.({ stage: 'syncing', message: 'Checking for existing projects...', progress: 25 });

      // filter out projects that already exist 
      const newProjects = await this.filterNewProjects(syncFolder.id!, projects.data || []);
      const skippedCount = (projects.data?.length || 0) - newProjects.length;

      progressCallback?.({ stage: 'syncing', message: 'Syncing projects...', progress: 30 });

      let createdCount = skippedCount;
      const totalProjects = newProjects.length;

      for (let i = 0; i < totalProjects; i++) {
        if (this.stopDesignSync) {
          return { success: false, message: 'Sync stopped by user', projectsCount: createdCount, error: 'Sync stopped' };
        }

        const project = newProjects[i];
        const projectProgress = 30 + (createdCount / (projects.data?.length || 1)) * 60;

        progressCallback?.({
          stage: 'syncing',
          message: `Syncing project ${createdCount}/${(projects.data?.length || 1)}: ${project.name}`,
          progress: projectProgress,
          currentItem: project.name
        });

        try {
          await this.importDesignProject(syncFolder.id!, project);
          createdCount++;
        } catch (error) {
          console.error(`Failed to sync project ${project.name}:`, error);
          progressCallback?.({
            stage: 'syncing',
            message: `Failed to sync project: ${project.name}`,
            progress: projectProgress,
            currentItem: project.name,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }

      progressCallback?.({ stage: 'complete', message: 'Sync completed successfully', progress: 100 });

      return {
        success: true,
        message: createdCount === 0 ? 'No new projects to sync' : `Successfully synced ${createdCount} projects from iJewel Design`,
        projectsCount: createdCount,
        folderId: syncFolder.id
      };

    } catch (error) {
      console.error('Design sync error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Internal server error during sync';
      return { success: false, message: errorMessage, projectsCount: 0, error: errorMessage };
    }
  }

  async authenticateWithExternalService(url: string, email: string, password: string, headers: Record<string, string> = {}): Promise<AuthResult> {
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...headers
        },
        body: JSON.stringify({
          email,
          password
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        return {
          success: false,
          error: errorData.msg || errorData.error_description || errorData.message || 'Authentication failed'
        };
      }

      const authData = await response.json();
      return {
        success: true,
        token: authData.token, // for other drive instances
        accessToken: authData.access_token, // for design
        user: authData.user || authData.record
      };
    } catch (error) {
      return {
        success: false,
        error: 'Network error during authentication'
      };
    }
  }

  async fetchExternalFiles(url: string, headers: Record<string, string>): Promise<FetchResult> {
    try {
      const response = await fetch(url, { headers });

      if (!response.ok) {
        return {
          success: false,
          error: 'Failed to fetch data from external service'
        };
      }

      const data = await response.json();
      return {
        success: true,
        data: Array.isArray(data) ? data : [data]
      };
    } catch (error) {
      return {
        success: false,
        error: 'Network error while fetching data'
      };
    }
  }

  async createSyncFolder(folderName: string, source: string, metadata: Record<string, any> = {}): Promise<CustomFileData | null> {
    const userBasePath = this.user?.meta?.base;
    if (!userBasePath) {
      throw new Error('User base path not found');
    }

    const syncFolderTag = `_${source}_folder`;

    try {
      const { data: existingFolders } = await this.getFilesBytag(syncFolderTag);
      if (existingFolders && existingFolders.length > 0) {
          const existingFolder = existingFolders.find(folder => {
          const folderMeta = folder.meta;
          return folder.name === folderName &&
                 folderMeta?.source === source &&
                 folder.path?.startsWith(userBasePath);
        });

        if (existingFolder) {

          // update the last synced timestamp
          const updatedFolder = await this.updateFile({
            id: existingFolder.id,
            meta: {
              ...existingFolder.meta,
              lastSyncedAt: new Date().toISOString(),
              ...metadata
            }
          });

          return updatedFolder.data || existingFolder;
        }
      }
    } catch (error) {
      console.warn('Error checking for existing sync folders:', error);
    }

    const { data: folder, error } = await this.createDirectory(
      folderName,
      userBasePath,
      undefined,
      false,
      syncFolderTag,
      {
        type: 'folder',
        source: source,
        lastSyncedAt: new Date().toISOString(),
        ...metadata
      }
    );

    if (error || !folder) {
      throw new Error('Failed to create sync folder');
    }

    return folder;
  }

  async getNewName(newName: string, folderPath: string): Promise<string> {
    let iterations = 0;
    const maxIterations = 10;
    let currentName = newName;

    while (iterations < maxIterations) {
      const { data: existingFile } = await this.getFileByName(currentName, folderPath);
      if (!existingFile) {
        return currentName;
      }
      currentName = getDuplicateName(currentName);
      iterations++;
    }

    throw new Error("Max same File name reached");
  }

  async checkIfProjectExists(folderId: string, project: any): Promise<boolean> {
    try {
      if (!project.id) {
        console.warn('Project has no ID, cannot check for existence');
        return false;
      }

      const { data: folderFiles, error } = await this.getAssets(folderId);
      if (error || !folderFiles) {
        console.error('Error getting folder files:', error);
        return false;
      }

      const existingFile = folderFiles.find((file: CustomFileData) => {
        const meta = typeof file.meta === 'string' ? JSON.parse(file.meta) : file.meta;
        return meta && meta.source === 'design' && meta.sourceId === project.id;
      });

      return !!existingFile;
    } catch (error) {
      console.error('Error checking if project exists:', error);
      return false;
    }
  }

  async filterNewProjects(folderId: string, projects: any[]): Promise<any[]> {
    try {
      const { data: folderFiles, error } = await this.getAllNestedChildren(folderId);
      if (error || !folderFiles) {
        console.error('Error getting folder files:', error);
        return projects; // Return all projects if we can't check
      }

      const existingProjectIds = new Set<string>();
      folderFiles.forEach((file: CustomFileData) => {
        const meta = typeof file.meta === 'string' ? JSON.parse(file.meta) : file.meta;
        if (meta && meta.source === 'design' && meta.sourceId) {
          existingProjectIds.add(meta.sourceId);
        }
      });

      const newProjects = projects.filter(project => {
        if (!project.id) {
          console.warn('Project has no ID, skipping:', project.name);
          return false;
        }
        return !existingProjectIds.has(project.id);
      });

      return newProjects;
    } catch (error) {
      console.error('Error filtering projects:', error);
      return projects; 
    }
  }

  async importDesignProject(parentFolderId: string, project: any): Promise<void> {
    const modelUrl = project.project_data?.modelUrl;
    if (!modelUrl) {
      throw new Error(`Skipping project ${project.name}: no modelUrl found`);
    }

    const folderPath = `${this.basePath}${parentFolderId}/`;
    let finalFileName: string;

    try {
      const sanitizedProjectName = sanitizeFileName(project.name || 'Untitled Project');
      const preferredFileName = sanitizedProjectName + '.glb';

      try {
        finalFileName = await this.getNewName(preferredFileName, folderPath);
      } catch (error : any) {
        console.warn(`Failed to get unique name for project name "${preferredFileName}": ${error?.message}`);

        const urlFileName = extractFilenameFromUrl(modelUrl);

        try {
          finalFileName = await this.getNewName(urlFileName, folderPath);
        } catch (urlError : any) {
          throw new Error(`Failed to get unique filename even with URL extraction: ${urlError?.message}`);
        }
      }

      // download file
      const modelResponse = await fetch(modelUrl);
      if (!modelResponse.ok) {
        throw new Error(`Failed to download model: ${modelResponse.statusText}`);
      }

      const modelBlob = await modelResponse.blob();
      const modelFile = new File([modelBlob], finalFileName, { type: 'model/gltf-binary' });

      // download thumbnail if available
      let thumbnailFile: File | undefined;
      if (project.poster_url) {
        try {
          const thumbResponse = await fetch(project.poster_url);
          if (thumbResponse.ok) {
            const thumbBlob = await thumbResponse.blob();
            thumbnailFile = new File([thumbBlob], 'thumbnail.jpg', { type: 'image/jpeg' });
          }
        } catch (error) {
          console.warn('Failed to download thumbnail:', error);
        }
      }

      const project_data = typeof project.project_data === 'string'
        ? JSON.parse(project.project_data)
        : project.project_data || {};

      // upload the file
      const { data: uploadedFile, error } = await this.uploadFile(
        modelFile,
        folderPath,
        thumbnailFile,
        {
          source: 'design',
          sourceId: project.id,
          sourceProjectName: project.name, 
          sourceModelUrl: modelUrl,
          size: modelFile.size
        },
        undefined, // progress callback
        undefined, // signal
        '', // tags
        finalFileName,
        project_data
      );

      if (error || !uploadedFile) {
        throw new Error(`Failed to upload file: ${error}`);
      }

    } catch (error) {
      console.error(`Error importing project ${project.name}:`, error);
      throw error;
    }
  }

  async syncFromInstance(basename: string, password: string, progressCallback?: (progress: SyncProgress) => void): Promise<{ success: boolean; message: string; filesCount: number; error?: string }> {
    this.stopInstanceSync = false;
    try {
      progressCallback?.({ stage: 'authenticating', message: 'Authenticating with source instance...', progress: 0 });

      const instanceBasePath = `https://${basename}.ijewel3d.com`;

      const sourceAuth = await this.authenticateWithExternalService(
        `${instanceBasePath}/api/raw/v1/users/auth/login-password`,
        this.user.email,
        password
      );

      if (!sourceAuth.success || !sourceAuth.token) {
        return { success: false, message: 'Source instance authentication failed: ' + sourceAuth.error, filesCount: 0, error: sourceAuth.error };
      }

      progressCallback?.({
        stage: 'fetching',
        message: 'Fetching files from source instance...',
        progress: 10,
        profile: {
          name: sourceAuth.user.name,
          email: sourceAuth.user.email,
          avatar: sourceAuth.user.avatar
        }
      });
      const sourceMeta = typeof sourceAuth.user.meta === 'string' 
        ? JSON.parse(sourceAuth.user.meta) 
        : sourceAuth.user.meta;

      const sourceParentId = sourceMeta?.base?.replace(/\//g, '') 
      if (!sourceParentId) {
        return { success: false, message: 'Could not determine source folder', filesCount: 0, error: 'Could not determine source folder' };
      }

      const queryParams = new URLSearchParams({
        limit: '1000',
        where: `deleted=null&path~'%${sourceParentId}%'|id='${sourceParentId}'`
      });

      const sourceFiles = await this.fetchExternalFiles(
        `${instanceBasePath}/api/raw/v1/files/select?${queryParams}`,
        { 'X-Authorization': `Bearer ${sourceAuth.token}` }
      );

      if (!sourceFiles.success || !sourceFiles.data?.length) {
        return { success: false, message: 'No files found to sync', filesCount: 0, error: 'No files found' };
      }

      progressCallback?.({ stage: 'creating_folder', message: 'Creating sync folder...', progress: 20 });

      // create sync folder
      const instanceName = basename.includes('dev') ? basename : 'Shared Drive';
      const syncFolder = await this.createSyncFolder(`Synced from ${instanceName}`, basename);
      if (!syncFolder) {
        return { success: false, message: 'Failed to create sync folder', filesCount: 0, error: 'Failed to create sync folder' };
      }

      progressCallback?.({ stage: 'syncing', message: 'Syncing files...', progress: 30 });

      const copiedCount = await this.importFolder(
        sourceFiles.data,
        sourceParentId,
        syncFolder.id!,
        `${instanceBasePath}/files/`,
        basename,
        progressCallback
      );

      progressCallback?.({ stage: 'complete', message: 'Sync completed successfully', progress: 100 });

      return {
        success: true,
        message: copiedCount === 0 ? 'No new files to sync' : `Successfully synced ${copiedCount} files from instance`,
        filesCount: copiedCount
      };

    } catch (error) {
      console.error('Instance sync error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Internal server error during sync';
      return { success: false, message: errorMessage, filesCount: 0, error: errorMessage };
    }
  }

  async importFolder(
    sourceFiles: any[],
    sourceFolderId: string,
    targetFolderId: string,
    filesUrlBasePath: string,
    basename: string,
    progressCallback?: (progress: SyncProgress) => void
  ): Promise<number> {
    const listData = sourceFiles
      .filter(file => !file.tags?.includes(CustomAssetsBaseFolderTag) // don't copy custom assets
        && file.deleted_by === null
      )
      .map(file => ({ ...file }));
    const root = listData.find((file) => file.id === sourceFolderId);
    if (!root) {
      throw new Error('Root folder not found');
    }

    const targetPath = `${this.basePath}${targetFolderId}/`;
    const sourcePath = root.path;

    traverse(root, sourcePath, targetPath, listData, this.user.id); // keep the original ids

    // Separate folders and files for processing
    const folders = listData.filter(file => {
      const meta = typeof file.meta === 'string' ? JSON.parse(file.meta) : file.meta;
      return meta?.type === 'folder';
    });

    const files = listData.filter(file => {
      const meta = typeof file.meta === 'string' ? JSON.parse(file.meta) : file.meta;
      return meta?.type !== 'folder';
    });

    let copiedCount = 0;
    const totalFiles = files.length;

    const findBySourceId = (files : FileData[], id: string) => {
      return files.find(file => {
        const meta = typeof file.meta === 'string' ? JSON.parse(file.meta) : file.meta;
        return meta?.sourceId === id;
      });
    }
    const {data: existingFiles} = await this.getAllNestedChildren(targetFolderId);

    try {
      //create folders first
      if (folders.length > 0) {
        progressCallback?.({
          stage: 'syncing',
          message: 'Creating folder structure...',
          progress: 30,
          currentItem: 'Folders'
        });

    
        //if folder already exist, we need to use it's id instead of the id given from traverse, so we replace the id in the files and folders
        existingFiles.forEach((folder: any) => {
          const meta = typeof folder.meta === 'string' ? JSON.parse(folder.meta) : folder.meta;
          const oldId = folder.id
          const newId = findBySourceId(listData, meta.sourceId)?.id
          if(meta?.type !== 'folder') return;

          if(!oldId || !newId) return;

          files.forEach((file: any) => {
              file.path = file.path.replace(newId, oldId);
          });
          folders.forEach((folder: any) => {
            folder.path = folder.path.replace(newId, oldId);
          });
        });

        const newFolders = folders.filter(folder => {
          const meta = typeof folder.meta === 'string' ? JSON.parse(folder.meta) : folder.meta;
          return !findBySourceId(existingFiles, meta.sourceId);
        });

        for (let i = 0; i < newFolders.length; i++) {
          if (this.stopInstanceSync) {
            throw new Error('Sync stopped by user');
          }

          const folder = newFolders[i];
          progressCallback?.({
            stage: 'syncing',
            message: `Creating folder ${i + 1}/${newFolders.length}: ${folder.name}`,
            progress: 30 + (i / newFolders.length) * 10,
            currentItem: folder.name
          });

          const meta = {
            ...(typeof folder.meta === 'string' ? JSON.parse(folder.meta) : folder.meta) || {},
            type: 'folder',
            source: 'instance-' + basename,
          }

          const { error: folderError } = await this.createDirectory(
            folder.name,
            folder.path,
            folder.id,
            false, // absolute path
            folder.tags || '',
            meta
          );

          if (folderError) {
            console.error(`Error creating folder ${folder.name}:`, folderError);
            throw new Error(`Failed to create folder: ${folder.name}`);
          }
        }
      }

      for (let i = 0; i < files.length; i++) {
        if (this.stopInstanceSync) {
          return copiedCount;
        }

        const file = files[i];
        const fileProgress = 30 + (i / totalFiles) * 60;

        progressCallback?.({
          stage: 'syncing',
          message: `Syncing file ${i + 1}/${totalFiles}: ${file.name}`,
          progress: fileProgress,
          currentItem: file.name
        });

        try {
          const filePath = file.path; // this is the new path from traverse
          const { data: existingFile } = await this.getFileByName(file.name, filePath);
          if (existingFile && !existingFile.deleted ) {
            progressCallback?.({
              stage: 'syncing',
              message: `Skipping existing file: ${file.name}`,
              progress: fileProgress,
              currentItem: file.name
            });
            continue;
          }

          if (!file.file) {
            progressCallback?.({
              stage: 'syncing',
              message: `Skipping file without content: ${file.name}`,
              progress: fileProgress,
              currentItem: file.name
            });
            continue;
          }

          const fileUrl = filesUrlBasePath + file.file;

          try {
            const response = await fetch(fileUrl);
            if (!response.ok) {
              console.warn(`Failed to download file ${file.name}: ${response.statusText}`);
              continue;
            }

            const blob = await response.blob();
            const downloadedFile = new File([blob], file.name, { type: blob.type });

            let thumbnailFile: File | undefined;
            if (file.thumb) {
              try {
                const thumbUrl = filesUrlBasePath + file.thumb;
                const thumbResponse = await fetch(thumbUrl);
                if (thumbResponse.ok) {
                  const thumbBlob = await thumbResponse.blob();
                  thumbnailFile = new File([thumbBlob], 'thumbnail.jpg', { type: thumbBlob.type });
                }
              } catch (error) {
                console.warn('Failed to download thumbnail:', error);
              }
            }

            const { data: uploadedFile, error } = await this.uploadFile(
              downloadedFile,
              filePath,
              thumbnailFile,
              {
                ...(typeof file.meta === 'string' ? JSON.parse(file.meta) : file.meta),
                source: 'instance-' + basename,
                // sourceId: file.id, already set in traverse
                size: blob.size
              },
              undefined, // progress callback
              undefined, // signal
              file.tags || '',
              file.name,
              (typeof file.config === 'string' ? JSON.parse(file.config) : file.config) || {}
            );

            if (error || !uploadedFile) {
              console.error(`Failed to upload file ${file.name}:`, error);
              continue;
            }

            copiedCount++;
          } catch (error) {
            console.error(`Error processing file ${file.name}:`, error);
          }
        } catch (error) {
          console.error(`Error syncing file ${file.name}:`, error);
        }
      }

    } catch (error) {
      console.error('Error in importFolder:', error);
      throw error;
    }

    return copiedCount;
  }

  getBase(){
    // const origin = window.location.origin;
    // const _NAME = window.location.pathname.split('/')[1];
    // if(
    //     origin === 'https://drive.ijewel3d.com' ||
    //     origin === 'https://dev.drive.ijewel3d.com' ||
    //     origin.startsWith('http://localhost') ||
    //     origin.startsWith('https://localhost')
    // ) return `https://${_NAME}.ijewel3d.com/api/`; // accessing from https://drive.ijewel3d.com/custom
    // else if(_NAME === 'drive') return origin + '/api/'; // accessing from https://custom.ijewel3d.com/drive
    // return `https://drive-demo.ijewel3d.com/api/`
    const _NAME = this.getBaseName();
    return `https://${_NAME}.ijewel3d.com/api/`;
  }
  

  private _getBaseName(){
    // url can be https://basename.ijewel3d.com/drive or https://drive.ijewel3d.com/basename
    const origin = window.location.origin;
    const _NAME = window.location.pathname.split('/')[1];
    
    if(origin === 'https://drive.ijewel3d.com' ||
        origin === 'https://dev.drive.ijewel3d.com' ||
        origin.startsWith('http://localhost') ||
        origin.startsWith('https://localhost')
    ) {
      if(_NAME && _NAME !== "drive" && _NAME !== "drive-dev"){
        return ((origin === 'https://drive.ijewel3d.com') ? "drive" : "drive-dev");
      }
      return _NAME;
    } // accessing from https://drive.ijewel3d.com/basename
    else if(_NAME === 'drive') return origin.split('.')[0].split("/").pop(); // accessing from https://basename.ijewel3d.com/drive
    return ''
  }


  getBaseName(){
    let baseName = this._getBaseName();

    //map it to a shared drive bucker
    if(baseName === "drive") {
      baseName = this.getSharedDriveBucket();
    }

    //map to development bucket
    if(baseName === "drive-dev") {
      baseName = this.getDevDriveBucket();
    }

    return baseName;
  }

  getSharedDriveBucket(){
    return 'drive-weur-1';
  }

  getDevDriveBucket(){
    return 'dev-2';
  }
}

/**
 * Generate a UUID v4
 * https://stackoverflow.com/a/53723395/2229899
 */
export function uuidV4(b64 = false, nice = true){
  const ho = (n: number, p: number) => n.toString(16).padStart(p, '0'); /// Return the hexadecimal text representation of number `n`, padded with zeroes to be of length `p`
  const data = crypto.getRandomValues(new Uint8Array(16)); /// Fill the buffer with random data
  data[6] = (data[6] & 0xf) | 0x40; /// Patch the 6th byte to reflect a version 4 UUID
  data[8] = (data[8] & 0x3f) | 0x80; /// Patch the 8th byte to reflect a variant 1 UUID (version 4 UUIDs are)

  /// Return the Base64-encoded representation of the UUID - https://github.com/taskcluster/slugid/blob/db19090389b815f52d0f5baae72b2555a9f37008/src/slugid.js#L73
  if(b64) {
    if(nice) data[0] = data[0] & 0x7f;  // unset first bit to ensure [A-Za-f] first char
    return btoa(String.fromCharCode(...data))
        .replace(/\+/g, '-')  // Replace + with - (see RFC 4648, sec. 5)
        .replace(/\//g, '_')  // Replace / with _ (see RFC 4648, sec. 5)
        .substring(0, 22) // Drop '==' padding
  }

  const view = new DataView(data.buffer); /// Create a view backed by the 16-byte buffer
  /// Compile the canonical textual form from the array data
  return `${ho(view.getUint32(0), 8)}-${ho(view.getUint16(4), 4)}-${ho(view.getUint16(6), 4)}-${ho(view.getUint16(8), 4)}-${ho(view.getUint32(10), 8)}${ho(view.getUint16(14), 4)}`;
}

export function generateUid(): string {
  return uuidV4(true, true)
}

const traverse = (obj: TFileData, oldPath: string, newPath: string, listData: TFileData[], uid: string): any => {
  const newId = generateUid();
  // console.log(`replacing ${oldPath} with ${newPath} in ${obj.path} for ${obj.id}`);
  const lastPath = obj.path
  const path = lastPath.replace(oldPath, newPath);
  const meta = typeof obj.meta === "string" ? JSON.parse(obj.meta) : obj.meta;
  const lastId = obj.id;
  obj.id = newId; 
  obj.path = path;
  // @ts-ignore just in case
  delete obj.created;
  delete obj.updated;
  // @ts-ignore
  obj.created_by = uid;
  obj.meta = JSON.stringify({
    ...meta,
    sourceId: lastId,
  });
  if(meta.type === "folder") {
    const children = listData.filter((dat) => dat.path.endsWith(lastPath + lastId + "/"));
    // if(children.includes(obj)) console.error('dup', children, obj)
    children.forEach((child: any) => traverse(child, oldPath + lastId + "/", newPath + newId + "/", listData, uid));
  }
  // todo check if any other fields need to be ignored
}

(window as any).FileAPI = FileAPI;