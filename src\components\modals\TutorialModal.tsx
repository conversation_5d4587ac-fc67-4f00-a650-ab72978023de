import {Modal} from "../Modal.tsx";
import {useModal} from "../../provider/useModal.ts";
import {FC} from "react";
import {Button} from "@nextui-org/react";

interface TutorialModalProps {
    data: any;
    onClose: () => void;
}

const TutorialModal: FC<TutorialModalProps> = (props) => {
    const { modalState } = useModal();
    
    // Convert regular YouTube URL to embed URL
    const getEmbedUrl = (url: string) => {
        const videoId = url.split('v=')[1];
        return `https://www.youtube.com/embed/${videoId}`;
    };

    return (
        <Modal isOpen={modalState.modalType === "TUTORIAL"} onClose={props.onClose} size="4xl" backdrop="blur">
            {() => (
                <div className="w-full h-[80vh]">
                    <div className="flex justify-between items-center bg-[#ffffff] px-3 py-2.5">
                        <span className="flex gap-3 items-center">
              {/*<Button*/}
              {/*    isIconOnly*/}
              {/*    className="h-[21px] w-[21px]"*/}
              {/*    startContent={<OpenNewTab width={13} height={13} />}*/}
              {/*    as={Link}*/}
              {/*    href={addParamsToYoutubeURL(currentHint.src)}*/}
              {/*    target="__blank"*/}
              {/*    variant="light"*/}
              {/*/>*/}
              <Button
                  className="h-[21px] w-[21px]"
                  isIconOnly
                  startContent={"×"}
                  onClick={props.onClose}
                  variant="light"
              />
            </span>
                </div>
                <iframe
                    width="100%"
                    height="calc(100% - 44px)"
                    className="w-full h-[calc(100%_-_44px)]"
                    src={getEmbedUrl(props.data.link)}
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                />
            </div>)}
        </Modal>
    )
}

export default TutorialModal;