import { TextEditor } from "ijewelTextEditor";
import { useState } from "react";
import { Button } from "../Button";

interface RichTextEditorProps {
  project: any;
  onCancel: () => void;
  onChange: (data: any) => void;
}


export default function RichTextEditor(props: RichTextEditorProps) {
  const { project, onCancel, onChange } = props;

  const [data, setdata] = useState(
    () =>
      Object.keys(project.project_data.textEditorState || {}).length > 0 ? project.project_data.textEditorState : {
        name: project.name,
        description: project.description,
      }
  );
  return (
    <div>
      <TextEditor
        initialState={data}
        onChange={(editorNewState, projectData) => {
          setdata({
            ...projectData,
            textEditorState: projectData.name
              ? editorNewState.toJSON()
              : undefined,
          });
        }}
      />
      <div className="flex justify-between mt-3 gap-4">
        <Button
          color="danger"
          varient="solid"
          onClick={()=>{onCancel()}}
          name={<div className="text-white">Cancel</div>}
          className="flex-1 overflow-visible px-3.5 text-sm h-[30px] border-[1px] border-[#DCDCDC] rounded-full"
        >
        </Button>
        <Button
          color="primary"
          varient="solid"
          onClick={()=>{onChange(data)}}
          name={<div className="text-white">Save</div>}
          className="flex-1 overflow-visible px-3.5 text-sm h-[30px] border-[1px] border-[#DCDCDC] rounded-full"
        >
        </Button>
      </div>
    </div>
  );
}
