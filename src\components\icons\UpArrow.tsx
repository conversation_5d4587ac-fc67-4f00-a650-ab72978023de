import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <mask id="mask0_124_4142" style={{maskType: "alpha",}} maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
            <rect x="20" y="20" width="20" height="20" transform="rotate(-180 20 20)" fill="#D9D9D9"/>
        </mask>
        <g mask="url(#mask0_124_4142)">
            <path d="M10 7.16671L15 12.1667L13.8333 13.3334L10 9.50004L6.16667 13.3334L5 12.1667L10 7.16671Z" fill="#373737"/>
        </g>
    </svg>
)
export default SvgComponent