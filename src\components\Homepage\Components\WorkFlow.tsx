export default function WorkFlow() {
  return (
    <div className="bg-[#F6F6F6]">
      <div className="max-w-[1000px] mx-auto py-[60px] px-5 lg:px-0">
        <div className="flex flex-col items-center gap-[30px]">
          <div className="bg-[#FFFFFF] py-[50px] p-5 lg:px-[60px] rounded-[30px]">
            <h1 className="w-[65%] mx-auto md:w-full text-3xl md:text-4xl font-[700]">
              iJewel <span className="text-[#6E72F2]">Drive:</span> How It Works
            </h1>
            <p className="mt-[30px] text-[#373737] text-xl font-ltlight">
              We provide a simple process to bring jewelry designs to life,
              using professional tools for customization and seamless
              collaboration with clients.
            </p>
          </div>
          <div className="flex flex-col md:flex-row flex-wrap justify-center gap-[28px]">
            {[
              {
                sequence: "1",
                title: "Upload",
                imgSrc: "/workflow-upload.png",
              },
              {
                sequence: "2",
                title: "Apply materials",
                imgSrc: "/workflow-apply.png",
              },
              {
                sequence: "3",
                title: "Share with clients",
                imgSrc: "/workflow-share.png",
              },
            ].map((item) => (
              <div
                key={item.sequence}
                className="flex flex-col bg-[#FFFFFF] p-[30px] rounded-[30px] gap-2.5"
              >
                <div className="flex items-center gap-2.5">
                  <span className="min-h-[36px] min-w-[36px] border-2 border-[#373737] rounded-full flex items-center justify-center text-xl font-[500]">
                    {item.sequence}
                  </span>
                  <span className="font-sans text-2xl font-semibold">
                    {item.title}
                  </span>
                </div>
                <img
                  src={item.imgSrc}
                  className="max-w-[254px] max-h-[188px] object-cover pointer-events-none"
                />
              </div>
            ))}
          </div>
          <div className="flex flex-col md:flex-row gap-[30px]">
            {[
              {
                sequence: "4",
                title: "Collaborate",
                imgSrc: "/workflow-collab.png",
              },
              {
                sequence: "5",
                title: "Make adjustments",
                imgSrc: "/workflow-adjustment.png",
              },
            ].map((item) => (
              <div
                key={item.sequence}
                className="bg-[#FFFFFF] p-[30px] rounded-[30px]"
              >
                <div className="leading-[50px] flex items-center justify-start gap-2.5">
                  <span className="min-h-[36px] min-w-[36px] border-2 border-[#373737] rounded-full flex items-center justify-center text-xl font-[500]">
                    {item.sequence}
                  </span>
                  <span className="flex items-center font-sans justify-center text-2xl font-semibold">
                    {item.title}
                  </span>
                </div>
                <img
                  src={item.imgSrc}
                  className="pt-2.5 max-w-[254px] max-h-[188px] lg:max-w-[425px] lg:max-h-[206px] pointer-events-none"
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
