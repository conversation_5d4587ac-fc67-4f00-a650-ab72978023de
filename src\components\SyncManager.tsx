import { FC, useState, useCallback } from "react";
import { Button, Progress, Input, Chip, Modal, <PERSON><PERSON>ontent, <PERSON>dalHeader, ModalBody, Avatar } from "@nextui-org/react";
import { useUser } from "../provider/UserProvider";
import { SyncProgress } from "../api/api";
import toast from "react-hot-toast";
import Design from "./icons/Design.tsx";

interface SyncState {
  isActive: boolean;
  progress: SyncProgress | null;
  result: {
    success: boolean;
    message: string;
    count: number;
    error?: string;
  } | null;
}



const SyncManager: FC = () => {
  const { user, api } = useUser();
  const [designSync, setDesignSync] = useState<SyncState>({
    isActive: false,
    progress: null,
    result: null
  });
  const [instanceSync, setInstanceSync] = useState<SyncState>({
    isActive: false,
    progress: null,
    result: null
  });
  const [designPassword, setDesignPassword] = useState("");
  const [instancePassword, setInstancePassword] = useState("");
  const [profile, setProfile] = useState<{
    name?: string;
    email?: string;
    avatar?: string;
  }>();

  // Modal states
  const [showDesignModal, setShowDesignModal] = useState(false);
  const [showInstanceModal, setShowInstanceModal] = useState(false);

  const isEnterprise = user?.meta?.plan === "enterprise";

  const handleDesignSync = useCallback(async () => {
    if (!api || !designPassword.trim()) {
      toast.error("Please enter your iJewel Design password");
      return;
    }

    setDesignSync(prev => ({ ...prev, isActive: true, progress: null, result: null }));

    try {
      const result = await api.syncFromDesign(designPassword, (progress) => {
        if(progress.profile){
          setProfile(progress.profile);
        }
        setDesignSync(prev => ({ ...prev, progress }));
      });
      if(result.error === "Sync stopped"){
        return;
      }
      setDesignSync(prev => ({
        ...prev,
        isActive: false,
        result: {
          success: result.success,
          message: result.message,
          count: result.projectsCount,
          error: result.error
        }
      }));
   
      if (result.success) {
        toast.success(`Successfully synced ${result.projectsCount} projects`);
        setDesignPassword("");
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      setDesignSync(prev => ({
        ...prev,
        isActive: false,
        result: {
          success: false,
          message: "Sync failed",
          count: 0,
          error: error instanceof Error ? error.message : "Unknown error"
        }
      }));
      toast.error("Sync failed");
    }
  }, [api, designPassword]);

  const handleInstanceSync = useCallback(async () => {
    if (!api || !instancePassword.trim()) {
      toast.error("Please enter your password");
      return;
    }

    setInstanceSync(prev => ({ ...prev, isActive: true, progress: null, result: null }));
    const instanceBasename = api.getSharedDriveBucket()
    try {
      const result = await api.syncFromInstance(instanceBasename, instancePassword, (progress) => {
        if(progress.profile){
          setProfile(progress.profile);
        }
        setInstanceSync(prev => ({ ...prev, progress }));
      });
      if(result.error === "Sync stopped"){
        return;
      }
      setInstanceSync(prev => ({
        ...prev,
        isActive: false,
        result: {
          success: result.success,
          message: result.message,
          count: result.filesCount,
          error: result.error
        }
      }));
      if (result.success) {
        toast.success(`Successfully synced ${result.filesCount} files`);
        setInstancePassword("");
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      setInstanceSync(prev => ({
        ...prev,
        isActive: false,
        result: {
          success: false,
          message: "Sync failed",
          count: 0,
          error: error instanceof Error ? error.message : "Unknown error"
        }
      }));
      toast.error("Sync failed");
    }
  }, [api, instancePassword]);

  const resetDesignSync = useCallback(() => {
    setDesignSync({ isActive: false, progress: null, result: null });
    setDesignPassword("");
  }, []);

  const resetInstanceSync = useCallback(() => {
    setInstanceSync({ isActive: false, progress: null, result: null });
    setInstancePassword("");
  }, []);

  const handleCloseDesignModal = useCallback(() => {
    if (!designSync.isActive) {
      resetDesignSync();
    }
    setShowDesignModal(false);
  }, [designSync.isActive, resetDesignSync]);

  const handleCloseInstanceModal = useCallback(() => {
    if (!instanceSync.isActive) {
      resetInstanceSync();
    }
    setShowInstanceModal(false);
  }, [instanceSync.isActive, resetInstanceSync]);

  const handleStopDesignSync = useCallback(() => {
    if (api) {
      api.stopDesignSyncOperation();
    }
    resetDesignSync();
    setShowDesignModal(false);
    toast("Design sync stopped");
  }, [api, resetDesignSync]);

  const handleStopInstanceSync = useCallback(() => {
    if (api) {
      api.stopInstanceSyncOperation();
    }
    resetInstanceSync();
    setShowInstanceModal(false);
    toast("Instance sync stopped");
  }, [api, resetInstanceSync]);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-start gap-4 py-4 border-b md:ml-8">
        <Design className="h-10 w-fit" />
        <div className="">
          <div className="flex items-center gap-2">
            <p className="text-gray-700 font-medium">iJewel Design Sync</p>
          </div>
          <p className="text-gray-500 text-sm">Import projects from your iJewel Design account</p>
        </div>
        <div className="flex-1" />
        <div>
          <Button
            size="md"
            color="primary"
            name="Start Sync"
            fullWidth={false}
            onClick={() => setShowDesignModal(true)}
          >
            Start Sync
          </Button>
        </div>
      </div>

      {isEnterprise && (
        <div className="flex items-center justify-start gap-4 py-4 border-b md:ml-8">
          <img src="/logo-sm.svg" alt="iJewel Drive" className="h-10 w-fit" />
          <div className="">
            <div className="flex items-center gap-2">
              <p className="text-gray-700 font-medium">Shared Instance Sync</p>
            </div>
            <p className="text-gray-500 text-sm">Import files from Shared iJewel Drive instance</p>
          </div>
          <div className="flex-1" />
          <div className="flex items-center gap-2">
            <Button
              size="md"
              color="primary"
              name="Start Sync"
              fullWidth={false}
              onClick={() => setShowInstanceModal(true)}
            >
              Start Sync
            </Button>
          </div>
        </div>
      )}

      <Modal
        isOpen={showDesignModal}
        onClose={handleCloseDesignModal}
        isDismissable={!designSync.isActive}
        hideCloseButton={designSync.isActive}
        size="xl"
        placement="center"
        backdrop="blur"
      >
        <ModalContent>
          <ModalHeader className="text-xl font-semibold">
            iJewel Design Sync
          </ModalHeader>
          <ModalBody className="pb-6">
            {!designSync.isActive && !designSync.result && (
              <div className="space-y-4">
                <p className="text-gray-600 text-sm">
                  Import projects from your iJewel Design account. Enter your password to begin the sync process.
                </p>
                <Input
                  type="password"
                  autoComplete="new-password"
                  label="iJewel Design Password"
                  placeholder="Enter your password"
                  value={designPassword}
                  onValueChange={setDesignPassword}
                  variant="bordered"
                />
                <div className="flex justify-end gap-3">
                  <Button
                    onClick={handleCloseDesignModal}
                    size="lg"
                    fullWidth={false}
                  >
                    Cancel
                  </Button>
                  <Button
                    color="primary"
                    onClick={handleDesignSync}
                    isDisabled={!designPassword.trim()}
                    size="lg"
                    fullWidth={false}
                  >
                    Start Sync
                  </Button>
                </div>
              </div>
            )}

            {designSync.isActive && designSync.progress && (
              <div className="space-y-4">
                <SyncProgressDisplay progress={designSync.progress} profile={profile} />
                <div className="flex justify-center pt-4">
                  <Button
                    color="danger"
                    onClick={handleStopDesignSync}
                  >
                    Stop Sync
                  </Button>
                </div>
              </div>
            )}

            {designSync.result && (
              <SyncResultDisplay
                result={designSync.result}
                onReset={resetDesignSync}
                type="projects"
              />
            )}
          </ModalBody>
        </ModalContent>
      </Modal>

      <Modal
        isOpen={showInstanceModal}
        onClose={handleCloseInstanceModal}
        isDismissable={!instanceSync.isActive}
        hideCloseButton={instanceSync.isActive}
        size="xl"
        placement="center"
        backdrop="blur"
      >
        <ModalContent>
          <ModalHeader className="text-xl font-semibold">
            Shared Instance Sync
          </ModalHeader>
          <ModalBody className="pb-6">
            {!instanceSync.isActive && !instanceSync.result && (
              <div className="space-y-4">
                <p className="text-gray-600 text-sm">
                  Import files from Shared iJewel Drive instance. Enter the instance details to begin the sync process.
                </p>
                <Input
                  type="password"
                  autoComplete="new-password"
                  label="Password"
                  placeholder="Enter your password for the source instance"
                  value={instancePassword}
                  onValueChange={setInstancePassword}
                  variant="bordered"
                />
                <div className="flex justify-end gap-3">
                  <Button
                    onClick={handleCloseInstanceModal}
                    size="lg"
                    fullWidth={false}
                  >
                    Cancel
                  </Button>
                  <Button
                    color="primary"
                    onClick={handleInstanceSync}
                    isDisabled={!instancePassword.trim()}
                    size="lg"
                    fullWidth={false}
                  >
                    Start Sync
                  </Button>
                </div>
              </div>
            )}

            {instanceSync.isActive && instanceSync.progress && (
              <div className="space-y-4">
                <SyncProgressDisplay progress={instanceSync.progress} profile={profile} />
                <div className="flex justify-center pt-4">
                  <Button
                    color="danger"
                    onClick={handleStopInstanceSync}
                  >
                    Stop Sync
                  </Button>
                </div>
              </div>
            )}

            {instanceSync.result && (
              <SyncResultDisplay
                result={instanceSync.result}
                onReset={resetInstanceSync}
                type="files"
              />
            )}
          </ModalBody>
        </ModalContent>
      </Modal>
    </div>
  );
};

interface SyncProgressDisplayProps {
  progress: SyncProgress;
  profile?: {
    name?: string;
    email?: string;
    avatar?: string;
  };
}

const SyncProgressDisplay: FC<SyncProgressDisplayProps> = ({ progress , profile }) => {
  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'authenticating': return 'warning' as const;
      case 'fetching': return 'primary' as const;
      case 'creating_folder': return 'primary' as const;
      case 'syncing': return 'primary' as const;
      case 'complete': return 'success' as const;
      default: return 'primary' as const;
    }
  };

  const getStageLabel = (stage: string) => {
    switch (stage) {
      case 'authenticating': return 'Authenticating';
      case 'fetching': return 'Fetching Data';
      case 'creating_folder': return 'Creating Folder';
      case 'syncing': return 'Syncing';
      case 'complete': return 'Complete';
      default: return stage;
    }
  };

  return (
    <div className="space-y-4">
      {profile && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <Avatar
              src={profile.avatar}
              name={profile.name || profile.email}
              size="lg"
              className="flex-shrink-0"
            />
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-gray-900 truncate">
                {profile.name || 'Unknown User'}
              </h3>
              <p className="text-sm text-gray-600 truncate">{profile.email}</p>
            </div>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between">
        <Chip
          size="sm"
          variant="flat"
          color={getStageColor(progress.stage)}
        >
          {getStageLabel(progress.stage)}
        </Chip>
        <span className="text-sm font-medium text-gray-700">{progress?.progress?.toFixed(1)}%</span>
      </div>

      <Progress
        value={progress.progress}
        color={getStageColor(progress.stage)}
        className="w-full"
        size="sm"
      />

      <div className="space-y-2">
        <p className="text-sm text-gray-700">{progress.message}</p>
        {progress.error && (
          <p className="text-xs text-red-600">Error: {progress.error}</p>
        )}
      </div>
    </div>
  );
};

interface SyncResultDisplayProps {
  result: {
    success: boolean;
    message: string;
    count: number;
    error?: string;
  };
  onReset: () => void;
  type: 'projects' | 'files';
}

const SyncResultDisplay: FC<SyncResultDisplayProps> = ({ result, onReset, type }) => {
  return (
    <div className="space-y-4">
      <div className={`p-4 rounded-lg ${result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
        <div className="flex items-center justify-between mb-2">
          <Chip
            size="sm"
            variant="flat"
            color={result.success ? 'success' : 'danger'}
          >
            {result.success ? 'Success' : 'Failed'}
          </Chip>
          {result.success && (
            <span className="text-sm font-medium text-green-700">
              {result.count} {type} synced
            </span>
          )}
        </div>
        <p className={`text-sm ${result.success ? 'text-green-700' : 'text-red-700'}`}>
          {result.message}
        </p>
        {result.error && (
          <p className="text-xs text-red-600 mt-1">Error: {result.error}</p>
        )}
      </div>

      <div className="flex gap-2">
        <Button
          onClick={onReset}
          className="flex-1"
        >
          Start New Sync
        </Button>
        {!result.success && (
          <Button
            color="primary"
            onClick={onReset}
            className="flex-1"
          >
            Retry
          </Button>
        )}
      </div>
    </div>
  );
};

export default SyncManager;
