import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <mask id="mask0_95_2805" style={{maskType: "alpha",}} maskUnits="userSpaceOnUse" x="0" y="0" width="20"
              height="20">
            <rect width="20" height="20" fill="#D9D9D9"/>
        </mask>
        <g mask="url(#mask0_95_2805)">
            <path
                d="M5 16C3.88889 16 2.94444 15.6111 2.16667 14.8333C1.38889 14.0556 1 13.1111 1 12C1 10.9444 1.36111 10.0313 2.08333 9.26042C2.80556 8.48958 3.6875 8.06944 4.72917 8C5.04861 6.81944 5.69097 5.85764 6.65625 5.11458C7.62153 4.37153 8.73611 4 10 4C11.4306 4 12.6736 4.48264 13.7292 5.44792C14.7847 6.41319 15.375 7.59722 15.5 9C16.4722 9 17.2986 9.34028 17.9792 10.0208C18.6597 10.7014 19 11.5278 19 12.5C19 13.4722 18.6597 14.2986 17.9792 14.9792C17.2986 15.6597 16.4722 16 15.5 16H5ZM5 14.5H15.5C16.0556 14.5 16.5278 14.3056 16.9167 13.9167C17.3056 13.5278 17.5 13.0556 17.5 12.5C17.5 11.9444 17.3056 11.4722 16.9167 11.0833C16.5278 10.6944 16.0556 10.5 15.5 10.5H14.125L14 9.14583C13.9028 8.11806 13.4722 7.25347 12.7083 6.55208C11.9444 5.85069 11.0417 5.5 10 5.5C9.11111 5.5 8.3125 5.76736 7.60417 6.30208C6.89583 6.83681 6.42361 7.54167 6.1875 8.41667L5.89583 9.4375L4.83333 9.5C4.16667 9.54167 3.61111 9.80208 3.16667 10.2812C2.72222 10.7604 2.5 11.3333 2.5 12C2.5 12.6944 2.74306 13.2847 3.22917 13.7708C3.71528 14.2569 4.30556 14.5 5 14.5Z"
                fill="#373737"/>
        </g>
    </svg>
)
export default SvgComponent

