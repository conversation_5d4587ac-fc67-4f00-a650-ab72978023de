import { useRef } from 'react';
import { Dropdown, DropdownTrigger, DropdownMenu, DropdownItem, Button as NextButton } from "@nextui-org/react";
import Plus from "./icons/Plus2";
import Folder from "./icons/Folder";
import UploadFile from "./icons/UploadFile";
import UploadFolder from "./icons/UploadFolder";
import { useModal } from "../provider/useModal";
import { useBrowser } from "../provider/BrowserProvider";
import { is3dFile } from './shared/util';

interface AddNewButtonProps {
    compact?: boolean;
    className?: string;
    onUpload?: (files: { file: File, path: string }[], options?: any) => Promise<void>;
}

declare module 'react' {
    interface InputHTMLAttributes<T> extends HTMLAttributes<T> {
        directory?: string;
        webkitdirectory?: string;
    }
}

export const AddNewButton = ({ compact = false, className = '', onUpload }: AddNewButtonProps) => {
    const dropdownRef = useRef<HTMLDivElement>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const folderInputRef = useRef<HTMLInputElement>(null);
    const { openModal, openUploadPrompt } = useModal();
    const { setShowUploadProgress, uploadOptions, setUploadOptions } = useBrowser();

    const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(event.target.files || []).map(file => ({
            file,
            path: file.name
        }));
        
        if (files.length > 0 && onUpload) {
            let options = uploadOptions;
            
            // Check if any file is a 3D model
            const has3dmFile = is3dFile(files.map(file => file.file)) && files.some(file => file.file.name.endsWith('.3dm'));
            const has3dFile = is3dFile(files.map(file => file.file));
            
            if (has3dFile) {
                options = await openUploadPrompt(uploadOptions!, has3dmFile);
                if (options === null) return;
                setUploadOptions({...options});
            }

            setShowUploadProgress(true);
            await onUpload(files, options || {});
        }
        
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    const handleFolderUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(event.target.files || []).map(file => ({
            file,
            path: file.webkitRelativePath || file.name
        }));
        
        if (files.length > 0 && onUpload) {
            let options = uploadOptions;
            
            const has3dmFile = is3dFile(files.map(file => file.file)) && files.some(file => file.file.name.endsWith('.3dm'));
            const has3dFile = is3dFile(files.map(file => file.file));
            if (has3dFile) {
                options = await openUploadPrompt(uploadOptions!, has3dmFile);
                if (options === null) return;
                setUploadOptions({...options});
            }
            
            if (has3dFile) {
                // Pass "3dm" as fileType if any file is a 3dm file
                options = await openUploadPrompt(uploadOptions!, has3dmFile);
                if (options === null) return;
                setUploadOptions({...options});
            }

            setShowUploadProgress(true);
            await onUpload(files, options || {});
        }
        
        if (folderInputRef.current) {
            folderInputRef.current.value = '';
        }
    };

    return (
        <div ref={dropdownRef} className="relative">
            <Dropdown>
                <DropdownTrigger>
                    <NextButton
                        className={`h-8 bg-white drop-shadow-md rounded-3xl ${
                            !compact ? 'w-[128px]' : 'w-10'
                        } ${className}`}
                        size="sm"
                        variant="shadow"
                    >
                        <div className="flex items-center gap-3 justify-center">
                            <Plus className="w-5 h-5"/>
                            {!compact && <span className="text-sm">Add new</span>}
                        </div>
                    </NextButton>
                </DropdownTrigger>
                <DropdownMenu 
                    aria-label="Add new options" 
                    className="w-[210px]"
                >
                    <DropdownItem
                        key="new-folder"
                        startContent={<Folder className="w-5 h-5"/>}
                        className="py-3 data-[hover=true]:bg-gray-100"
                        onClick={() => openModal("CREATE_FOLDER")}
                    >
                        <span className="text-gray-600 text-sm">New Folder</span>
                    </DropdownItem>
                    <DropdownItem
                        key="file-upload"
                        startContent={<UploadFile className="w-5 h-5"/>}
                        className="py-3 data-[hover=true]:bg-gray-100"
                        onClick={() => fileInputRef.current?.click()}
                    >
                        <span className="text-gray-600 text-sm">File upload</span>
                    </DropdownItem>
                    <DropdownItem
                        key="folder-upload"
                        startContent={<UploadFolder className="w-5 h-5"/>}
                        className="py-3 data-[hover=true]:bg-gray-100"
                        onClick={() => folderInputRef.current?.click()}
                    >
                        <span className="text-gray-600 text-sm">Folder upload</span>
                    </DropdownItem>
                </DropdownMenu>
            </Dropdown>

            <input 
                type="file"
                ref={fileInputRef}
                className="hidden"
                multiple
                onChange={handleFileUpload}
            />
            
            <input 
                type="folder"
                ref={folderInputRef}
                className="hidden"
                webkitdirectory=""
                directory=""
                multiple
                onChange={handleFolderUpload}
            />
        </div>
    );
};
