import { FC, useCallback, useEffect, useState } from "react";
import { Modal } from "../Modal";
import { CustomFileData } from "../../api/api";
import { Input, Switch as NextUISwitch, Select, SelectItem } from "@nextui-org/react";
import { Button } from "../Button";
import toast from "react-hot-toast";
import { fileMetaData } from "../shared/types";
import { Emails } from "../Emails";
import { useModal } from "../../provider/useModal";
import { useUser } from "../../provider/UserProvider";
import { useParams } from "react-router-dom";

interface ShareModalProps {
  file: CustomFileData;
  updateFilePermissions: (file: CustomFileData, permissions: Partial<fileMetaData>) => Promise<CustomFileData | null>;
  onClose: () => void;
}

const ShareModal: FC<ShareModalProps> = (props) => {
  const { modalState } = useModal();
  const [pub, setPub] = useState(props.file?.meta?.pub);
  const [r, setR] = useState(props.file?.meta?.r);
  const [link, setLink] = useState<string>("");
  const [downloadLink , setDownloadLink] = useState<string>("");
  const {config , api, getPublicShareUrls} = useUser();
  const [view, setView] = useState<"share" | "settings" | "domain">("share");
  const [passwordProtected, setPasswordProtected] = useState(false);
  const [allowDownload, setAllowDownload] = useState(false);
  const [password, setPassword] = useState("");
  const [selectedDomain, setSelectedDomain] = useState("jewelry.com");
  const [domains] = useState([
    { name: "jewelry.com", status: "Connected" },
    { name: "jewelrygold.com", status: "" },
    { name: "jewelryshop.com", status: "" }
  ]);
  const { basename } = useParams();

  useEffect(() => {
    if (!props.file || !api) return;
    setLink(`${window.location.origin + "/" + basename  + (props.file?.isDir ? "/folders/" : "/files/") + props.file?.id + (props.file?.isDir ? "" : "/view")}`)
    setDownloadLink(api?.getDownloadUrl(props.file , false));
  }, [api, basename, props.file]);

  const setPublic = useCallback(
    async (value: boolean) => {
      if(!config?.features?.["public-sharing"] && value){
        toast.error("Public sharing is disabled by the admin");
        return;
      }
      setPub(value);
      await props.updateFilePermissions(props.file, { pub: value, r: r });
    },
    [config, props, r]
  );

  const copyLink = useCallback(() => {
    toast.success("Link copied to clipboard", { id: "copy" });
    navigator.clipboard.writeText(link);
  }, [link]);

  const copyDownloadLink = useCallback(() => {
    toast.success("Download Link copied to clipboard", { id: "copy" });
    navigator.clipboard.writeText(downloadLink);
  }, [downloadLink]);

  const updateEmails = useCallback(
    async (emails: string[]) => {
      console.log("updating emails: ", emails);
      setR(emails);
      await props.updateFilePermissions(props.file, { r: emails, pub: pub });
    },
    [props, pub]
  );

  const handleLinkSettings = () => {
    setView("settings");
  };

  const handleBack = () => {
    setView("share");
  };

  const handleManageDomain = () => {
    setView("domain");
  };

  const LinkSettingsView = () => (
    <div className="w-full p-4 flex flex-col gap-8 font-normal rounded-3xl">
      <h2 className="text-2xl">Shared Link Settings</h2>

      <div className="space-y-4">
        <div>
          <p className="text-gray-700 text-sm">
            This content is publicly available to anyone with the link, and can be viewed.{" "}
            <Button 
              varient="light" 
              className="text-[#6366f1] hover:text-blue-700 p-0"
              name="Unshare file"
              fullWidth={false}
            >
            </Button>
          </p>
        </div>

        <div className="border-t pt-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm">Link Expiration</h3>
            <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm">Valid</span>
          </div>
          <p className="text-gray-600 text-sm">Valid until 22 May 2024 9:30 AM</p>
        </div>

        <div className="border-t pt-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm mb-1">Password Protect</h3>
              <p className="text-gray-600 text-sm">Only people who know the password will be able to access the contents of this link.</p>
            </div>
            <NextUISwitch 
              size="lg"
              isSelected={passwordProtected}
              onValueChange={setPasswordProtected}
              className="ml-4"
            />
          </div>
          {passwordProtected && (
            <Input
              type="password"
              placeholder="Link password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="mt-4 font-normal"
              classNames={{
                inputWrapper: "h-12 rounded-full bg-gray-50",
              }}
            />
          )}
        </div>

        <div className="border-t pt-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm mb-1">Allow download</h3>
              <p className="text-gray-600 text-sm">Allow users with shared link to download this item.</p>
            </div>
            <NextUISwitch 
              size="lg"
              isSelected={allowDownload}
              onValueChange={setAllowDownload}
              className="ml-4"
            />
          </div>
        </div>

        <div className="flex justify-end gap-4 pt-4 border-t mt-8">
          <Button
            size="lg"
            varient="light"
            onClick={handleBack}
            className="rounded-full px-8"
            name="Cancel"
            fullWidth={false}
          >
          </Button>
          <Button
            size="lg"
            onClick={handleBack}
            className="rounded-full px-8 bg-[#6366f1] text-white"
            name="Save"
            fullWidth={false}
          >
          </Button>
        </div>
      </div>
    </div>
  );

  const ManageDomainView = () => (
    <div className="w-full bg-white p-8 flex flex-col gap-8 font-normal rounded-3xl">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl">Manage Domain "{props.file?.name}"</h2>
        <Button
          varient="light"
          className="p-2"
          onClick={() => setView("share")}
          name="x"
          fullWidth={false}
        >
        </Button>
      </div>

      <div className="space-y-4">
        <div>
          <h3 className="text-sm mb-4">Assigned Domain</h3>
          <Select
            defaultSelectedKeys={[selectedDomain]}
            value={selectedDomain}
            onChange={(e) => setSelectedDomain(e.target.value)}
            className="font-normal"
            classNames={{
              trigger: "h-12 rounded-full bg-gray-50",
              value: "text-default-700"
            }}
          >
            {domains.map((domain) => (
              <SelectItem key={domain.name} value={domain.name}>
                {domain.name}
              </SelectItem>
            ))}
          </Select>
        </div>

        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm">My Domains</h3>
            <Button
              className="rounded-full px-6 bg-[#6366f1] text-white"
              size="lg"
              name="Add New"
              fullWidth={false}
            >
            </Button>
          </div>
          <Input
            placeholder="mywebsite.com"
            className="mb-6 font-normal"
            classNames={{
              inputWrapper: "h-12 rounded-full bg-gray-50",
            }}
          />
          <div className="space-y-4">
            {domains.map((domain) => (
              <div key={domain.name} className="flex items-center justify-between py-2 border-b">
                <div className="flex items-center gap-3">
                  <span className="text-gray-900 text-sm">{domain.name}</span>
                  {domain.status && (
                    <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm">
                      {domain.status}
                    </span>
                  )}
                </div>
                <Button
                  varient="light"
                  className="text-[#6366f1] hover:text-blue-700 p-0"
                  name="Delete"
                  fullWidth={false}
                >
                </Button>
              </div>
            ))}
          </div>
        </div>

        {domains.length === 0 && (
          <div className="text-center text-gray-600 py-12 text-sm">
            Here you will see all the domains.
          </div>
        )}
      </div>
    </div>
  );

  const decodeShareUrl = (url: string): string => {
    try {
      return decodeURIComponent(url);
    } catch (e) {
      console.error('Error decoding URL:', e);
      return url;
    }
  };

  const copyPublicShareURL = (domainName: string) =>{
    if(!pub){
      toast.error("Make file public first");
      return;
    }
    navigator.clipboard.writeText(domainName.replace("MODEL_ID",props.file.id));
    toast.success("Link copied to clipboard", { id: "copy" });
  }

  return (
    <Modal isOpen={modalState.modalType == "SHARE"} onClose={props.onClose} size="2xl" backdrop="blur">
      {() => {
        switch(view) {
          case "settings":
            return <LinkSettingsView />;
          case "domain":
            return <ManageDomainView />;
          default:
            return (
              <div className="w-full bg-white p-8 flex flex-col gap-8 font-normal rounded-3xl">
                <h2 className="text-2xl">Share "{props.file?.name}"</h2>
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm">Invite People</span>
                      <div className="flex items-center gap-2">
                        <span className="text-gray-600 text-sm">Shared with</span>
                        <span className="w-8 h-8 flex items-center justify-center bg-[#6366f1] text-white rounded-full">
                          {r?.length || 0}
                        </span>
                      </div>
                    </div>
                    <Emails updateEmails={updateEmails} emails={r} />
                    {/*<div className="text-[#6366f1] mt-2 cursor-pointer">can edit ▼</div>*/}
                  </div>
                  <div>
                    <div className="flex items-center justify-between mb-4 border-t pt-3">
                      <div className="flex items-center gap-4">
                        <h3 className="text-sm">Share Link</h3>
                        <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm">Valid</span>
                      </div>
                      {(
                          <div className="flex gap-unit-lg items-center">
                            <p className=" text-black text-md">Public Access</p>
                            <NextUISwitch size="sm" isSelected={pub} onValueChange={(v) => setPublic(v)}></NextUISwitch>
                          </div>
                      )}
                      {/*<button */}
                      {/*  className="text-[#6366f1] hover:text-blue-700 text-sm"*/}
                      {/*  onClick={handleLinkSettings}*/}
                      {/*>*/}
                      {/*  Link Settings*/}
                      {/*</button>*/}
                    </div>
                    <div className="flex flex-col">
                      {getPublicShareUrls().map((url: string, index: number) => {
                        const decodedUrl = decodeShareUrl(url);
                        return (<Input
                          key={index + decodedUrl}
                          onClick={(e) => {
                            e.preventDefault();
                            copyPublicShareURL(decodedUrl);
                          }}
                          size="md"
                          value={decodedUrl.replace("MODEL_ID", props.file.id)}
                          readOnly
                          className="font-normal mb-4"
                          classNames={{
                            inputWrapper: `h-12 rounded-xl ${
                              pub ? "bg-gray-50" : "bg-white opacity-25"
                            }`,
                          }}
                        />)
                      })}
                      <Input
                          onClick={copyLink}
                          size="md"
                          value={link}
                          readOnly
                          className="font-normal"
                          classNames={{
                            inputWrapper: "h-12 rounded-xl bg-gray-50",
                          }}
                      />
                      {/* <Button
                          size="lg"
                          color="primary"
                          onClick={copyLink}
                          name="Copy"
                          className="rounded-full px-8 bg-[#6366f1]"
                      >
                      </Button> */}
                    </div>
                  </div>

                  {/*<div>*/}
                  {/*  <div className="flex items-center justify-between mb-4 border-t pt-3">*/}
                  {/*    <div className="flex items-center gap-4">*/}
                  {/*      <h3 className="text-sm">Assigned Domain</h3>*/}
                  {/*      <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm">Connected</span>*/}
                  {/*    </div>*/}
                  {/*      <button */}
                  {/*        className="text-[#6366f1] hover:text-blue-700 text-sm"*/}
                  {/*        onClick={handleManageDomain}*/}
                  {/*      >*/}
                  {/*        Manage*/}
                  {/*      </button>*/}
                  {/*  </div>*/}
                  {/*  <Input*/}
                  {/*    label="Domain"*/}
                  {/*    value="jewelry.com"*/}
                  {/*    readOnly*/}
                  {/*    className="font-normal"*/}
                  {/*    classNames={{*/}
                  {/*      inputWrapper: "h-12 rounded-full bg-[#F7F7F7]",*/}
                  {/*    }}*/}
                  {/*  />*/}
                  {/*</div>*/}

                  <div className="hidden">
                    {!props.file.isDir && (
                      <>
                        <div className="flex gap-unit-lg items-center">
                          <p className=" text-black font-semibold text-sm">Public Access</p>
                          <NextUISwitch size="md" isSelected={pub} onValueChange={(v) => setPublic(v)}></NextUISwitch>
                        </div>
                        <div className="flex gap-unit-lg items-center">
                          <Input
                            onClick={copyDownloadLink}
                            label="Download Link"
                            size="lg"
                            value={downloadLink}
                            readOnly
                          />
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            );
        }
      }}
    </Modal>
  );
};
export default ShareModal;
