import {useCallback, useEffect, useState} from "react";
import {useUser} from "../provider/UserProvider";
import toast from "react-hot-toast";
import {Outlet, useLocation, useParams} from "react-router-dom";
import ModalProvider from "../provider/ModalProvider";
import {Button, Input} from "@nextui-org/react";
import {Logo} from "./Logo";
import { useBaseNavigate } from "../hooks/UseBaseNavigate";

// Validation function that returns error message or undefined
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function validateField(type: string, value: string, compareValue?: string): string | undefined {
  switch (type) {
    case "password":
      if (!value.trim()) return "Password is required";
      if (value.length < 8) return "Password must be at least 8 characters long";
      break;
    case "passwordConfirm":
      if (!value.trim()) return "Confirm password is required";
      if (value !== compareValue) return "Passwords do not match";
      break;
    default:
      break;
  }
  return undefined;
}

function ResetPassword() {
  const { setIsLogin, api, setUser } = useUser();
  const [submitted, setSubmitted] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const location = useLocation();
  const navigate = useBaseNavigate();
  const { token} = useParams();

  const [password, setPassword] = useState<string>("");
  const [passwordConfirm, setPasswordConfirm] = useState<string>("");

  // Error messages
  const passwordError = submitted ? validateField("password", password) : undefined;
  const passwordConfirmError = submitted ? validateField("passwordConfirm", passwordConfirm, password) : undefined;


  useEffect(() => {
    if(!token) {
      toast.error("Unable to reset password. Please try again.");
      navigate("/forgot-password");
    }
  }, [token, navigate]);

  const handleResetPassword = useCallback(async () => {
    setSubmitted(true);

    if (!api) {
      toast.error("API not initialized");
      return;
    }

    // Re-validate fields

    const passwordError = validateField("password", password);
    const passwordConfirmError = validateField("passwordConfirm", passwordConfirm, password);

    // Collect all errors
    const allErrors = [passwordError, passwordConfirmError].filter(Boolean);

    if (allErrors.length > 0) {
      toast.error(allErrors[0] as string);
      return;
    }

    setLoading(true);

    if(!token) return;
    const { user, error } = await api.confirmPasswordReset({
          password, passwordConfirm, token
    })

    setLoading(false);

    if (error) {
      toast.error(`${error}`);
    } else {
      toast.success("Password has been changed");
      setPassword("");
      setPasswordConfirm("");
      setIsLogin(true);
      setUser(user);
      navigate("/folders/"); // todo shouldnt it be done automatically
    }
  }, [api, navigate, password, passwordConfirm, setIsLogin, setUser, token]);

  const isAuthRoute = location.pathname.includes("/reset-password");

  return isAuthRoute ? (
    <div className="w-screen h-screen flex justify-center items-center fixed t-0 l-0 z-10 bg-white">
      <div className="w-unit-9xl flex flex-col gap-unit-xl p-4">
        <Logo className="h-unit-3xl" />
        <p className="text-3xl font-bold text-center">{"Create a new password"}</p>
        <form
          className="flex flex-col gap-unit-xl"
          onSubmit={(e) => {
            e.preventDefault();
            handleResetPassword();
          }}
        >
            <div className="flex gap-2 items-center h-[38px]">
                <Input
                    required
                    onValueChange={setPassword}
                    value={password}
                    placeholder="New Password"
                    variant="bordered"
                    radius="full"
                    type="password"
                    classNames={{
                      base: "w-full",
                      input: "bg-transparent text-gray-600 placeholder:text-gray-400",
                      inputWrapper: "bg-white h-[38px] shadow-none border border-gray-200 hover:border-gray-300",
                      innerWrapper: "pr-4",
                      label: "text-sm text-gray-700"
                    }}
                    isInvalid={!!passwordError}
                    errorMessage={passwordError}
                />
              </div>
              <div className="flex gap-2 items-center h-[38px]">
                <Input
                    required
                    onValueChange={setPasswordConfirm}
                    value={passwordConfirm}
                    placeholder="Confirm New Password"
                    variant="bordered"
                    type="password"
                    radius="full"
                    classNames={{
                      base: "w-full",
                      input: "bg-transparent text-gray-600 placeholder:text-gray-400",
                      inputWrapper: "bg-white h-[38px] shadow-none border border-gray-200 hover:border-gray-300",
                      innerWrapper: "pr-4",
                      label: "text-sm text-gray-700"
                    }}
                    isInvalid={!!passwordConfirmError}
                    errorMessage={passwordConfirmError}
                />
              </div>

              <div className="flex gap-unit-xl w-full">
                <Button type="submit" isLoading={loading} className="h-11 w-full rounded-full bg-[#6E72EA] text-white font-medium" color="primary">
                  {"Reset Password"}
                </Button>
              </div>
              {/* <Button
                  variant="bordered"
                  className="h-12 w-full rounded-full border-gray-300 font-normal"
                  startContent={
                  <img src="https://www.google.com/favicon.ico" className="w-5 h-5" alt="Google" />
                  }
                >
                  Log in with Google
              </Button> */}
        </form>
      </div>
    </div>
  ) : (
    <ModalProvider>
      <Outlet />
    </ModalProvider>
  );
}

export default ResetPassword;
