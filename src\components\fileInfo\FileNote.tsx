import { useState } from 'react'
import { But<PERSON> } from '../Button';
import { CustomFileData } from '../../api/api';
import { useCustomFileMap } from '../../hooks/fileHooks';
import toast from 'react-hot-toast';
import Edit from '../icons/Edit';

function FileNote({ file }: { file: CustomFileData }) {
    const [note, setNotes] = useState<string>(file.notes ?? "");
    const [changenotes, setChangeNotes] = useState<boolean>((!file.notes || file.notes == "") ? true : false);
    const { updateNote } = useCustomFileMap();
    const handleSaveNote = async () => {
        try {
            await updateNote(file,note);
            setChangeNotes(false);
        } catch (error) {
            toast.error("Failed to save Note", { id: "updateNote" });
        }
    }
    return (
        <>
            <div className='flex items-center gap-3'  onClick={(e) => { e.preventDefault(); setChangeNotes(!changenotes) }}><label className="text-gray-500 text-sm block mb-1">Note</label><Edit className='w-3'/></div>
            {!changenotes ? <p className="text-gray-400 text-sm pb-3">{note}</p> :
                <>
                    <textarea
                        className="w-full p-3 border border-gray-200 rounded-lg text-sm"
                        placeholder="Enter a Note..."
                        rows={2}
                        value={note}
                        onChange={(e) => { setNotes(e.target.value) }}
                    />
                    {(file.notes != note) &&
                        <Button
                            name={<span className='pb-1 text-sm'>Save Note</span>}
                            size='md'
                            varient='solid'
                            onClick={handleSaveNote}
                            className='px-3 py-1 bg-gray-100 rounded-full min-w-unit-8 h-8 mb-3'
                        />
                    }
                </>
            }
        </>
    )
}

export default FileNote
