import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <mask id="mask0_2128_2544" style={{maskType: "alpha",}} maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
            <rect x="20" width="20" height="20" transform="rotate(90 20 0)" fill="#D9D9D9"/>
        </mask>
        <g mask="url(#mask0_2128_2544)">
            <path d="M7.78846 9.99987L11.4583 6.33008L11.4583 13.6697L7.78846 9.99987Z" fill="#686868"/>
        </g>
    </svg>
)
export default SvgComponent

