import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg width="159" height="30" viewBox="0 0 159 30" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path fillRule="evenodd" clipRule="evenodd" d="M4.1582 14.1502H5.05575C8.99609 14.3707 12.1525 17.5264 12.3733 21.4659V22.3638H4.1582V14.1502ZM4.1582 13.6939V5.48029H12.3821L12.3856 5.9366C12.3856 10.0795 9.14228 13.4652 5.05575 13.6939H4.1582ZM12.8173 5.48302V5.9366C12.8173 10.0795 16.0607 13.4652 20.1472 13.6939H21.0417C20.9227 9.20881 17.3048 5.59559 12.8173 5.48302ZM21.0417 14.1502H20.1472C16.2068 14.3707 13.0504 17.5263 12.8297 21.4659V22.3608C17.3114 22.2419 20.9228 18.6312 21.0417 14.1502Z" fill="#6E72F2"/>
        <path d="M150.998 14.9564C150.998 14.9729 150.982 14.9853 150.949 14.9935H150.998V15.2409H143.764C143.74 16.2054 143.954 17.1658 144.407 18.1221C144.869 19.0702 145.628 19.6678 146.683 19.9151C147.985 20.08 149.156 19.7585 150.195 18.9506C150.376 18.7857 150.541 18.6044 150.689 18.4065C150.706 18.3818 150.731 18.3653 150.763 18.3571C150.796 18.3488 150.829 18.3571 150.862 18.3818C150.887 18.3983 150.904 18.423 150.912 18.456C150.92 18.489 150.912 18.5219 150.887 18.5549C150.731 18.7528 150.562 18.9424 150.38 19.1237C149.399 20.2119 148.167 20.8632 146.683 21.0775C145.017 21.1352 143.707 20.5541 142.75 19.334C141.802 18.1056 141.295 16.7495 141.229 15.2656C141.18 14.0785 141.423 12.9697 141.959 11.9392C142.495 10.9005 143.332 10.1461 144.469 9.67624C145.401 9.32999 146.353 9.22694 147.326 9.36709C148.694 9.65563 149.688 10.4182 150.306 11.6548C150.792 12.7017 151.023 13.8023 150.998 14.9564ZM143.764 14.9935H148.439C148.447 14.9771 148.451 14.9647 148.451 14.9564C148.839 13.9342 149.045 12.9367 149.069 11.9639C149.028 10.7768 148.43 10.0019 147.276 9.63914C145.916 9.37533 144.906 9.84936 144.247 11.0612C143.859 12.1247 143.698 13.2294 143.764 14.3753C143.764 14.4412 143.764 14.5113 143.764 14.5855C143.764 14.7174 143.764 14.8534 143.764 14.9935Z" fill="#6E72F2"/>
        <path d="M140.475 9.86171C140.524 9.72157 140.508 9.59379 140.425 9.47837C140.351 9.35471 140.236 9.28876 140.079 9.28052H141.575C141.172 9.31349 140.887 9.51547 140.722 9.88644L136.468 20.9415L136.53 21.1023H136.407H136.147H133.909L129.605 9.92354C129.44 9.54432 129.152 9.32998 128.74 9.28052H132.598C132.449 9.29701 132.338 9.36708 132.264 9.49074C132.19 9.60615 132.177 9.73805 132.227 9.88644L136.345 20.5953L140.475 9.86171Z" fill="#6E72F2"/>
        <path d="M127.973 20.4098C127.973 20.5829 128.031 20.7354 128.146 20.8673C128.262 20.9992 128.41 21.0775 128.591 21.1022H124.906C125.088 21.0775 125.232 20.9992 125.339 20.8673C125.454 20.7354 125.512 20.5829 125.512 20.4098V11.1848C125.512 11.0035 125.454 10.8468 125.339 10.7149C125.232 10.583 125.088 10.5047 124.906 10.48L127.973 9.2434V20.4098ZM126.749 5.92936C126.394 5.92936 126.089 5.8057 125.834 5.55838C125.586 5.30282 125.463 4.9978 125.463 4.64331C125.463 4.29707 125.586 4.00029 125.834 3.75297C126.089 3.49741 126.394 3.36963 126.749 3.36963C127.103 3.36963 127.404 3.49741 127.651 3.75297C127.899 4.00029 128.022 4.29707 128.022 4.64331C128.022 4.9978 127.899 5.30282 127.651 5.55838C127.404 5.8057 127.103 5.92936 126.749 5.92936Z" fill="#6E72F2"/>
        <path d="M120.331 11.2467C120.38 11.1972 120.434 11.1436 120.491 11.0859C121.002 10.5583 121.555 10.1049 122.148 9.72568C122.742 9.34646 123.422 9.2228 124.189 9.3547C124.197 9.3547 124.205 9.3547 124.213 9.3547C124.255 9.36294 124.292 9.37119 124.325 9.37943C124.358 9.38768 124.387 9.40829 124.411 9.44126C124.411 9.44126 124.349 9.54843 124.226 9.76277C124.102 9.97711 123.962 10.2244 123.805 10.5047C123.649 10.785 123.509 11.0365 123.385 11.259C123.261 11.4734 123.199 11.5806 123.199 11.5806C123.183 11.5806 123.162 11.5682 123.138 11.5435C123.121 11.5187 123.101 11.4899 123.076 11.4569C123.059 11.4157 123.043 11.3786 123.026 11.3456C123.018 11.3291 123.01 11.3167 123.002 11.3085C122.672 10.8551 122.26 10.6778 121.765 10.7768C121.27 10.8757 120.846 11.09 120.491 11.4198C120.294 11.6094 120.108 11.8114 119.935 12.0257V20.4098C119.935 20.5829 119.993 20.7354 120.108 20.8673C120.223 20.9992 120.372 21.0775 120.553 21.1022H116.868C117.041 21.0775 117.186 20.9992 117.301 20.8673C117.416 20.7354 117.474 20.5829 117.474 20.4098V11.1848C117.474 11.0035 117.416 10.8468 117.301 10.7149C117.186 10.583 117.041 10.5047 116.868 10.48L119.935 9.24341V11.6424C120.059 11.5105 120.19 11.3786 120.331 11.2467Z" fill="#6E72F2"/>
        <path d="M110.141 3.55532C112.796 4.19835 114.494 5.94193 115.236 8.78607C115.492 9.92373 115.607 11.0696 115.582 12.2238C115.599 13.551 115.43 14.8453 115.075 16.1066C114.721 17.3597 114.078 18.452 113.146 19.3836C112.05 20.3646 110.789 20.917 109.362 21.0406C108.859 21.0818 108.357 21.1024 107.854 21.1024C107.812 21.1024 107.668 21.1024 107.421 21.1024C107.174 21.1024 106.897 21.1024 106.592 21.1024C106.287 21.1024 106.011 21.1024 105.764 21.1024C105.525 21.1024 105.389 21.1024 105.356 21.1024H105.343H102.821C103.233 20.9376 103.439 20.6655 103.439 20.2863V4.19835C103.439 3.81913 103.233 3.54708 102.821 3.3822H105.888L107.854 3.39457C108.348 3.37808 108.855 3.39457 109.375 3.44403C109.63 3.46052 109.886 3.49762 110.141 3.55532ZM109.239 20.5089C110.681 19.7175 111.72 18.5633 112.355 17.0465C112.99 15.5213 113.303 13.922 113.295 12.2485C113.319 10.542 113.01 8.94271 112.367 7.45056C111.732 5.95017 110.69 4.79603 109.239 3.98813C108.802 3.76554 108.34 3.64601 107.854 3.62952L105.888 3.60479V20.8799C106.127 20.8799 106.39 20.8799 106.679 20.8799C106.967 20.8799 107.223 20.8799 107.446 20.8799C107.676 20.8799 107.812 20.8799 107.854 20.8799C108.332 20.8799 108.793 20.7562 109.239 20.5089Z" fill="#6E72F2"/>
        <path d="M35.6683 11.3353C35.6683 11.1525 35.6098 10.9917 35.4928 10.8528C35.3758 10.7138 35.2259 10.6261 35.0431 10.5822L38.1432 9.3064V23.7318C38.1432 24.6896 37.8982 25.545 37.4047 26.2945C36.9149 27.0439 36.1727 27.4314 35.1784 27.457C34.0597 27.3912 33.2628 26.8245 32.7766 25.7571C32.5865 25.3147 32.4878 24.8614 32.4878 24.3935H32.7364C32.7364 24.8358 32.8241 25.2526 32.9996 25.6437C33.1897 25.9837 33.4895 26.2067 33.8916 26.3054C34.2974 26.4041 34.6593 26.3676 34.9846 26.1921C35.2113 26.0495 35.3685 25.8119 35.4672 25.4792C35.5622 25.1466 35.6207 24.8139 35.6354 24.4776C35.6536 24.1449 35.6609 23.9073 35.6609 23.7647L35.6683 11.3353Z" fill="#2E2E2E"/>
        <path d="M31.5626 11.595C31.5626 11.4122 31.5041 11.2514 31.3872 11.1125C31.2702 10.9735 31.1203 10.8858 30.9375 10.8419L34.0375 9.5661V19.6741L31.5626 20.95V11.595Z" fill="#2E2E2E"/>
        <path d="M48.8936 15.1229V15.3715H41.5786C41.553 16.3475 41.7723 17.3163 42.2366 18.2778C42.6972 19.2392 43.4649 19.8461 44.5324 20.0983C45.8484 20.2665 47.0329 19.9411 48.0821 19.1222C48.2649 18.9541 48.433 18.7713 48.5829 18.5702C48.6012 18.5446 48.6231 18.53 48.6597 18.519C48.6926 18.5117 48.7255 18.519 48.7584 18.5446C48.784 18.5629 48.7986 18.5848 48.8096 18.6177C48.8169 18.6506 48.8096 18.6835 48.784 18.7164C48.6268 18.9175 48.455 19.1076 48.2722 19.2904C47.2815 20.3908 46.0349 21.0488 44.536 21.2645C42.8508 21.323 41.531 20.7344 40.5659 19.4951C39.6045 18.2595 39.089 16.8886 39.0232 15.3898C38.9721 14.1907 39.217 13.0647 39.7617 12.0192C40.3027 10.9737 41.1509 10.2133 42.2987 9.73803C43.2419 9.38709 44.2034 9.28473 45.1868 9.42364C46.5686 9.7161 47.5739 10.4875 48.1991 11.7377C48.6853 12.8088 48.9192 13.9202 48.8936 15.0863V15.1229ZM41.5786 15.1229H46.3054C46.3127 15.1046 46.3164 15.0936 46.3164 15.0863C46.7075 14.0518 46.9159 13.0464 46.9415 12.0594C46.9013 10.8603 46.2944 10.0744 45.1283 9.70879C43.7537 9.44192 42.7338 9.92082 42.0648 11.1455C41.6736 12.2203 41.5091 13.3389 41.5786 14.4978V15.1229Z" fill="#2E2E2E"/>
        <path d="M67.5345 9.93543C67.5857 9.79286 67.5711 9.66125 67.498 9.54061C67.4212 9.41998 67.3079 9.35417 67.147 9.34686H68.6605C68.251 9.37976 67.9659 9.58448 67.7977 9.96102L63.495 21.1365L63.5571 21.301H60.8958L58.3697 14.7134L55.8948 21.1365L55.9569 21.301H53.2956L48.9562 9.99758C48.7881 9.61373 48.4993 9.39804 48.0825 9.34686H51.9832C51.8333 9.36514 51.7199 9.43094 51.6395 9.55158C51.5591 9.67222 51.5445 9.80748 51.5957 9.95737L55.7705 20.7856L58.2344 14.3735L56.5601 9.99758C56.3919 9.61373 56.1031 9.39804 55.6864 9.34686H59.5761C59.4591 9.36514 59.364 9.41632 59.2873 9.50406C59.2105 9.59179 59.1739 9.6905 59.1739 9.79651C59.1739 9.90253 59.1812 9.89887 59.1995 9.95737L59.5761 10.9078L59.7004 11.2588L63.3743 20.7856L67.5345 9.93543Z" fill="#2E2E2E"/>
        <path d="M77.6863 15.1229V15.3715H70.3712C70.3456 16.3475 70.565 17.3163 71.0256 18.2778C71.4899 19.2392 72.2539 19.8461 73.3214 20.0983C74.6374 20.2665 75.8219 19.9411 76.8711 19.1222C77.0538 18.9541 77.222 18.7713 77.3719 18.5702C77.3865 18.5446 77.4121 18.53 77.4487 18.519C77.4816 18.5117 77.5145 18.519 77.5474 18.5446C77.573 18.5629 77.5876 18.5848 77.5985 18.6177C77.6059 18.6506 77.5985 18.6835 77.573 18.7164C77.4158 18.9175 77.2439 19.1076 77.0612 19.2904C76.0705 20.3908 74.8239 21.0488 73.325 21.2645C71.6397 21.323 70.32 20.7344 69.3549 19.4951C68.3935 18.2595 67.878 16.8886 67.8122 15.3898C67.761 14.1907 68.0096 13.0647 68.5507 12.0192C69.0917 10.9737 69.9398 10.2133 71.0877 9.73803C72.0309 9.38709 72.9924 9.28473 73.9757 9.42364C75.3576 9.7161 76.3629 10.4875 76.988 11.7377C77.4779 12.8088 77.7119 13.9202 77.6863 15.0863V15.1229ZM70.3712 15.1229H75.098C75.1054 15.1046 75.109 15.0936 75.109 15.0863C75.5002 14.0518 75.7085 13.0464 75.7341 12.0594C75.6939 10.8603 75.0871 10.0744 73.9209 9.70879C72.5464 9.44192 71.5228 9.92082 70.8574 11.1455C70.4663 12.2203 70.3018 13.3389 70.3712 14.4978V15.1229Z" fill="#2E2E2E"/>
        <path d="M81.2245 20.6393C81.2245 20.8148 81.283 20.9647 81.4 21.089C81.517 21.2132 81.6632 21.2864 81.8387 21.301H78.1245C78.3 21.2827 78.4462 21.2169 78.5632 21.0963C78.6802 20.9756 78.7387 20.8294 78.7387 20.6539V5.29631C78.7387 5.12083 78.6802 4.97095 78.5632 4.83934C78.4462 4.71139 78.3 4.63462 78.1245 4.60903L81.2245 3.36975V20.6393Z" fill="#2E2E2E"/>
        <path d="M34.8157 4.72221L33.9018 4.02763C33.8982 4.02397 33.8945 4.02397 33.8909 4.02032C33.8872 4.01666 33.8799 4.01666 33.8762 4.01666H31.4781C31.4708 4.01666 31.4671 4.01666 31.4635 4.02032C31.4598 4.02397 31.4562 4.02397 31.4525 4.02763L30.5386 4.72221H34.8157Z" fill="#2E2E2E"/>
        <path d="M30.5674 4.86504L31.9456 6.08605L32.6438 6.70386H32.6475C32.6511 6.70752 32.6548 6.70752 32.6584 6.71118C32.6621 6.71118 32.6694 6.71483 32.6731 6.71483C32.6767 6.71483 32.684 6.71483 32.6877 6.71118C32.6913 6.71118 32.695 6.70752 32.6987 6.70386H32.7023L34.7788 4.86139H30.5674V4.86504Z" fill="#2E2E2E"/>
        <path d="M88.2664 16.6983C88.1494 16.6983 88.1165 16.6544 88.1165 16.5667C88.1165 16.4424 88.175 16.3437 88.2664 16.3437C88.3578 16.3437 88.5991 16.4095 88.9098 16.4095C89.4508 16.4095 89.8493 15.9818 89.8493 15.2835C89.8493 14.5853 89.7835 14.5414 89.2498 14.5414C88.716 14.5414 88.5991 14.8668 88.4272 15.2981C88.3687 15.4298 88.3103 15.47 88.2189 15.47C88.1275 15.47 87.9374 15.3676 87.9374 15.1739C87.9374 14.7973 88.5004 14.3513 89.2717 14.3513C90.0431 14.3513 90.5512 14.5743 90.5512 15.3603C90.5512 16.1463 89.8493 16.4424 89.3302 16.4862V16.5155C89.7835 16.5959 90.3136 16.914 90.3136 17.5537C90.3136 18.6139 89.4472 19.0416 88.5515 19.0416C87.6559 19.0416 87.166 18.5298 87.166 18.1971C87.166 17.8645 87.3451 17.8206 87.506 17.8206C87.6668 17.8206 87.6632 17.8937 87.6998 18.029C87.8496 18.5773 88.0909 18.8515 88.5808 18.8515C89.0706 18.8515 89.5751 18.4823 89.5751 17.466C89.5751 16.4497 89.4326 16.6361 88.8476 16.6361C88.5698 16.6361 88.3614 16.6983 88.2664 16.6983Z" fill="#2E2E2E"/>
        <path d="M94.7189 13.8031L94.7482 13.825L93.6368 18.3435C93.5783 18.5592 93.571 18.72 93.6953 18.72C93.8818 18.72 94.1852 18.2375 94.3095 17.9925L94.4521 18.0949C94.2218 18.614 93.8525 19.0563 93.3627 19.0563C92.8728 19.0563 92.9569 18.7602 93.0812 18.2777C93.147 18.0401 93.2128 17.7951 93.2676 17.5539H93.253C92.8545 18.2813 92.2769 19.0563 91.4909 19.0563C90.705 19.0563 90.8329 18.603 90.8329 18.1534C90.8329 17.3309 91.5312 15.5542 93.1287 15.5542C94.7262 15.5542 93.5747 15.6273 93.6917 15.7004L94.0024 14.3953C94.0536 14.1796 94.0463 14.1358 93.8781 14.1211L93.4796 14.0773L93.5089 13.953L94.7189 13.8031ZM91.8236 18.7127C92.4597 18.7127 93.2603 17.3491 93.476 16.5193C93.5199 16.3255 93.6003 16.0733 93.6405 15.8978C93.5601 15.8466 93.3809 15.7735 93.1726 15.7735C92.0905 15.7735 91.4909 17.4771 91.4909 18.2119C91.4909 18.5519 91.6079 18.7091 91.8236 18.7127Z" fill="#2E2E2E"/>
        <path d="M94.2838 21.301H87.6304C84.9434 21.301 82.7573 19.1149 82.7573 16.428C82.7573 13.741 84.9434 11.5549 87.6304 11.5549H94.2838C96.9707 11.5549 99.1568 13.741 99.1568 16.428C99.1568 19.1149 96.9707 21.301 94.2838 21.301ZM87.6304 11.851C85.1043 11.851 83.0498 13.9056 83.0498 16.4316C83.0498 18.9577 85.1043 21.0122 87.6304 21.0122H94.2838C96.8099 21.0122 98.8644 18.9577 98.8644 16.4316C98.8644 13.9056 96.8099 11.851 94.2838 11.851H87.6304Z" fill="#2E2E2E"/>
    </svg>
)
export default SvgComponent

