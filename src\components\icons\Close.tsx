import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={10}
    height={10}
    fill="none"
    {...props}
  >
    <mask
      id="a"
      width={10}
      height={11}
      x={0}
      y={-1}
      maskUnits="userSpaceOnUse"
      style={{
        maskType: "alpha",
      }}
    >
      <path fill="#D9D9D9" d="M0-.01h10v10H0z" />
    </mask>
    <g mask="url(#a)">
      <path
        fill="currentColor"
        d="M1.633 9 1 8.367 4.367 5 1 1.633 1.633 1 5 4.367 8.367 1 9 1.633 5.633 5 9 8.367 8.367 9 5 5.633 1.633 9Z"
      />
    </g>
  </svg>
)
export default SvgComponent
