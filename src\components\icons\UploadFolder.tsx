import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <mask id="mask0_167_369" style={{maskType: "alpha",}} maskUnits="userSpaceOnUse" x="0" y="0" width="20"
              height="20">
            <rect width="20" height="20" fill="#D9D9D9"/>
        </mask>
        <g mask="url(#mask0_167_369)">
            <path
                d="M9.25 15H10.75V11.875L11.9375 13.0625L13 12L10 9L7 12L8.0625 13.0625L9.25 11.875V15ZM5.49417 18C5.08139 18 4.72917 17.8531 4.4375 17.5594C4.14583 17.2656 4 16.9125 4 16.5V3.5C4 3.0875 4.14687 2.73438 4.44062 2.44063C4.73437 2.14688 5.0875 2 5.5 2H12L16 6V16.5C16 16.9125 15.8531 17.2656 15.5592 17.5594C15.2653 17.8531 14.9119 18 14.4992 18H5.49417ZM11 7V3.5H5.5V16.5H14.5V7H11Z"
                fill="#373737"/>
        </g>
    </svg>
)
export default SvgComponent

