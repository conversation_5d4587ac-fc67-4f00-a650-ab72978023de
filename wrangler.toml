#:schema node_modules/wrangler/config-schema.json
name = "ijewel-drive-frontend"
compatibility_date = "2024-10-11"
logpush = true
account_id = "3c4cf55a12810e878be2349f1411ca08"

#main = "worker/index.ts" # not used right now, uncomment if required.
#assets = { directory = "./dist", binding = "ASSETS", html_handling = "force-trailing-slash", not_found_handling = "single-page-application" }
#compatibility_flags = ["nodejs_compat"]

assets = { directory = "./dist", html_handling = "force-trailing-slash", not_found_handling = "single-page-application" }

#routes = [
#    { pattern = "drive.ijewel3d.com", custom_domain = true }
#]

# Workers Logs
# Docs: https://developers.cloudflare.com/workers/observability/logs/workers-logs/
# Configuration: https://developers.cloudflare.com/workers/observability/logs/workers-logs/#enable-workers-logs
[observability]
enabled = true

[env.dev]
routes = [
    { pattern = "dev.drive.ijewel3d.com", custom_domain = true }
]
