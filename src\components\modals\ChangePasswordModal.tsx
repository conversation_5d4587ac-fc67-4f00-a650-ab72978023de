import { FC, useState } from "react";
import { Modal } from "../Modal";
import { useModal } from "../../provider/useModal";
import { Button } from "../Button";
import Close from "../icons/Close";
import { Input } from "@nextui-org/react";

interface ChangePasswordModalProps {
  onClose: () => void;
}

const ChangePasswordModal: FC<ChangePasswordModalProps> = (props) => {
  const { modalState } = useModal();
  const [oldPassword, setOldPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  const handleSave = () => {
    // TODO: Implement password change logic
    console.log("Changing password:", { oldPassword, newPassword, confirmPassword });
    props.onClose();
  };

  return (
    <Modal isOpen={modalState.modalType === "CHANGE_PASSWORD"} onClose={props.onClose} size="md" backdrop="blur" hideCloseButton>
      {() => (
        <div className="w-full bg-white p-6 flex flex-col gap-8 rounded-xl">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Change password</h2>
            <Button
              varient="light"
              size="sm"
              onClick={props.onClose}
              className="w-8 h-8 min-w-8"
              name={<Close />}
              fullWidth={false}
            />
          </div>

          <div className="space-y-5">
            <Input
              type="password"
              placeholder="Old password"
              value={oldPassword}
              onChange={(e) => setOldPassword(e.target.value)}
              variant="bordered"
              classNames={{
                input: "h-12",
                inputWrapper: "h-12 rounded-full bg-transparent"
              }}
            />
            
            <Input
              type="password"
              placeholder="New password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              variant="bordered"
              classNames={{
                input: "h-12",
                inputWrapper: "h-12 rounded-full bg-transparent"
              }}
            />
            
            <Input
              type="password"
              placeholder="Confirm password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              variant="bordered"
              classNames={{
                input: "h-12",
                inputWrapper: "h-12 rounded-full bg-transparent"
              }}
            />

            <div>
              <Button
                varient="light"
                size="sm"
                name="Forgot password?"
                className="text-[#6366f1] p-0"
                fullWidth={false}
              />
            </div>
          </div>

          <div className="flex justify-end">
            <Button
              size="sm"
              name="Save"
              onClick={handleSave}
              className="px-8 py-1.5 rounded-full h-9 bg-[#6366f1] hover:bg-[#5558e6] text-white"
              fullWidth={false}
            />
          </div>
        </div>
      )}
    </Modal>
  );
};

export default ChangePasswordModal; 