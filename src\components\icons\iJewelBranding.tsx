import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg width="102" height="36" viewBox="0 0 102 36" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g filter="url(#filter0_d_1889_3865)">
            <rect x="4" y="3.5" width="94" height="27" rx="13.5" fill="white"/>
            <path fillRule="evenodd" clipRule="evenodd" d="M27.5942 12.25L30.7608 15.4167L23.5 22.6775L16.2392 15.4167L19.4058 12.25H27.5942ZM18.75 10.6667H28.25L33 15.4167L23.5 24.9167L14 15.4167L18.75 10.6667Z" fill="#343536"/>
            <path d="M46.2089 14.3586C46.2089 14.2402 46.1723 14.136 46.0991 14.0461C46.0259 13.9561 45.9321 13.8993 45.8177 13.8708L47.7575 13.0445V22.388C47.7575 23.0084 47.6042 23.5624 47.2954 24.0478C46.9889 24.5332 46.5245 24.7842 45.9023 24.8008C45.2024 24.7582 44.7037 24.3912 44.3995 23.6998C44.2805 23.4133 44.2188 23.1196 44.2188 22.8166H44.3743C44.3743 23.1031 44.4292 23.373 44.539 23.6264C44.6579 23.8466 44.8455 23.991 45.0971 24.0549C45.3511 24.1189 45.5775 24.0952 45.7811 23.9815C45.9229 23.8892 46.0213 23.7353 46.0831 23.5198C46.1425 23.3043 46.1791 23.0889 46.1883 22.871C46.1997 22.6555 46.2043 22.5016 46.2043 22.4093L46.2089 14.3586Z" fill="#343536"/>
            <path d="M43.6412 14.5267C43.6412 14.4083 43.6046 14.3042 43.5314 14.2142C43.4582 14.1242 43.3644 14.0674 43.25 14.039L45.1898 13.2126V19.7597L43.6412 20.5861V14.5267Z" fill="#343536"/>
            <path d="M54.4851 16.8117V16.9727H49.9078C49.8918 17.6049 50.029 18.2324 50.3195 18.8551C50.6078 19.4779 51.0881 19.8709 51.7561 20.0343C52.5796 20.1432 53.3207 19.9325 53.9772 19.4021C54.0916 19.2932 54.1968 19.1748 54.2906 19.0446C54.3021 19.028 54.3158 19.0185 54.3387 19.0114C54.3593 19.0067 54.3798 19.0114 54.4004 19.028C54.4164 19.0398 54.4256 19.054 54.4325 19.0753C54.437 19.0967 54.4325 19.118 54.4164 19.1393C54.3181 19.2695 54.2106 19.3926 54.0962 19.511C53.4763 20.2238 52.6963 20.65 51.7584 20.7897C50.7038 20.8276 49.8781 20.4463 49.2742 19.6436C48.6725 18.8433 48.35 17.9554 48.3088 16.9845C48.2768 16.2079 48.4301 15.4786 48.7709 14.8014C49.1095 14.1242 49.6402 13.6317 50.3584 13.3239C50.9486 13.0965 51.5502 13.0302 52.1656 13.1202C53.0302 13.3097 53.6593 13.8093 54.0504 14.6191C54.3547 15.3128 54.5011 16.0327 54.4851 16.788V16.8117ZM49.9078 16.8117H52.8655C52.8701 16.7999 52.8724 16.7927 52.8724 16.788C53.1172 16.1179 53.2475 15.4668 53.2636 14.8274C53.2384 14.0508 52.8587 13.5417 52.129 13.3049C51.2689 13.1321 50.6306 13.4423 50.212 14.2355C49.9673 14.9316 49.8643 15.6562 49.9078 16.4068V16.8117Z" fill="#343536"/>
            <path d="M66.1503 13.4518C66.1823 13.3594 66.1731 13.2742 66.1274 13.1961C66.0794 13.1179 66.0084 13.0753 65.9078 13.0706H66.8548C66.5986 13.0919 66.4202 13.2245 66.315 13.4684L63.6226 20.7069L63.6615 20.8134H61.9962L60.4155 16.5466L58.8669 20.7069L58.9058 20.8134H57.2405L54.5252 13.492C54.42 13.2434 54.2393 13.1037 53.9785 13.0706H56.4193C56.3255 13.0824 56.2546 13.125 56.2042 13.2032C56.1539 13.2813 56.1448 13.3689 56.1768 13.466L58.7891 20.4795L60.3309 16.3263L59.2832 13.492C59.178 13.2434 58.9973 13.1037 58.7365 13.0706H61.1704C61.0972 13.0824 61.0377 13.1155 60.9897 13.1724C60.9416 13.2292 60.9188 13.2931 60.9188 13.3618C60.9188 13.4305 60.9233 13.4281 60.9348 13.466L61.1704 14.0816L61.2482 14.3089L63.5471 20.4795L66.1503 13.4518Z" fill="#343536"/>
            <path d="M72.501 16.8117V16.9727H67.9237C67.9077 17.6049 68.0449 18.2324 68.3332 18.8551C68.6237 19.4779 69.1018 19.8709 69.7697 20.0343C70.5932 20.1432 71.3344 19.9325 71.9909 19.4021C72.1052 19.2932 72.2105 19.1748 72.3043 19.0446C72.3134 19.028 72.3294 19.0185 72.3523 19.0114C72.3729 19.0067 72.3935 19.0114 72.4141 19.028C72.4301 19.0398 72.4392 19.054 72.4461 19.0753C72.4507 19.0967 72.4461 19.118 72.4301 19.1393C72.3317 19.2695 72.2242 19.3926 72.1098 19.511C71.4899 20.2238 70.7099 20.65 69.772 20.7897C68.7175 20.8276 67.8917 20.4463 67.2878 19.6436C66.6862 18.8433 66.3636 17.9554 66.3225 16.9845C66.2904 16.2079 66.446 15.4786 66.7845 14.8014C67.1231 14.1242 67.6538 13.6317 68.3721 13.3239C68.9622 13.0965 69.5638 13.0302 70.1792 13.1202C71.0439 13.3097 71.6729 13.8093 72.0641 14.6191C72.3706 15.3128 72.517 16.0327 72.501 16.788V16.8117ZM67.9237 16.8117H70.8814C70.886 16.7999 70.8883 16.7927 70.8883 16.788C71.1331 16.1179 71.2635 15.4668 71.2795 14.8274C71.2543 14.0508 70.8746 13.5417 70.1449 13.3049C69.2848 13.1321 68.6443 13.4423 68.2279 14.2355C67.9832 14.9316 67.8802 15.6562 67.9237 16.4068V16.8117Z" fill="#343536"/>
            <path d="M74.7152 20.3848C74.7152 20.4984 74.7518 20.5955 74.825 20.676C74.8982 20.7565 74.9897 20.8039 75.0995 20.8134H72.7754C72.8852 20.8015 72.9767 20.7589 73.0499 20.6808C73.1231 20.6026 73.1597 20.5079 73.1597 20.3943V10.447C73.1597 10.3333 73.1231 10.2362 73.0499 10.151C72.9767 10.0681 72.8852 10.0184 72.7754 10.0018L74.7152 9.1991V20.3848Z" fill="#343536"/>
            <path d="M45.6764 10.0752L45.1045 9.6253C45.1022 9.62293 45.0999 9.62293 45.0976 9.62056C45.0953 9.61819 45.0908 9.61819 45.0885 9.61819H43.5879C43.5833 9.61819 43.581 9.61819 43.5787 9.62056C43.5764 9.62293 43.5742 9.62293 43.5719 9.6253L43 10.0752H45.6764Z" fill="#343536"/>
            <path d="M43.0176 10.1676L43.88 10.9584L44.3169 11.3586H44.3192C44.3214 11.361 44.3237 11.361 44.326 11.3633C44.3283 11.3633 44.3329 11.3657 44.3352 11.3657C44.3375 11.3657 44.342 11.3657 44.3443 11.3633C44.3466 11.3633 44.3489 11.361 44.3512 11.3586H44.3535L45.6528 10.1652H43.0176V10.1676Z" fill="#343536"/>
            <path d="M79.1221 17.8322C79.0489 17.8322 79.0283 17.8038 79.0283 17.747C79.0283 17.6665 79.0649 17.6025 79.1221 17.6025C79.1793 17.6025 79.3303 17.6452 79.5247 17.6452C79.8633 17.6452 80.1126 17.3681 80.1126 16.9159C80.1126 16.4636 80.0714 16.4352 79.7375 16.4352C79.4035 16.4352 79.3303 16.6459 79.2228 16.9253C79.1862 17.0106 79.1496 17.0366 79.0924 17.0366C79.0352 17.0366 78.9163 16.9703 78.9163 16.8448C78.9163 16.6009 79.2685 16.3121 79.7512 16.3121C80.2339 16.3121 80.5518 16.4565 80.5518 16.9656C80.5518 17.4747 80.1126 17.6665 79.7878 17.6949V17.7138C80.0714 17.7659 80.4031 17.9719 80.4031 18.3863C80.4031 19.073 79.861 19.35 79.3006 19.35C78.7401 19.35 78.4336 19.0185 78.4336 18.803C78.4336 18.5876 78.5457 18.5592 78.6463 18.5592C78.747 18.5592 78.7447 18.6065 78.7676 18.6941C78.8614 19.0493 79.0123 19.2269 79.3189 19.2269C79.6254 19.2269 79.9411 18.9877 79.9411 18.3295C79.9411 17.6712 79.8518 17.792 79.4858 17.792C79.312 17.792 79.1816 17.8322 79.1221 17.8322Z" fill="#343536"/>
            <path d="M83.1591 15.9569L83.1774 15.9711L82.482 18.8978C82.4454 19.0375 82.4408 19.1417 82.5186 19.1417C82.6353 19.1417 82.8251 18.8291 82.9029 18.6705L82.9921 18.7368C82.848 19.073 82.617 19.3595 82.3104 19.3595C82.0039 19.3595 82.0565 19.1677 82.1343 18.8551C82.1755 18.7012 82.2166 18.5426 82.251 18.3863H82.2418C81.9925 18.8575 81.631 19.3595 81.1392 19.3595C80.6474 19.3595 80.7275 19.0659 80.7275 18.7746C80.7275 18.2419 81.1644 17.0911 82.164 17.0911C83.1637 17.0911 82.4431 17.1385 82.5163 17.1858L82.7107 16.3405C82.7428 16.2008 82.7382 16.1724 82.633 16.1629L82.3836 16.1345L82.4019 16.054L83.1591 15.9569ZM81.3474 19.1369C81.7454 19.1369 82.2464 18.2537 82.3813 17.7162C82.4088 17.5907 82.4591 17.4273 82.4843 17.3137C82.434 17.2805 82.3219 17.2332 82.1915 17.2332C81.5144 17.2332 81.1392 18.3366 81.1392 18.8125C81.1392 19.0327 81.2124 19.1346 81.3474 19.1369Z" fill="#343536"/>
            <path d="M82.8863 20.8134H78.7231C77.0418 20.8134 75.6738 19.3974 75.6738 17.6571C75.6738 15.9167 77.0418 14.5007 78.7231 14.5007H82.8863C84.5676 14.5007 85.9355 15.9167 85.9355 17.6571C85.9355 19.3974 84.5676 20.8134 82.8863 20.8134ZM78.7231 14.6925C77.1424 14.6925 75.8568 16.0233 75.8568 17.6594C75.8568 19.2956 77.1424 20.6263 78.7231 20.6263H82.8863C84.467 20.6263 85.7525 19.2956 85.7525 17.6594C85.7525 16.0233 84.467 14.6925 82.8863 14.6925H78.7231Z" fill="#343536"/>
        </g>
        <defs>
            <filter id="filter0_d_1889_3865" x="0" y="0.5" width="102" height="35" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                <feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_1889_3865"/>
                <feOffset dy="1"/>
                <feGaussianBlur stdDeviation="1.5"/>
                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1889_3865"/>
                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1889_3865" result="shape"/>
            </filter>
        </defs>
    </svg>
)
export default SvgComponent
