import { CustomFileData } from "../api/api";
import { downloadBlob } from "./downloadBlob";
import { is3dFile, getShareLink } from "../components/shared/util";

export interface CsvExportOptions {
  includeId: boolean;
  includeName: boolean;
  includePath: boolean;
  includeSize: boolean;
  includeTags: boolean;
  includeType: boolean;
  includeModDate: boolean;
  includeCreatedBy: boolean;
  includeDownloadUrl: boolean;
  includeShareUrl: boolean;
  includeCustomDomainShareUrl: boolean;
  includeNotes: boolean;
  only3dFiles: boolean;
  onlyPublicFiles: boolean;
}

export const defaultCsvExportOptions: CsvExportOptions = {
  includeId: true,
  includeName: true,
  includePath: true,
  includeSize: true,
  includeTags: false,
  includeType: true,
  includeModDate: false,
  includeCreatedBy: false,
  includeDownloadUrl: false,
  includeShareUrl: false,
  includeCustomDomainShareUrl: false,
  includeNotes: false,
  only3dFiles: false,
  onlyPublicFiles: false,
};

export function formatFileSize(bytes: number | undefined): string {
  if (!bytes || bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

export function formatDate(date: string | Date | undefined): string {
  if (!date) return "";
  try {
    if (date instanceof Date) {
      return date.toLocaleDateString();
    }
    return new Date(date).toLocaleDateString();
  } catch {
    return typeof date === 'string' ? date : "";
  }
}

export function escapeCSVField(field: string): string {
  if (field.includes('"') || field.includes(',') || field.includes('\n')) {
    return `"${field.replace(/"/g, '""')}"`;
  }
  return field;
}

export function generateCSV(
  files: CustomFileData[],
  options: CsvExportOptions,
  basename?: string,
  userShareDomains?: string[]
): string {
  const headers: string[] = [];

  if (options.includeId) headers.push("ID");
  if (options.includeName) headers.push("Name");
  if (options.includeType) headers.push("Type");
  if (options.includePath) headers.push("Path");
  if (options.includeSize) headers.push("Size");
  if (options.includeTags) headers.push("Tags");
  if (options.includeModDate) headers.push("Modified Date");
  if (options.includeCreatedBy) headers.push("Created By");
  if (options.includeDownloadUrl) headers.push("Download URL");
  if (options.includeShareUrl) headers.push("Share URL");
  if (options.includeCustomDomainShareUrl) headers.push("Custom Domain Share URL");
  if (options.includeNotes) headers.push("Notes");

  const rows: string[] = [headers.join(",")];

  let filteredFiles = files;

  if (options.only3dFiles) {
    filteredFiles = filteredFiles.filter(file => is3dFile(file));
  }

  if (options.onlyPublicFiles) {
    filteredFiles = filteredFiles.filter(file => file.meta?.pub === true);
  }

  filteredFiles.forEach(file => {
    const row: string[] = [];

    if (options.includeId) row.push(escapeCSVField(file.id || ""));
    if (options.includeName) row.push(escapeCSVField(file.name || ""));
    if (options.includeType) row.push(escapeCSVField(file.isDir ? "Folder" : "File"));
    if (options.includePath) row.push(escapeCSVField(file.path || ""));
    if (options.includeSize) {
      const sizeStr = file.isDir ? "0 B" : formatFileSize(file.meta?.size);
      row.push(escapeCSVField(sizeStr));
    }
    if (options.includeTags) row.push(escapeCSVField(file.tags || ""));
    if (options.includeModDate) row.push(escapeCSVField(formatDate(file.modDate)));
    if (options.includeCreatedBy) row.push(escapeCSVField(file.created_by || ""));
    if (options.includeDownloadUrl) row.push(escapeCSVField(file.url || ""));
    if (options.includeShareUrl) {
      const shareUrl = basename ? getShareLink(file, basename) : "";
      row.push(escapeCSVField(shareUrl));
    }
    if (options.includeCustomDomainShareUrl) {
      let customDomainUrl = "";
      if (userShareDomains && userShareDomains.length > 0 && file.id) {
        customDomainUrl = userShareDomains[0].replace("{MODEL_ID}", file.id);
      }
      row.push(escapeCSVField(customDomainUrl));
    }
    if (options.includeNotes) row.push(escapeCSVField(file.notes || ""));

    rows.push(row.join(","));
  });

  return rows.join("\n");
}

export function downloadCSV(csvContent: string, filename: string): void {
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  downloadBlob(blob, filename);
}
