import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g clipPath="url(#clip0_203_1541)">
            <path d="M21.5176 6.37512L13.6562 1.78123C13.048 1.42582 12.2985 1.42582 11.6903 1.78123L3.82886 6.37512C3.22092 6.73038 2.84644 7.38676 2.84644 8.09708V17.0426C2.84644 17.7529 3.22092 18.4093 3.82886 18.7645L11.6903 23.3584C12.2985 23.7138 13.048 23.7138 13.6562 23.3584L21.5176 18.7645C22.1256 18.4093 22.5 17.7529 22.5 17.0426V8.09708C22.5 7.38676 22.1256 6.73038 21.5176 6.37512Z" fill="white" stroke="#6E72F2"/>
            <path d="M5.64121 16.7767C5.30549 16.5829 5.30549 16.2686 5.64121 16.0748L12.1059 12.3428C12.4416 12.1489 12.9859 12.1489 13.3217 12.3428L19.7863 16.0748C20.1221 16.2686 20.1221 16.5829 19.7863 16.7767L13.3217 20.5087C12.9859 20.7026 12.4416 20.7026 12.1059 20.5087L5.64121 16.7767Z" fill="#5236FF"/>
            <path d="M5.64121 13.2835C5.30549 13.0897 5.30549 12.7755 5.64121 12.5816L12.1059 8.8496C12.4416 8.65578 12.9859 8.65578 13.3217 8.8496L19.7863 12.5816C20.1221 12.7755 20.1221 13.0897 19.7863 13.2835L13.3217 17.0156C12.9859 17.2094 12.4416 17.2094 12.1059 17.0156L5.64121 13.2835Z" fill="#A5A8FF"/>
            <path d="M5.64121 9.79066C5.30549 9.59684 5.30549 9.2826 5.64121 9.08879L12.1059 5.35674C12.4416 5.16292 12.9859 5.16292 13.3217 5.35674L19.7863 9.08879C20.1221 9.2826 20.1221 9.59684 19.7863 9.79066L13.3217 13.5227C12.9859 13.7165 12.4416 13.7165 12.1059 13.5227L5.64121 9.79066Z" fill="#DADBFF"/>
        </g>
        <defs>
            <clipPath id="clip0_203_1541">
                <rect x="0.5" y="0.5" width="24" height="24" rx="6" fill="white"/>
            </clipPath>
        </defs>
    </svg>
)
export default SvgComponent