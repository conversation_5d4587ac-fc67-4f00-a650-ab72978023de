import {  motion, useMotionValue, useSpring, useTransform } from "framer-motion"
import { useEffect } from "react"

function CircularProgress({ percent } : { percent : number }) {

  const progress = useMotionValue(percent)
  const circleLength = useSpring(useTransform(progress, [0, 100], [0, 1]))
  const checkmarkPathLength = useSpring(useTransform(progress, [0, 99, 100], [0, 0, 1]))
  const circleColor = useTransform(
    progress,
    [0, 99, 100],
    ["#FFCC66", "#FFCC66", "#66BB66"]
  )


  useEffect (() => {
    progress.set(percent)
  }, [percent, progress])

  return (
    <motion.svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 320 320"
    >
      {/* Check mark  */}
      <motion.path
        transform="translate(75 120)"
        d="M3 50L45 92L134 3"
        fill="transparent"
        stroke="#7BB86F"
        strokeWidth={30}
        style={{ pathLength: checkmarkPathLength }}
      />
      {/* Circle */}
      <motion.path
        d="M 130 6 C 198.483 6 254 61.517 254 130 C 254 198.483 198.483 254 130 254 C 61.517 254 6 198.483 6 130 C 6 61.517 61.517 6 130 6 Z"
        fill="transparent"
        transform="translate(15 35)"

        strokeWidth="35"
        stroke={circleColor}
        style={{
          pathLength: circleLength
        }}
      />
    </motion.svg>
  )
}

export default CircularProgress