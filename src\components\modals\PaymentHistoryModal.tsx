import { FC } from "react";
import { Modal } from "../Modal";
import { useModal } from "../../provider/useModal";
import { Button } from "../Button";
import Close from "../icons/Close";
import Info from "../icons/Info";
import Receipt from "../icons/Receipt";

interface PaymentHistoryModalProps {
  onClose: () => void;
}

interface PaymentRecord {
  date: string;
  description: string;
  amount: string;
  status: "Failed" | "Paid";
}

const paymentHistory: PaymentRecord[] = [
  {
    date: "04/04/24",
    description: "iJewel Premium",
    amount: "USD 600",
    status: "Failed"
  },
  {
    date: "04/03/24",
    description: "iJewel Premium",
    amount: "USD 600",
    status: "Paid"
  },
  {
    date: "04/02/24",
    description: "iJewel Premium",
    amount: "USD 600",
    status: "Paid"
  }
];

const PaymentHistoryModal: FC<PaymentHistoryModalProps> = (props) => {
  const { modalState } = useModal();

  const handleInfoClick = (payment: PaymentRecord) => {
    // Handle showing payment failure details
    console.log("Show failure details for:", payment);
  };

  const handleReceiptClick = (payment: PaymentRecord) => {
    // Handle downloading/viewing receipt
    console.log("Download receipt for:", payment);
  };

  return (
    <Modal isOpen={modalState.modalType === "PAYMENT_HISTORY"} onClose={props.onClose} size="2xl" backdrop="blur" hideCloseButton>
      {() => (
        <div className="w-full bg-white p-6 flex flex-col gap-4 rounded-xl">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Payment history</h2>
            <Button
              varient="light"
              size="sm"
              onClick={props.onClose}
              className="w-8 h-8 min-w-8"
              name={<Close />}
              fullWidth={false}
            />
          </div>

          <div className="w-full">
            <table className="w-full">
              <thead>
                <tr className="text-left">
                  <th className="py-2 text-gray-600 font-normal">Date issued</th>
                  <th className="py-2 text-gray-600 font-normal">Description</th>
                  <th className="py-2 text-gray-600 font-normal">Amount</th>
                  <th className="py-2 text-gray-600 font-normal">Status</th>
                  <th className="py-2"></th>
                </tr>
              </thead>
              <tbody>
                {paymentHistory.map((payment, index) => (
                  <tr key={index}>
                    <td className="py-4">{payment.date}</td>
                    <td className="py-4">{payment.description}</td>
                    <td className="py-4">{payment.amount}</td>
                    <td className="py-4">
                      <span className={`inline-flex items-center gap-1 ${payment.status === "Paid" ? "text-green-600" : "text-red-600"}`}>
                        <span className={`w-1.5 h-1.5 rounded-full ${payment.status === "Paid" ? "bg-green-600" : "bg-red-600"}`}></span>
                        {payment.status}
                      </span>
                    </td>
                    <td className="py-4">
                      <div className="flex gap-2 justify-end">
                        {payment.status === "Failed" ? (
                          <Button
                            varient="light"
                            size="sm"
                            className="w-8 h-8 min-w-8 border border-gray-200 rounded-xl"
                            name={<Info />}
                            onClick={() => handleInfoClick(payment)}
                            fullWidth={false}
                          />
                        ) : (
                          <Button
                            varient="light"
                            size="sm"
                            className="w-8 h-8 min-w-8 border border-gray-200 rounded-xl"
                            name={<Receipt className="w-5 h-5" />}
                            onClick={() => handleReceiptClick(payment)}
                            fullWidth={false}
                          />
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </Modal>
  );
};

export default PaymentHistoryModal; 