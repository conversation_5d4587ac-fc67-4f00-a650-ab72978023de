import { nextui } from "@nextui-org/react";

const config = {
  content: [
    "./index.html",
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./node_modules/@nextui-org/theme/dist/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        normal: ["segoe-regular", "sans-serif"],
        semibold: ["segoe-semibold", "sans-serif"],
        bold: ["segoe-bold", "sans-serif"],
        ltlight: ["leto-light", "sans-serif"],
        ltnormal: ["leto-regular", "sans-serif"],
        ltbold: ["lato-semibold", "sans-serif"],
      },
      screens: {
        sm: "640px",
        // => @media (min-width: 640px) { ... }

        md: "768px",
        // => @media (min-width: 768px) { ... }

        lg: "1024px",
        // => @media (min-width: 1024px) { ... }

        xl: "1280px",
        // => @media (min-width: 1280px) { ... }

        "2xl": "1536px",
        "3xl": "1800px",
        // => @media (min-width: 1536px) { ... }
      },
      colors: {
        bg: "#f0f0f0",
      },
    },
  },
  darkMode: "class",
  plugins: [
    nextui({
      prefix: "ijewel-drive",
      themes: {
        light: {
          extend: "light",
          colors: {
            primary: {
              DEFAULT: "#6E73F2",
              foreground: "#ffffff",
            },
            default: {
              DEFAULT: "#e9e9e9",
              foreground: "#2e2e2e",
            },
            secondary: {
              DEFAULT: "#373737",
              foreground: "#fff",
            },
            danger: {
              DEFAULT: "#ef4444",
              foreground: "#fff",
            },
          },
          layout: {
            radius: {
              small: "10px",
              medium: "12px",
              large: "14px",
            },
            boxShadow: {
              small:
                "0px 0px 5px 0px rgb(0 0 0 / 0.01), 0px 2px 10px 0px rgb(0 0 0 / 0.00)",
              medium:
                " 0px 0px 15px 0px rgb(0 0 0 / 0.02), 0px 2px 30px 0px rgb(0 0 0 / 0.08)",
              large:
                " 0px 0px 30px 0px rgb(0 0 0 / 0.03), 0px 30px 60px 0px rgb(0 0 0 / 0.12)",
            },
            spacingUnit: 2.4,
            fontSize: {
              tiny: "11px",
              small: "14px",
              medium: "16px",
              large: "20px",
            },
            lineHeight: {
              tiny: "0.7rem", // text-tiny
              small: "0.9rem", // text-small
              medium: "1.1rem", // text-medium
              large: "3.75rem", // text-large
            },
            borderWidth: {
              small: "1px",
              medium: "2px",
              large: "3px",
            },
          },
        },
      },
    }),
  ],
};
export default config;
