import { Chip, Input } from "@nextui-org/react";
import { FC, useCallback, useState } from "react";
import toast from "react-hot-toast";
import CloseIcon from "./icons/Close";
import { useUser } from "../provider/UserProvider";
import { Button } from "./Button";
import { validateEmail } from "./shared/util";

interface EmailsProps {
  emails?: string[];
  updateEmails: (emails : string[]) => Promise<void>;
}

export const Emails: FC<EmailsProps> = (props) => {
  const [email, setEmail] = useState<string>("");
  const [emails, setEmails] = useState<string[]>(props.emails || []);
  const [saving, setSaving] = useState<boolean>(false);
  const { api } = useUser();

  const addEmail = useCallback(
    async (e: KeyboardEvent) => {
      if (e.key !== "Enter") return;
      if (email.trim() === "") return;
      if (!validateEmail(email.trim())) {
        toast.error("Invalid email address");
        return;
      }
      if (emails.includes(email.trim())) {
        toast.error("Email already exists");
        return;
      }

      const updatedEmails = [...emails, email.trim()];
      setEmails(updatedEmails);
      setEmail("");

      if (!api) return;
      setSaving(true);
      await props.updateEmails(updatedEmails);
      setSaving(false);
    },
    [email, emails, api, props]
  );

  const deleteEmail = useCallback(
    async (index: number) => {
      const updatedEmails = [...emails];
      updatedEmails.splice(index, 1);
      setEmails(updatedEmails);

      if (!api) return;
      setSaving(true);
      await props.updateEmails(updatedEmails);
      setSaving(false);
    },
    [emails, api, props]
  );

  return (
    <div className="flex flex-col gap-unit-md h-full">
      <div className="flex gap-unit-md items-center">
        <Input
          value={email}
          onKeyDown={addEmail as any}
          onValueChange={(v) => setEmail(v)}
          variant="bordered"
          size="lg"
          classNames={{
            inputWrapper: "h-unit-3xl",
          }}
          type="text"
          placeholder="Email"
        />
      </div>
      {emails.length > 0 && <p className="text-default-400 text-sm">People who have access</p>}
      {emails && emails.length > 0 && (
        <div className="w-full flex flex-wrap gap-unit-md">
          {emails.map((email, i) => (
            <Chip
              key={email + i}
              color="primary"
              variant="faded"
              classNames={{
                base: "bg-[#f7f7f7] rounded-small border-1",
                content: "text-sm font-normal inline-block min-w-20",
              }}
              endContent={
                <CloseIcon
                  onClick={() => deleteEmail(i)}
                  className="mx-unit-xs cursor-pointer"
                />
              }
            >
              {email}
            </Chip>
          ))}
        </div>
      )}
    </div>
  );
};
