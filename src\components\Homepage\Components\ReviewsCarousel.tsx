import React, { useState } from "react";
import { Button } from "../../Button";
import RightArrow from "../../icons/RightArrow";

const ReviewsFromCustomers = () => {
  const reviews = [
    {
      image: "VJ",
      name: "VIK JAIN",
      review:
        "For a few years We have been searching for a software to render my jewelry designs... We recently found iJewel Drive and I must say it's like magic. Perfect render everytime, and all in real time! I am done searching for the perfect jewelry visualization software. Thank you iJewel!",
      ceo: "CEO VLORA",
    },
    {
      image: "IJ",
      name: "IMPERIAL JEWEL",
      review:
        "The Full-Editor is an incredibly flexible tool that gives us complete control over lighting, materials, and scene settings. It has become an essential part of our workflow for perfecting each jewelry design.",
      ceo: "CEO",
    },
    {
      image: "LG",
      name: "LUXEG<PERSON>",
      review:
        "The ability to customize materials and lighting for meetings has made client presentations more engaging. Immediate feedback and updates help expedite approvals, facilitating seamless collaboration.",
      ceo: "CEO",
    },
  ];

  const [currentIndex, setCurrentIndex] = useState(0);

  const handlePrevious = () => {
    setCurrentIndex((currentIndex - 1 + reviews.length) % reviews.length);
  };

  const handleNext = () => {
    setCurrentIndex((currentIndex + 1) % reviews.length);
  };

  const currentReviews = [
    reviews[(currentIndex - 1 + reviews.length) % reviews.length],
    reviews[currentIndex],
    reviews[(currentIndex + 1) % reviews.length],
  ];

  return (
    <div className="bg-[#F0F1FF] rounded-lg p-8">
      <h1 className="text-base font-bold mb-4 text-center text-[#686868]">
        WHAT PEOPLE SAY
      </h1>
      <h2 className="text-[42px] md:text-5xl font-ltbold font-semibold mb-8 text-center">
        Reviews from customers
      </h2>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-[30px]">
        {currentReviews.map((review, index) => (
          <div
            key={index}
            className="first:hidden last:hidden lg:first:flex lg:last:flex bg-[#FFFFFF] flex flex-col p-[40px] rounded-[30px] gap-[30px] relative"
          >
            <div className=" self-start flex items-center gap-5">
              <div className="text-4xl font-light border-1 rounded-full min-w-[80px] min-h-[80px] flex items-center justify-center">
                {review.image}
              </div>
              <h3 className="text-xl font-bold">{review.name}</h3>
            </div>
            <p className="text-gray-600 font-san text-xl">{review.review}</p>
            <p className="text-gray-600 font-medium text-xl">{review.ceo}</p>
          </div>
        ))}
      </div>

      <div className="flex justify-center gap-2.5 mt-8">
        <Button
          className="border-2 border-[#373737] rounded-full px-4 py-6 rotate-180"
          onClick={handlePrevious}
          fullWidth={false}
          varient="ghost"
          name={<RightArrow />}
        />

        <Button
          className="border-2 border-[#373737] rounded-full px-4 py-6"
          onClick={handleNext}
          varient="ghost"
          fullWidth={false}
          name={<RightArrow />}
        />
      </div>
    </div>
  );
};

export default ReviewsFromCustomers;
