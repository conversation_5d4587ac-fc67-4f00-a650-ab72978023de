@tailwind base;
@tailwind components;
@tailwind utilities;

/* @font-face {
  font-family: "Inter-regular";
  src: url("./assets/Inter-Regular.woff2");
}
@font-face {
  font-family: "Inter-semibold";
  src: url("./assets/Inter-SemiBold.woff2");
} */
@font-face {
  font-family: "segoe-regular";
  src: url("./assets/fonts/segoe/normal.woff2");
}
@font-face {
  font-family: "segoe-semibold";
  src: url("./assets/fonts/segoe/semibold.woff2");
}
@font-face {
  font-family: "segoe-bold";
  src: url("./assets/fonts/segoe/bold.woff2");
}

@font-face {
  font-family: "leto-light";
  src: url("./assets/Lato-Light.ttf");
}
@font-face {
  font-family: "leto-regular";
  src: url("./assets/Lato-Regular.ttf");
}
@font-face {
  font-family: "leto-semibold";
  src: url("./assets/Lato-Bold.ttf");
}

::-webkit-scrollbar {
  width: 5px;
}

::-webkit-scrollbar-track-piece {
  background-color: #fff;
}

::-webkit-scrollbar-thumb {
  background-color: #cbcbcb;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #909090;
}

.overlay-button {
  width: 2.5rem; /* w-10 */
  height: 2.5rem; /* h-10 */
  padding: 1rem; /* p-unit-lg */
  border-radius: 9999px; /* rounded-full */
}

.chonky-toolbarWrapper {
  /* height: 3rem; */
  margin-bottom: 1rem;
  display: flex;
  width: 100%; /* w-full */
  flex-shrink: 0; /* shrink-0 */
  align-items: center; /* items-center */
}

.chonky-toolbarContainer {
  display: flex;
  width: 100%; /* w-full */
  flex-shrink: 0; /* shrink-0 */
  align-items: center; /* items-center */
  height: 100%; /* h-full */
}

.chonky-toolbarRight {
  height: 100%; /* h-full */
  align-items: center; /* items-center */
  padding-bottom: 0 !important;
}

.chonky-toolbarLeft {
  height: 100%; /* h-full */
  align-items: center; /* items-center */
  padding-bottom: 0 !important;
}

.MuiInputBase-root.MuiOutlinedInput-root.chonky-searchFieldInput.MuiInputBase-formControl.MuiInputBase-adornedStart.MuiOutlinedInput-adornedStart.MuiInputBase-marginDense.MuiOutlinedInput-marginDense {
  display: none;
}

.MuiBreadcrumbs-li > div > button {
  font-family: "segoe-semibold" !important;
  font-weight: unset !important;
  background-color: red;
}

/* for customizing the file name */
/* [data-test-id="file-entry"] > div > span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
[data-test-id="file-entry"] > div > span > span {
  color: red;
}
[data-test-id="file-entry"] > div {
  overflow: hidden;
} */
