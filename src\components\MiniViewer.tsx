import { useEffect, useRef, FC, useState, useCallback } from "react";
import { useBrowser } from "../provider/BrowserProvider";
import { useUser } from "../provider/UserProvider";
import { has3dConfig, loadScript } from "./shared/util";
import { currentAssetsVersion, FILE_CONFIG_3D_PROPERTIES } from "./shared/variables";

interface ViewerProps {
  file: any;
  profile?: any;

  setLoading?: any;
  setError?: any;
  headerComponent?: React.ReactNode;

  showCard?: boolean;
  showUiButtons?: boolean;
  showConfigurator?: boolean;
  showShareButton?: boolean;
  showZoomButtons?: boolean;
  hideConfigurator?: boolean;
  hideQuality?: boolean;
  hideCameraViews?: boolean;
  hideGltfAnimations?: boolean;
  hideRotateCamera?: boolean;
  hideFullScreen?: boolean;
  hideFitScene?: boolean;
  showPosterInLoadingScreen?: boolean;
  disableInteractionPrompt?: boolean;
  runRotateCamera?: boolean;
  embedLogo?: any;
  logoPositionX?: number | undefined;
  logoPositionY?: number | undefined;
  isBusiness?: boolean;
  shareUrl?: string;
  transparentBg?: boolean;
  isModal?: boolean;
  offsetConfigurator?: boolean;
}

const MiniViewer: FC<ViewerProps> = (props: ViewerProps) => {
  const { viewer, setViewer , addWebgi } = useBrowser();
  const canvasContainer = useRef<HTMLDivElement>(null);
  const [viewerInstance, setViewerInstance] = useState<any | null>(null);
  const { config , api, user } = useUser();

  // const loadDefaultConfig = async (basePath: string) => {
  //   const response = await fetch(basePath + "default.json");
  //   const data = await response.json();
  //   return data;
  // };
 
  const saveViewer = useCallback(
    (event: any) => {
      if (!viewer) {
        setViewer(event.detail.viewer);
        console.log("viewer ready", event.detail.viewer);
      }
    },
    [setViewer, viewer]
  );

  const loadViewerInstance =useCallback(async (container : HTMLDivElement, projectData : any) => {
    let ViewerClass;

    if(config && !config["viewer-path"]) config["viewer-path"] = "https://releases.ijewel3d.com/libs/mini-viewer/0.3.24/bundle.nowebgi.iife.js";
    // if(config) config["viewer-path"] = "../../../libs/bundle.nowebgi.iife.js";

    //custom viewer
    if (config?.["viewer-path"]) {
      
      await addWebgi();
      await loadScript(config["viewer-path"]);
      ViewerClass = (window as any)["ijewelViewer"]?.Viewer;

      if (!ViewerClass) {
        throw new Error("ijewelViewer is not available on the window object.");
      }
    } else {
        throw new Error("No config");
      // const module = await import("ijewelViewer");
      // ViewerClass = module.Viewer;
    }

    if (projectData.brandingSettings && user?.meta?.plan && user?.meta?.plan == "free") {
      projectData.brandingSettings.enable = false;
      projectData.brandingSettings.showLoadingScreenLogo = true;
    }

    // const isModal = !pathname.includes("/view");
    const viewerInstance = new ViewerClass(container, projectData, {
      showCard: props.showCard ?? false,
      showSwitchNode: true,
      viewer: viewer,
      headerComponent: props.headerComponent ?? null,
      showUiButtons: props.showUiButtons ?? true,
      showShareButton: props.showShareButton ?? false,
      showZoomButtons: props.showZoomButtons ?? false,
      showConfigurator: props.showConfigurator ?? true,
      hideQuality: props.hideQuality ?? false,
      hideCameraViews: props.hideCameraViews ?? false,
      hideGltfAnimations: props.hideGltfAnimations ?? false,
      hideRotateCamera: props.hideRotateCamera ?? false,
      hideFullScreen: props.hideFullScreen ?? false,
      hideFitScene: props.hideFitScene ?? false,
      disableInteractionPrompt: props.disableInteractionPrompt ?? false,
      runRotateCamera: props.runRotateCamera ?? false,
      showPosterInLoadingScreen: props.showPosterInLoadingScreen ?? false,
      embedLogo: props.embedLogo,
      shareUrl: props.shareUrl,
      transparentBg: props.transparentBg,
      showProfile: true,
      useIjewelLogo: false,
      configuratorBottomOffsetPx : (props.offsetConfigurator && window.innerWidth < 768) ? 20 : 0,
      brandingSettings: projectData.brandingSettings ?? null,
    });

    return viewerInstance;
  }, [addWebgi, config, props, viewer]);

  const ready = useRef(true);
  const initViewer = useCallback(
    async (basePath: string, modelUrl: string) => {
      let projectData: any = {};
      if (!ready.current) return;
      ready.current = false;
      projectData = props.file.config;
      if(!projectData) projectData = {};
      
      if (!has3dConfig(props.file) && props.file.defaultConfig) {
        FILE_CONFIG_3D_PROPERTIES.forEach((key) => {
          if (props.file.defaultConfig[key]) projectData[key] = props.file.defaultConfig[key];
        })
      }
      projectData.basePath = basePath;
      projectData.modelUrl = modelUrl;
      projectData.name = projectData.name ?? props.file.name;
      projectData.version = projectData.version ?? currentAssetsVersion;

      const container = canvasContainer.current;
      if (!container) return;

      if (!viewerInstance) {
        const viewerInstance = await loadViewerInstance(container, projectData);
        setViewerInstance(viewerInstance);
        props.setLoading?.(false);
        window.addEventListener("ijewel-viewer-ready", saveViewer);
      } else {
        viewerInstance.viewerOptions.viewer = viewer;
        viewerInstance.render(projectData);
      }
      ready.current = true;
    },
    [props, viewerInstance, loadViewerInstance, saveViewer, viewer]
  );

  useEffect(() => {
    if (!api) return;
    
    const url = api.getDownloadUrl(props.file);
    const basePath = api?.getBasePath();

    initViewer(basePath, url);
    
  }, [props.file]);

  return (
    <div className={"w-full h-full"}>
      <div ref={canvasContainer} id="webgi-canvas-container" className="w-full h-full flex "></div>
    </div>
  );
};

export default MiniViewer;
