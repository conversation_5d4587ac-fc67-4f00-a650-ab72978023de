import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <rect x="0.5" y="0.5" width="24" height="24" rx="7" fill="white"/>
        <g filter="url(#filter0_d_203_1552)">
            <path d="M19.9452 18.5611C20.0914 18.7619 20.2764 18.8749 20.5 18.9H18.2226C18.2871 18.89 18.3302 18.8523 18.3517 18.787C18.3775 18.7218 18.371 18.6565 18.3323 18.5912L16.4742 15.8724L14.042 18.6289C13.999 18.6841 13.9861 18.7418 14.0033 18.8021C14.0248 18.8573 14.0656 18.89 14.1259 18.9H13.3C13.5237 18.8799 13.7194 18.7795 13.8871 18.5987L16.3904 15.7519L13.9194 12.1368C13.7603 11.9059 13.556 11.7603 13.3065 11.7H15.5839C15.5194 11.7251 15.4785 11.7728 15.4613 11.8431C15.4441 11.9134 15.4549 11.9787 15.4936 12.0389L17.313 14.705L19.7259 11.9711C19.7646 11.9159 19.7732 11.8607 19.7517 11.8054C19.7345 11.7452 19.6979 11.71 19.642 11.7H20.4613C20.242 11.7151 20.0506 11.813 19.8871 11.9937L17.3968 14.818L19.9452 18.5611Z" fill="black"/>
            <path d="M10.0406 11.8891C11.2156 12.1891 12.1125 12.8484 12.7312 13.8672C13.3063 15.0422 13.3063 16.2172 12.7312 17.3922C12.1125 18.4172 11.2156 19.0797 10.0406 19.3797C9.85313 19.4234 9.63125 19.4578 9.375 19.4828C9.11875 19.5016 8.8875 19.5172 8.68125 19.5297C8.475 19.5359 8.35625 19.5391 8.325 19.5391C8.30625 19.5391 8.225 19.5391 8.08125 19.5391C7.9375 19.5391 7.75938 19.5391 7.54688 19.5391C7.34063 19.5391 7.15625 19.5391 6.99375 19.5391C6.83125 19.5391 6.74063 19.5391 6.72187 19.5391H4.5C4.8125 19.4141 4.96875 19.2078 4.96875 18.9203V6.72344C4.96875 6.43594 4.8125 6.22969 4.5 6.10469H6.81562C6.82188 6.10469 6.9 6.10469 7.05 6.10469C7.20625 6.10469 7.38125 6.10469 7.575 6.10469C7.76875 6.10469 7.9375 6.10469 8.08125 6.10469C8.23125 6.10469 8.30938 6.10469 8.31562 6.10469C8.34063 6.09844 8.43438 6.09844 8.59688 6.10469C8.75938 6.11094 8.94063 6.12031 9.14062 6.13281C9.34063 6.14531 9.5125 6.16406 9.65625 6.18906C10.3625 6.33906 10.9563 6.66094 11.4375 7.15469C11.9188 7.64844 12.1688 8.26719 12.1875 9.01094C12.1688 9.70469 11.925 10.3172 11.4563 10.8484C10.9875 11.3797 10.4062 11.7047 9.7125 11.8234C9.825 11.8359 9.93438 11.8578 10.0406 11.8891ZM6.825 6.27344V11.7297C6.925 11.7297 7.0625 11.7297 7.2375 11.7297C7.4125 11.7297 7.59375 11.7297 7.78125 11.7297C7.96875 11.7297 8.12812 11.7297 8.25938 11.7297C8.39688 11.7234 8.475 11.7203 8.49375 11.7203C8.85625 11.7016 9.18125 11.5859 9.46875 11.3734C9.81875 11.0859 10.05 10.7297 10.1625 10.3047C10.2812 9.87969 10.3344 9.44844 10.3219 9.01094C10.3344 8.54844 10.2688 8.08594 10.125 7.62344C9.9875 7.16094 9.70937 6.80781 9.29063 6.56406C9.14687 6.48906 8.9875 6.42344 8.8125 6.36719C8.64375 6.30469 8.48125 6.27344 8.325 6.27344C8.31875 6.27344 8.25312 6.27344 8.12812 6.27344C8.00312 6.27344 7.85313 6.27344 7.67813 6.27344C7.48438 6.27344 7.30313 6.27344 7.13438 6.27344C6.96563 6.27344 6.8625 6.27344 6.825 6.27344ZM11.1937 16.7734C11.35 16.0109 11.35 15.2516 11.1937 14.4953C11.025 13.5953 10.575 12.8766 9.84375 12.3391C9.4375 12.0766 8.9875 11.9328 8.49375 11.9078C8.475 11.9078 8.39688 11.9078 8.25938 11.9078C8.12812 11.9078 7.96563 11.9078 7.77188 11.9078C7.58438 11.9078 7.40625 11.9078 7.2375 11.9078C7.06875 11.9078 6.93125 11.9078 6.825 11.9078V19.3609C6.9125 19.3609 7.03125 19.3609 7.18125 19.3609C7.33125 19.3609 7.4875 19.3609 7.65 19.3609C7.8375 19.3609 7.99688 19.3609 8.12812 19.3609C8.25938 19.3609 8.32812 19.3609 8.33438 19.3609C8.36563 19.3609 8.475 19.3422 8.6625 19.3047C8.85625 19.2672 9.06563 19.2172 9.29063 19.1547C9.52187 19.0859 9.70625 19.0109 9.84375 18.9297C10.575 18.3859 11.025 17.6672 11.1937 16.7734Z" fill="black"/>
            <path d="M19.167 6.97788L18.2666 6.27207H15.8395L14.9 6.97788H19.167Z" fill="black"/>
            <path d="M14.9 7.09547L16.2702 8.31103L16.9748 8.93842L19.1279 7.09547H14.9Z" fill="black"/>
        </g>
        <defs>
            <filter id="filter0_d_203_1552" x="2.2" y="4.8" width="20.6" height="18.0391" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                <feOffset dy="1"/>
                <feGaussianBlur stdDeviation="1.15"/>
                <feComposite in2="hardAlpha" operator="out"/>
                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"/>
                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_203_1552"/>
                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_203_1552" result="shape"/>
            </filter>
        </defs>
    </svg>
)
export default SvgComponent