#!/usr/bin/env bash
# Usage: ./s3.sh <file_path> <bucket> <method> <prefix>
# Requires rclone binary in path. (apt-get install rclone)
# Requires rclone config named s3conn or the following environment variables:
# RCLONE_CONFIG_S3CONN_TYPE=s3
# RCLONE_CONFIG_S3CONN_PROVIDER=AWS
# RCLONE_CONFIG_S3CONN_ENV_AUTH=false
# RCLONE_CONFIG_S3CONN_REGION=us-east-1
# RCLONE_CONFIG_S3CONN_ENDPOINT=
# RCLONE_CONFIG_S3CONN_ACCESS_KEY_ID=XXXX
# RCLONE_CONFIG_S3CONN_SECRET_ACCESS_KEY=XXXX

# PUT Example: ./s3.sh path/to/file bucket-name PUT prefix/
# Will upload file path/to/file to s3://bucket-name/prefix/path/to/file
# GET Example: ./s3.sh path/to/file bucket-name GET prefix/
# Will download file from s3://bucket-name/prefix/path/to/file to path/to/file
# Note: prefix is optional

# npm run build

file_path="dist"
bucket="ijewel3d-maan-dev"
method="PUT"
prefix="ijewel-drive/jannpaul"
version=$npm_package_version

set -eu pipefail
# about the file
destination_path="${bucket}/${prefix}/${version}"

export $(cat .s3.env | xargs)

# check if rclone is installed
if ! command -v rclone &> /dev/null; then
  echo "rclone not found"
  exit 1
fi

# Fetch the latest version
latest_version_url="https://maan.ijewel3d.com/ijewel-drive/jannpaul/latest-version.txt"
latest_version=$(curl -s $latest_version_url | tr -d '\000' | tr -d '\n')

if [ -z "$latest_version" ]; then
  echo "Unable to retrieve the latest version. Aborting."
  exit 1
fi

echo "Latest version: $latest_version"
echo "Current version: $version"

# Compare versions
# if [ "$(printf '%s\n' "$version" "$latest_version" | sort -V | head -n1)" = "$version" ]; then
#   echo "Current version is not newer than the latest version. Aborting upload."
#   exit 1
# fi

# Proceed with upload if current version is newer
if [ "$method" = "PUT" ]; then
  if [ ! -d "$file_path" ]; then
    echo "File not found: $file_path"
    exit 1
  fi
  echo "Uploading $file_path to $destination_path"
  rclone copyto "$file_path" s3conn:"$destination_path" -v
  echo $version > "$file_path/latest-version.txt"
  rclone copyto "$file_path/latest-version.txt" s3conn:"${bucket}/${prefix}/latest-version.txt" -v --s3-no-check-bucket
elif [ "$method" = "GET" ]; then
  echo "Downloading $file_path from $destination_path"
  rclone copyto s3conn:"$destination_path" "$file_path" -v
else
  echo "Invalid method: $method"
  exit 1
fi