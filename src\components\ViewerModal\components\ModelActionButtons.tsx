import { <PERSON><PERSON> } from "@nextui-org/react";
// import { <PERSON> } from "react-router-dom";
import ShareIos from "../../icons/ShareIos";
// import Comments from "./Comments";
import { handleShare } from "../../shared/util";
import ShareCommon from "../../icons/ShareCommon";
import VectorBottom from "../../icons/VectorBottom";
import { useParams } from "react-router-dom";
// import ArrowOut from "../../icons/ArrowOut";

interface ModelActionButtonsProps {
  // handleLikeToggle: () => void;
  // liked: boolean;
  // likeCount: number;
  // canEdit?: boolean;
  // profile: any;
  project: any;
  // handleSaveView?: () => void;\
  mobileMode?: boolean;
  isSideButtons?: boolean;
  hideViewButton?: boolean;
  handleEdit?: null | (() => void);

  handleShowComments?: () => void;
  commentsLength?: number;
  showComments?: boolean;
}

export const ModelActionButtons: React.FC<ModelActionButtonsProps> = ({
  // handleLikeToggle,
  // liked,
  // likeCount,
  // canEdit,
  // profile,
  project,
  // handleSaveView,
  mobileMode,
  isSideButtons,
  // handleEdit,
  // hideViewButton = false,

  handleShowComments,
  // commentsLength = 0,
  showComments,
}) => {
  // const { username } = profile ?? {};
  // const { slug } = project ?? {};

  const {basename} = useParams()
  const deviceType = "ios";
  const defaultSideButtonProps: any = {
    variant: "solid",
    className: "px-3 h-[30px] bg-white border-none rounded-full",
  };
  const defaultButtonPanelProps: any = {
    variant: "ghost",
    className:
      "px-3.5 h-[30px] text-sm  border-[1px] border-[#CFCFCF] rounded-full gap-2",
  };
  const defautButtonProps: any = isSideButtons
    ? defaultSideButtonProps
    : defaultButtonPanelProps;

  const classes = {
    edit: defautButtonProps.className + (isSideButtons ? " order-first" : ""),
    delete:
      defautButtonProps.className + (isSideButtons ? "" : " border-[1px]"),
    save: defautButtonProps.className + " border-none",
    like: defautButtonProps.className,
    share: defautButtonProps.className + (isSideButtons ? "" : " px-[17px]"),
    view: defautButtonProps.className + (isSideButtons ? "" : " px-[18px]"),
    comment: defautButtonProps.className,
  };
  return (
    <>

      {  <Button
        {...defautButtonProps}
        onClick={() => handleShare(project , basename || "drive" )}
        className={classes.share}
        endContent={
          deviceType === "ios" ? (
            <ShareIos height={16} widths={16} />
          ) : (
            <ShareCommon height={16} widths={16} />
          )
        }
      />}
      {mobileMode && handleShowComments  && (
        <Button
          {...defautButtonProps}
          onClick={handleShowComments}
          variant="ghost"
          className={classes.share}
        >
          <VectorBottom className={!showComments ? "transform rotate-180" : ""} />
        </Button>
      )}
      {/* {!hideViewButton && (
        <Button
          {...defautButtonProps}
          className={classes.view}
          as={Link}
          // href={`/profile/${username}/${slug}`}
          target="blank"
          endContent={<ArrowOut height={16} widths={16} />}
        />
      )} */}
    </>
  );
};

export default ModelActionButtons;
