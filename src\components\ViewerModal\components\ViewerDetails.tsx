/* eslint-disable @typescript-eslint/no-unused-vars */
import { useState, useEffect, useCallback, FC, useRef, useMemo } from "react";
import { CustomFileData } from "../../../api/api";
import ModelActionButtons from "./ModelActionButtons";
import { convertHtmlToPlainText, debounce, getEllipsisText } from "../../shared/util";
import PreviewSidebar from "../../PreviewSidebar";

const calcScrollableHeight = (target: HTMLDivElement) => {
  if (!target) return 0;
  return target.scrollHeight - (target.scrollTop + target.offsetHeight);
};
const hasScroll = (target: HTMLDivElement) => {
  if (!target) return false;

  let isScrollable = true;
  if (target.scrollTop == 0 && target.offsetHeight >= target.scrollHeight)
    isScrollable = false;

  return isScrollable;
};

interface ViewerDetailProps {
  project: CustomFileData | null;
  // profile?: any;
  // isHideModalPanel: boolean;
  canEdit?: boolean;
  handleModelPanelToggle?: () => void;
  // likeCount: number;
  // liked: boolean;
  // handleLikeToggle: () => void;
  // handleSaveView?: () => Promise<void>;
  // handleEdit?: (() => void) | null;
  mobileMode: boolean;
  // hideViewButton?: boolean;
  handleShowComments?: () => void;
  showComments?: boolean;
}
const moreProjectsCount = 4;

const TitleAndMore = (props: {
  title: string;
  description?: string;
  richTxtDescription?: string;
  handleShowComments?: () => void;
  showComments?: boolean;
}) => {
  // const { t } = useTranslation()
  const { title, richTxtDescription, handleShowComments, description } = props;

  const getDescription = useCallback(() => {
    let descp = "";
    if (richTxtDescription) {
      descp = convertHtmlToPlainText(richTxtDescription).substring(0, 40);
    } else if (description) {
      descp = description.substring(0, 40);
    }
    return descp;
  }, [richTxtDescription, description]);

  return (
    <div
      className="grid grid-cols-1 gap-2 break-words select-none flex-1 w-full"
      onClick={handleShowComments}
    >
      <h6 className="font-semibold text-lg">
        {title && getEllipsisText(title, 30)}
      </h6>

      <div className="text-sm select-none md:hidden">
        {getDescription()} 
        {!props.showComments && <span className="text-[#373737] cursor-pointer text-sm font-semibold ">
          ... more
        </span>}
      </div>
    </div>
  );
};

const MobileViewMore = (props: any) => {
  const {
    // handleShowComments,
    onClose,
    showComments,
    // handleLikeToggle,
    // handleSaveView,
    // handleEdit,
    // liked,
    // likeCount,
    mobileMode,
    // comments,
    // editorInfo,
    // richTxtDescription,
    // profile,
    // project,
  } = props;

  if (!mobileMode) return null;

  return (
    <>
      {/* close button only for mobile model popup */}
      {/* {showComments && (
        <Button
          onClick={onClose}
          variant="flat"
          className="absolute top-12 left-3.5 w-fit h-fit min-h-0 min-w-0 px-2.5 py-1.5 bg-[#3737374D] rounded-full z-[99999]"
          endContent={<ArrowBack fill="white" />}
        />
      )}

      {!showComments && (
        <Button
          onClick={onClose}
          variant="flat"
          className="absolute top-12 left-3.5 w-fit h-fit min-h-0 min-w-0 px-[9px] py-[9px] bg-[#3737374D] rounded-full z-[99999]"
          endContent={<CloseCross fill="white" className="w-4 h-4" />}
        />
      )} */}
      <div className="flex py-2 px-[10px] justify-between items-center">
        {/* <OwnerInfo profile={profile} mobileMode={mobileMode} hideFollowButton /> */}
        {/* <div className="flex-1"/> */}

     
      </div>
    </>
  );
};

const ViewerDetail: FC<ViewerDetailProps> = (props) => {
  const {
    // liked,
    // handleLikeToggle,
    // onClose,
    // handleSaveView,
    // canEdit,
    // handleEdit,
    // likeCount,
    mobileMode,
    // hideViewButton,
    handleShowComments,
    showComments,
  } = props;
  const { id } = props.project ?? {};
  // const { username } = props.profile ?? {};
  // const {
  //   htmlContent: richTxtDescription,
  //   setEditorState: setEditorStateHtml,
  // } = useLexicalEditorHtml();

  const editorInfo = useMemo(()=> ({
    name: props.project?.config?.name || props.project?.name,
    description: props.project?.config?.description,
    textEditorState: props.project?.config?.textEditorState,
  }), [props.project]);

  // const renderDescriptionRichText = (editorJsonState: any) => {
  //   const json = JSON.parse(JSON.stringify(editorJsonState));
  //   json.root.children.shift();
  //   setEditorStateHtml(json);
  // };
  // const updateEdtInfo = (newInfo: {
  //   name?: string;
  //   description?: string;
  //   textEditorState?: any;
  // }) => {
  //   setEditorInfo((p) => ({ ...p, ...newInfo }));
  //   if (newInfo.textEditorState) {
  //     renderDescriptionRichText(newInfo.textEditorState);
  //   }
  // };

  // const client = useSelector((state: PixolState) => state.user.client);
  const [otherProjects, setOtherProjects] = useState<any[]>([]);
  const [comments, setComments] = useState<any[]>([]);
  const scrollableContainer = useRef<HTMLDivElement | null>(null);
  const [isScrollable, setIsScrollable] = useState(false);
  const [scrollHeight, setScrollHeight] = useState(0);
  const [openRichTxtEdit, setOpenRichTxtEdit] = useState(false);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const mamoizeScrollEvent = useCallback(
    debounce((event) => {
      const target = event.target as HTMLDivElement;
      const scrollableHeight = calcScrollableHeight(target);

      const isScroll = hasScroll(target);
      setIsScrollable(isScroll);

      if (!isScroll) return;

      setScrollHeight(scrollableHeight);
    }, 200),
    []
  );

  useEffect(() => {
    const scrollElm = scrollableContainer.current;
    if (!scrollElm) return;

    const addScrollEvent = () => {
      scrollElm.addEventListener("scroll", mamoizeScrollEvent);
    };
    const removeScrollEvent = () => {
      if (!scrollElm) return;
      scrollElm.removeEventListener("scroll", mamoizeScrollEvent);
    };
    addScrollEvent();
    return removeScrollEvent;
  }, [mamoizeScrollEvent, scrollableContainer]);

  useEffect(() => {
    const scrollElm = scrollableContainer.current;
    if (!scrollElm) return;

    const isScroll = hasScroll(scrollElm);
    setIsScrollable(isScroll);

    if (!isScroll) return;

    const scrollableHeight = calcScrollableHeight(scrollElm);
    setScrollHeight(scrollableHeight);
  }, [
    comments.length,
    scrollableContainer,
    otherProjects.length,
    isScrollable,
  ]);

  const isScrollUp = scrollHeight < 20;

  // const modelOwnerName = Utils.getNewUserName(props.profile);

  const handleTextEditorshow = (isOpen: boolean) => () =>
    setOpenRichTxtEdit(isOpen);
  return (
    <>
      <MobileViewMore
        handleShowComments={handleShowComments}
        showComments={showComments}

        mobileMode={mobileMode}
        comments={comments}
        editorInfo={editorInfo}
        // richTxtDescription={richTxtDescription}
        project={props.project}
      />
      <div
        id="scrollable-content"
        className={"flex flex-col h-full overflow-y-scroll scrollbar-hide px-[20px] gap-3" 
        }
        ref={scrollableContainer}
      >
        <div className="flex gap-2 w-full">
          <TitleAndMore
            title={editorInfo.name || ""}
            description={editorInfo.description}
            // richTxtDescription={richTxtDescription}
            handleShowComments={handleShowComments}
            showComments={showComments}
          />
          <ModelActionButtons

            project={props.project}
            mobileMode={mobileMode}
            handleShowComments={handleShowComments}
            hideViewButton={mobileMode}
            showComments={showComments}
            commentsLength={comments.length}
          />
        </div>
        {props.project && (showComments || !mobileMode) && <PreviewSidebar file={props.project} />}
      </div>
    </>
  );
};

export default ViewerDetail;
