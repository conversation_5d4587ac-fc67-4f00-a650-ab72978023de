import LoginLogo from "../icons/LoginLogo.tsx";
import AuthHeader from "./AuthHeader.tsx";
import BaseLink from "../BaseLink.tsx";
import { Link } from "@nextui-org/react";

function ForgotPasswordConfirmation({email}:{email:string}) {

  return  (
      <>
        <AuthHeader/>
        <div className="w-screen h-screen flex justify-center items-center fixed t-0 l-0 z-10 bg-[#F6F6F6]">
          <div className="w-[400px] flex flex-col gap-unit-xl p-7">
            <div className="flex items-center justify-center relative">
              <LoginLogo className="h-8" />
            </div>
            <p className="text-2xl font-bold text-center">{"Please check your mail!"}</p>
            <p className="text-[#373737] text-center text-[20px]">
                We’ve sent a verification email to {email}. Please check your inbox or spam folder.
            </p>
            {/* <form
                className="flex flex-col gap-unit-xl"
                onSubmit={(e) => {
                  e.preventDefault();
                //   handleResetPassword();
                }}
            >
              <div className="flex gap-unit-xl w-full">
                <Button type="submit" isLoading={loading} className="h-11 w-full rounded-full bg-[#6E72EA] text-white font-medium" color="primary">
                  {"Resend Mail"}
                </Button>
              </div>
            </form> */}
             <div className="text-center">
              <span className="text-[#686868] text-sm">
                {"Need help? "}
              </span>
              <Link href="mailto:<EMAIL>" className="underline">Contact us</Link>
            </div>
            <div className="text-center">
              <span className="text-[#686868] text-sm">
                {" < Back to "}
              </span>
              <BaseLink to={"/login"} className="text-[#6E72EA] text-sm underline">
                {"Log in"}
              </BaseLink>
            </div>
          </div>
        </div>
      </>
  ) ;
}

export default ForgotPasswordConfirmation;
