import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Input, Switch } from "@nextui-org/react";
import { useCallback, useEffect, useState } from "react";
import { useUser } from "../provider/UserProvider";
import DownArrow from "./icons/DownArrow";
import UpArrow from "./icons/UpArrow";

export interface UploadOptions {
  convert?: boolean;
  compress?: boolean;
  autoCenter?: boolean;
  autoScale?: boolean;
  encrypt?: boolean;
  encryptionKey?: string;
  uploader?: {
    name: string;
    username: string;
  }
  // Advanced options
  hidePointMesh?: boolean;
  hideLineMesh?: boolean;
  forceLayerMaterials?: boolean;
  importMaterials?: boolean;
  onlyVisible?: boolean;
  applyTransform?:boolean;
}

interface UploadPromptProps {
  defaultValues?: UploadOptions;
  show3dmOptions?: boolean;
  onSubmit: (value: UploadOptions) => void;
  onCancel: () => void;
}

const UploadPrompt: React.FC<UploadPromptProps> = ({ defaultValues, show3dmOptions, onSubmit, onCancel }) => {
  const initialConfig = {
    ...defaultValues,
    hidePointMesh: defaultValues?.hidePointMesh ?? true,
    hideLineMesh: defaultValues?.hideLineMesh ?? true,
    forceLayerMaterials: defaultValues?.forceLayerMaterials ?? true,
    importMaterials: defaultValues?.importMaterials ?? true,
    onlyVisible: defaultValues?.onlyVisible ?? true,
    applyTranform: defaultValues?.applyTransform ?? false
  };
  
  const [config, setConfig] = useState(initialConfig);
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  const { config: driveConfig, user } = useUser();
  

  const handleSubmit = useCallback(() => {
    if (!user) {
      console.error("User not found");
      return
    }
    const options = {...( config || {}) , uploader : {
      name : user.user,
      username: user.email,
    }};

    onSubmit(options || {});
  }, [config, onSubmit, user]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Enter") {
        handleSubmit();
      }
    };

    document.addEventListener("keydown", handleKeyDown);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [handleSubmit, config]);

  const canEncrypt = driveConfig?.features?.encryption?.enabled && driveConfig.plan?.["allowed-features"]?.includes("encryption");

  return (
    <Modal
      isOpen={true}
      placement="center"
      backdrop="blur"
      classNames={{
        wrapper: "z-[100000]",
        backdrop: "z-[10000]",
        base: "rounded-2xl",
      }}
      isDismissable={false}
      size="lg"
      onClose={onCancel}
    >
      <ModalContent>
        {() => (
          <>
            <ModalHeader className="text-2xl font-semibold">Upload Settings</ModalHeader>
            <ModalBody className="gap-0 max-h-[600px] overflow-y-auto">
            <div className="flex items-center justify-between py-4">
                <div>
                  <h3 className="text-base font-medium">Auto Convert to glb</h3>
                  <p className="text-sm text-gray-500">Convert the file to the GLB format for seamless integration</p>
                </div>
                <Switch
                  isSelected={config?.convert}
                  onValueChange={(value) => {
                    if (!value) {
                      setConfig({
                        ...config,
                        convert: false,
                        compress: false,
                        autoCenter: false,
                        autoScale: false
                      });
                    } else {
                      setConfig({ ...config, convert: value });
                    }
                  }}
                />
              </div>
              <hr className="border-t border-gray-200" />
              <div className="flex items-center justify-between py-4">
                <div>
                  <h3 className="text-base font-medium">Compression</h3>
                  <p className="text-sm text-gray-500">Reduce 3d file size without compromising quality</p>
                </div>
                <Switch
                  isDisabled={!config?.convert}
                  isSelected={config?.compress}
                  onValueChange={(value) => setConfig({ ...config, compress: value })}
                />
              </div>
              <hr className="border-t border-gray-200" />
              <div className="flex items-center justify-between py-4">
                <div>
                  <h3 className="text-base font-medium">Auto Center</h3>
                  <p className="text-sm text-gray-500">Automatically center the model on the axis for proper alignment.</p>
                </div>
                <Switch
                  isDisabled={!config?.convert}
                  isSelected={config?.autoCenter}
                  onValueChange={(value) => setConfig({ ...config, autoCenter: value })}
                />
              </div>
              <hr className="border-t border-gray-200" />
              <div className="flex items-center justify-between py-4">
                <div>
                  <h3 className="text-base font-medium">Auto Scale</h3>
                  <p className="text-sm text-gray-500">Automatically scale the model</p>
                </div>
                <Switch
                  isDisabled={!config?.convert}
                  isSelected={config?.autoScale}
                  onValueChange={(value) => setConfig({ ...config, autoScale: value })}
                />
              </div>
              <hr className="border-t border-gray-200" />
              {canEncrypt && (
                <>
                  <div className="space-y-1 py-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-base font-medium">Encrypted upload</h3>
                        <p className="text-sm text-gray-500">Secure the file with encryption to protect it from unauthorized access.</p>
                      </div>
                      <Switch
                        isDisabled={!config?.convert}
                        isSelected={config?.encrypt}
                        onValueChange={(value) => setConfig({ ...config, encrypt: value })}
                      />
                    </div>
                    {config?.encrypt && (
                      <>
                        <p className="text-sm mt-4 pb-2">Create a password to encrypt uploads.</p>
                        <Input
                            className="pb-2"
                            placeholder="Encryption password"
                            type="password"
                            size="lg"
                            autoComplete="new-password"
                            value={config?.encryptionKey}
                            onValueChange={(v) => setConfig({ ...config, encryptionKey: v })}
                        />
                        <p className="text-sm text-gray-500">If you lose or forget the password, it cannot be recovered</p>
                      </>
                    )}
                  </div>
                </>
              )}
                <>
                  <hr className="border-t border-gray-200" />
                  <div className="flex items-center justify-between py-4 cursor-pointer" onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}>
                    <div>
                      <h3 className="text-base font-medium">Advanced Settings</h3>
                      <p className="text-sm text-gray-500">Configure additional options for 3d files</p>
                    </div>
                    <div className="w-unit-3xl flex justify-center">
                      {showAdvancedSettings ? <UpArrow /> : <DownArrow />}
                    </div>
                  </div>
                  {showAdvancedSettings && (
                    <div className="pl-4">
                      <div className="flex items-center justify-between py-3">
                        <div>
                          <h3 className="text-base font-medium">Only Visible</h3>
                          <p className="text-sm text-gray-500">Import only visible objects</p>
                        </div>
                        <Switch
                          isDisabled={!config?.convert}
                          isSelected={config?.onlyVisible}
                          onValueChange={(value) => setConfig({ ...config, onlyVisible: value })}
                        />
                      </div>
                      <div className="flex items-center justify-between py-3">
                        <div>
                          <h3 className="text-base font-medium">Apply Tranform</h3>
                          <p className="text-sm text-gray-500 pr-4">The model stays put while its position, rotation, and scale are reset.</p>
                        </div>
                        <Switch
                          isDisabled={!config?.convert}
                          isSelected={config?.applyTransform}
                          onValueChange={(value) => setConfig({ ...config, applyTransform: value })}
                        />
                      </div>
                      {show3dmOptions && ( <>
                        <div className="flex items-center justify-between py-3">
                          <div>
                            <h3 className="text-base font-medium">Hide Point Mesh</h3>
                            <p className="text-sm text-gray-500">Hide point meshes in the model</p>
                          </div>
                          <Switch
                            isDisabled={!config?.convert}
                            isSelected={config?.hidePointMesh}
                            onValueChange={(value) => setConfig({ ...config, hidePointMesh: value })}
                          />
                        </div>
                        <div className="flex items-center justify-between py-3">
                          <div>
                            <h3 className="text-base font-medium">Hide Line Mesh</h3>
                            <p className="text-sm text-gray-500">Hide line meshes in the model</p>
                          </div>
                          <Switch
                            isDisabled={!config?.convert}
                            isSelected={config?.hideLineMesh}
                            onValueChange={(value) => setConfig({ ...config, hideLineMesh: value })}
                          />
                        </div>
                        <div className="flex items-center justify-between py-3">
                          <div>
                            <h3 className="text-base font-medium">Force Layer Materials</h3>
                            <p className="text-sm text-gray-500">Use layer materials for all objects</p>
                          </div>
                          <Switch
                            isDisabled={!config?.convert}
                            isSelected={config?.forceLayerMaterials}
                            onValueChange={(value) => setConfig({ ...config, forceLayerMaterials: value })}
                          />
                        </div>
                        <div className="flex items-center justify-between py-3">
                          <div>
                            <h3 className="text-base font-medium">Import Materials</h3>
                            <p className="text-sm text-gray-500">Import materials from the 3dm file</p>
                          </div>
                          <Switch
                            isDisabled={!config?.convert}
                            isSelected={config?.importMaterials}
                            onValueChange={(value) => setConfig({ ...config, importMaterials: value })}
                          />
                        </div>
                      </> )}
                    </div>
                  )}
                </>
            </ModalBody>
            <ModalFooter className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {/* <Checkbox
                  isSelected={dontShowAgain}
                  onValueChange={setDontShowAgain}
                >
                  Don't show again
                </Checkbox> */}
              </div>
              <div className="flex gap-2">
                {/* <Button
                    className="h-8 rounded-3xl bg-[#F2F2F2]"
                  variant="light"
                  onPress={onCancel}
                >
                  Skip
                </Button> */}
                <Button
                    className="h-8 rounded-3xl"
                  color="primary"
                  onPress={handleSubmit}
                >
                  Continue
                </Button>
              </div>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};

interface UseUploadPromptReturn {
  openUploadPrompt: (defaultValues?: UploadOptions, show3dmOptions?: boolean) => Promise<UploadOptions | null>;
  UploadPromptComponent: React.FC;
}

const useUploadPrompt = (): UseUploadPromptReturn => {
  const [visible, setVisible] = useState(false);
  const [config, setConfig] = useState<UploadOptions>({});
  const [show3dmOptions, setShow3dmOptions] = useState<boolean | undefined>();
  const [resolvePrompt, setResolvePrompt] = useState<(value: UploadOptions | null) => void | null>();

  const openUploadPrompt = (defaultValues?: UploadOptions, show3dmOptions?: boolean) => {
    defaultValues && setConfig(defaultValues);
    setShow3dmOptions(show3dmOptions);
    setVisible(true);
    return new Promise<UploadOptions | null>((resolve) => {
      setResolvePrompt(() => resolve);
    });
  };

  const handleResult = (result: UploadOptions | null) => {
    setVisible(false);
    if (resolvePrompt) {
      resolvePrompt(result);
    }
  };

  const handleSubmit = (value: UploadOptions) => {
    handleResult(value);
  };

  const handleCancel = () => {
    handleResult(null);
  };

  const UploadPromptComponent: React.FC = () => (visible ? <UploadPrompt defaultValues={config} show3dmOptions={show3dmOptions} onSubmit={handleSubmit} onCancel={handleCancel} /> : null);

  return { openUploadPrompt, UploadPromptComponent };
};

// eslint-disable-next-line react-refresh/only-export-components
export { useUploadPrompt, UploadPrompt };
