import { Chip, Input } from "@nextui-org/react";
import { FC, useCallback, useEffect, useState } from "react";
import toast from "react-hot-toast";
import CloseIcon from "./icons/Close";
import { useUser } from "../provider/UserProvider";
import { Button } from "./Button";
import { CustomFileData } from "../api/api";

interface TagsProps {
  file: CustomFileData;
  updateTags: (file: {id :string},tags: string) => Promise<void>;
  onClose: () => void;
}

export const Tags: FC<TagsProps> = (props) => {
  const [tag, setTag] = useState<string>("");
  const [tags, setTags] = useState<string[]>([]);
  const [saving, setSaving] = useState<boolean>(false);
  const { api } = useUser();

  useEffect(() => {
    if (props.file?.tags) {
      setTags(props.file?.tags.split(","));
    } else {
      setTags([]);
    }
  }, [props.file?.tags]);

  const saveTags = useCallback(async () => {
    if (!api) return;
    setSaving(true);
    await props.updateTags(props.file, tags.join(","));
    setSaving(false);
    props.onClose();
  }, [api, props, tags]);

  const addTag = useCallback(
    (e: KeyboardEvent) => {
      if (e.key !== "Enter") return;
      if (tag.trim() === "") return;
      if (tag.trim().length > 40) {
        toast.error("Tag length should be less than 40 characters");
        return;
      }
      if (tag.trim().length < 2) {
        toast.error("Tag length should be at least 2 characters");
        return;
      }
      if (tag.startsWith("_")) {
        toast.error("Tag should not start with _");
        return;
      }

      tags.push(tag.trim());
      setTags([...tags]);
      // setProject({ ...project, tags });
      setTag("");
    },
    [tag, tags]
  );

  const deleteTag = useCallback(
    (index: number) => {
      tags.splice(index, 1);
      setTags([...tags]);
    },
    [tags]
  );

  return (
    <div className="flex flex-col gap-4">
      <div className="flex gap-3 items-center">
        <Input
          value={tag}
          onKeyDown={addTag as any}
          onValueChange={(v) => setTag(v)}
          variant="bordered"
          placeholder="Add tags"
          classNames={{
            input: "h-11",
            inputWrapper: "h-11 rounded-full bg-transparent"
          }}
        />
        <Button 
          name="Add" 
          onClick={() => addTag({ key: "Enter" } as any)} 
          size="sm"
          className="px-4 py-1.5 rounded-full h-9 bg-[#6366f1] hover:bg-[#5558e6] text-white"
          fullWidth={false}
        />
      </div>

      {tags && (
        <div className="flex flex-wrap gap-2">
          {tags
            ?.filter((tag) => tag && !tag?.startsWith("_"))
            .map((tag, i) => (
              <Chip
                key={tag + i}
                variant="flat"
                classNames={{
                  base: "bg-gray-100 rounded-full h-7",
                  content: "text-sm font-normal px-3 flex items-center",
                  closeButton: "pr-2 pl-1",
                }}
                endContent={
                  <CloseIcon 
                    onClick={() => deleteTag(i)} 
                    className="cursor-pointer w-3.5 h-3.5" 
                  />
                }
              >
                {tag}
              </Chip>
            ))}
        </div>
      )}

      <div className="flex justify-end gap-2 pt-2">
        <Button
          size="sm"
          name="Cancel"
          onClick={props.onClose}
          className="px-4 py-1.5 rounded-full h-9"
          fullWidth={false}
        />
        <Button
          size="sm"
          name="Save"
          onClick={saveTags}
          isLoading={saving}
          className="px-4 py-1.5 rounded-full h-9 bg-[#6366f1] hover:bg-[#5558e6] text-white"
          fullWidth={false}
        />
      </div>
    </div>
  );
};
