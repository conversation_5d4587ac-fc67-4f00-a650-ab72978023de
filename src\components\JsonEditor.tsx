import { useEffect, FC, useState, useCallback, useRef } from "react";
import { CustomFileData } from "../api/api";
import { useUser } from "../provider/UserProvider";
// @ts-ignore
import { JSONEditor } from "react-json-editor-viewer";
import Editor, { Monaco } from "@monaco-editor/react";
import toast from "react-hot-toast";
import { Button } from "./Button";

interface ViewerProps {
  file: CustomFileData;
  setLoading: any;
}

const JsonEditor: FC<ViewerProps> = (props: ViewerProps) => {
  const [data, setData] = useState<any>(null);
  const { api } = useUser();
  const monacoRef = useRef<any>();
  const [loading, setLoading] = useState<boolean>(false);
  const [invalid, setInvalid] = useState<boolean>(false);

  const loadFile = useCallback(async () => {
    if (props.file && api) {
      const data = await fetch(api.getDownloadUrl(props.file));
      const json = await data.json();
      setData(json);
      props.setLoading(false);
    }
  }, [props, api]);

  useEffect(() => {
    loadFile();
  }, [loadFile, props.file]);

  function handleEditorDidMount(editor: any, monaco: Monaco) {
    monacoRef.current = monaco;
    // setMonacoEditor(editor);
    //make it format document on every change
  }

  const save = useCallback(async () => {
    if(invalid){
      toast.error("Invalid JSON")
      return
    }
    toast.loading("Saving...", { id: "saving" });
    setLoading(true);
    if (api) {
      try {
        const blob = new Blob([JSON.stringify(data)], { type: "application/json" });
        const file = new File([blob], props.file.name, { type: "application/json" });
        const response = await api.updateFile({
          id: props.file.id,
          file: file,
        });
        if (response.data && !response.error) {
          toast.success("Saved", { id: "saving" });
        } else {
          toast.error("Failed to save", { id: "saving" });
        }
      } catch (e) {
        toast.error("Failed to save", { id: "saving" });
      }
    }
    setLoading(false);
  }, [invalid, data, api, props.file]);

  return (
    //   <ReactShadowRoot>
    <div className="w-full h-full flex flex-col">
      <div className="flex justify-end p-unit-lg">
        <Button
          name="Save"
          onClick={() => save()}
          isLoading={loading}
          className="h-unit-3xl px-unit-xl"
          fullWidth={false}
          color="primary"
        />
      </div>
      {data && <div className="w-full h-full flex overflow-hidden pt-unit-md p-unit-2xl gap-unit-xl">
        <div className="w-[50vw] h-full overflow-auto">
          <JSONEditor
            data={data}
            onChange={(key: any, value: any, parent: any, data: any) => {
              setData(data.root || data);
            }}
          />
        </div>
        <div className="w-[50vw] h-screen">
          <Editor
            defaultValue={JSON.stringify(data, null, 4)}
            defaultLanguage="json"
            onMount={handleEditorDidMount}
            onChange={(value) => {
              if(value){
                try {
                  setData(JSON.parse(value))
                  setInvalid(false)
                } catch (error) {
                  setInvalid(true)
                  console.error(error)
                }
              
              }
            }}
          />
        </div>
      </div>}
    </div>
    //   </ReactShadowRoot>
  );
};

export default JsonEditor;
