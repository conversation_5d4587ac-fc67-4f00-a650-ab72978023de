import { FC, useEffect, useState } from "react";
import { Modal } from "../Modal";
import { CustomFileData } from "../../api/api";
import { Autocomplete, AutocompleteItem, Button } from "@nextui-org/react";

interface EditFileInfoModalProps {
  file: CustomFileData;
  updateTags: (file: { id: string }, tags: string) => Promise<void>;
  onClose: () => void;
}

export type FilterCategory = "diamond-type" | "ring-shape" | "diamond-shape" | "material" | "gemstone" | "setting" | "petite" | "bridal" | "snowflake";

const fileInfos = {
  "ring-shape": ["Cushion", "Emerald", "Heart", "Marquise", "Oval", "Pear", "Princess", "Radiant", "Round"],
  "diamond-type": ["Cushion", "Emerald", "Heart", "Marquise", "Oval", "Pear", "Princess", "Radiant", "Round"],
  "diamond-shape": ["Round", "Emerald", "Oval", "Pear", "Cushion"],
  material: ["White Gold", "Yellow Gold", "Rose Gold", "Platinum"],
  gemstone: ["Sapphire", "Ruby", "Emerald", "Diamond"],
  setting: ["Pave", "Channel", "Bezel", "Prong", "Halo"],
  petite: [],
  bridal: [],
  snowflake: [],
};

const EditFileInfoModal: FC<EditFileInfoModalProps> = (props) => {
  // const { modalState } = useModal();
  const [saving, setSaving] = useState<boolean>(false);
  const [info, setInfo] = useState<{ [key: string]: string }>();

  useEffect(() => {
    //set info from tags
    const tags = props.file.tags?.split(",") || [];
    const newInfo: { [key: string]: string } = {};
    Object.keys(fileInfos).forEach((key) => {
      const tag = tags.find((tag) => tag.startsWith(key));
      if (tag) {
        newInfo[key] = tag.split(":")[1];
      }
    });
    setInfo(newInfo);
  }, [props.file]);


  const saveInfo = async () => {
    if (!props.file || !info) return;
    setSaving(true);

    //convert info to tags
    const tags = Object.entries(info)
      .map(([key, value]) => {
        if (!value) return "";
        return `${key}:${value}`;
      })
      .filter(Boolean);
    const oldTags = (props.file.tags || "").split(",");

    //replace old info
    tags.forEach((tag) => {
      const key = tag.split(":")[0];
      oldTags?.forEach((oldTag, index) => {
        if (oldTag.split(":")[0] === key) {
          oldTags[index] = tag;
        }
      });
    });
    //remove duplicates
    const newTags = [...new Set([...oldTags, ...tags])].join(",");
    await props.updateTags(props.file, newTags);
    setSaving(false);
    props.onClose();
  };

  return (
    <Modal isOpen={true} onClose={props.onClose} size="2xl" backdrop="blur">
      {() => (
        <div className="bg-white">
          <p className="text-large font-bold ml-unit-xl ">File Info</p>
          <div className="h-fit w-full bg-white p-unit-xl grid grid-cols-2 flex-col gap-unit-xl pt-0 ">
            {info &&
              Object.entries(fileInfos).map(([key, items]) => (
                <div className="flex gap-unit-xl items-center">
                  <p className="w-unit-6xl font-semibold">{key.replace("-", " ").toUpperCase()}</p>
                  <Autocomplete
                    value={info[key]}
                    inputProps={{
                      classNames: { inputWrapper: "h-unit-3xl" },
                    }}
                    defaultInputValue={info[key]}
                    onSelectionChange={(value) => value !== null && setInfo({ ...info, [key]: value as any })}
                    onValueChange={(value: string) => {
                      setInfo({ ...info, [key]: value });
                    }}
                    allowsCustomValue
                    onKeyDown={(e) => {
                      if ("continuePropagation" in e) {
                        e.continuePropagation();
                      }
                    }}
                    shouldCloseOnBlur
                    aria-label={key}
                  >
                    {items.map((option) => (
                      <AutocompleteItem key={option} value={option} aria-label={option} shouldHighlightOnFocus>
                        {option}
                      </AutocompleteItem>
                    ))}
                  </Autocomplete>
                </div>
              ))}
            <div className="flex gap-unit-md mt-auto justify-end">
              <Button onClick={props.onClose} className="py-unit-xl" name="Cancel" fullWidth color="danger">Cancel</Button>
              <Button isLoading={saving} onClick={saveInfo} className="py-unit-xl" fullWidth color="primary">Save</Button>
            </div>
          </div>
        </div>
      )}
    </Modal>
  );
};

export default EditFileInfoModal;
