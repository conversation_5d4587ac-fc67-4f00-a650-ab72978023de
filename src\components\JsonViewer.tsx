import { useEffect, FC, useState, useCallback } from "react";
import { CustomFileData } from "../api/api";
import { useUser } from "../provider/UserProvider";
import { Editor } from "@monaco-editor/react";

interface ViewerProps {
  file: CustomFileData;
  setLoading: any;
  setError: any;
}

const JsonViewer: FC<ViewerProps> = (props: ViewerProps) => {
  const [data, setData] = useState<any>(null);
  const { api } = useUser();

  const loadFile = useCallback(async () => {
    if (props.file && api) {
      try {
        const data = await fetch(api.getDownloadUrl(props.file));
        const json = await data.json();
        setData(json);
        props.setLoading(false);
      } catch (e) {
        props.setError(true);
        props.setLoading(false);
      }
    }
  }, [props, api]);

  useEffect(() => {
    loadFile();
  }, [loadFile, props.file]);

  return (
    <div className="w-full h-full overflow-auto p-unit-xl isolate ">
      {data && (
        <Editor
          value={JSON.stringify(data, null, 2)}
          defaultLanguage="json"
          options={{ formatOnType: true, readOnly: true }}
          // onMount={handleEditorDidMount}
          // onChange={(value) => value && setData(JSON.parse(value))}
        />
      )}
    </div>
  );
};

export default JsonViewer;
