import {  Input } from "@nextui-org/react";
import { FC, useCallback, useEffect, useRef } from "react";

interface InputProps {
  className?: string;
  type: string;
  label?: string;
  placeholder?: string;
  size?: "sm" | "md" | "lg";
  value?: string;
  onChange?: any;
  validationState?: "valid" | "invalid";
  errorMessage?: string;
  onBlur?: any;
  onSubmit?: any;
  startContent?: any;
  autoComplete?: string;
}

const MyInput: FC<InputProps> = (props) => {
  const ref = useRef<HTMLInputElement>(null);

  const handleKeyDown = useCallback((e: any) => {
    if (e.key === "Escape") {
      // props.onBlur();
      ref.current?.blur();
    }
    if (e.key === "Enter") {
      props.onSubmit?.();
      ref.current?.blur();
    }
  }, [props]);

  useEffect(() => {
    if (!ref.current) return;
    const input = ref.current;
    input.addEventListener("keydown", handleKeyDown);
    return () => {
      input?.removeEventListener("keydown", handleKeyDown);
    };
  }, [handleKeyDown, props.onSubmit]);

  return (
    <Input
      autoComplete={props.autoComplete}
      ref={ref}
      key={"outside"}
      type={props.type}
      label={props.label}
      labelPlacement="outside"
      placeholder={props.placeholder}
      size={props.size}
      fullWidth
      value={props.value}
      onValueChange={props.onChange}
      validationState={props.validationState}
      errorMessage={props.errorMessage}
      onBlur={props.onBlur}
      startContent={props.startContent}
      classNames={{
        base: ["h-full"],
        label: ["text-default-600 font-inherit ml-unit-lg ", "font-light"],
        input: "text-default-foreground font-normal  placeholder:text-tiny placeholder:font-normal placeholder:text-default-400 ",
        innerWrapper: [" font-bold h-full"],
        inputWrapper: ["bg-white ", "", "rounded-full" , "h-full"],
      }}
      className={props.className + ""}
    />
  );
};

export { MyInput as Input };
