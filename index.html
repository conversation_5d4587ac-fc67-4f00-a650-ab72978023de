<!doctype html>
<html lang="en">
  <head>
     <meta charset="UTF-8" />
     <link rel="icon" type="image/svg+xml" href="https://drive.ijewel3d.com/logo-sm.svg" />
     <title>iJewel Drive</title>
     <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- TODO - see if it should be added before or after, or add it internally by the script based on the version -->
<!--    <script src="https://dist.pixotronics.com/webgi/runtime/bundle-0.9.19.js"></script>-->
<!--    <script>window.webgi = window;</script>-->
  </head>
  <body>
    <div id="root" class="font-normal"></div>
    <script>
      //do this only for the verify-email and reset-password pages
      if (window && window.location.pathname.includes('//') && 
        (window.location.pathname.includes('verify-email') || window.location.pathname.includes('reset-password'))) {
        const normalizedPath = window.location.pathname.replace(/\/{2,}/g, '/');
        if (normalizedPath !== window.location.pathname) window.history.replaceState(null, '', normalizedPath + window.location.search);
      }
    </script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
