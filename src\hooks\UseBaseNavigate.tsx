import { useCallback } from "react";
import { useNavigate, useParams, parsePath, NavigateOptions } from "react-router-dom";
import { useUser } from "../provider/UserProvider";
import { FileAPI } from "../api/api";

//custom hook that gives the same functionality as BaseLink 
export function useBaseNavigate() {
  const navigate = useNavigate();
  
  const { api } = useUser() || FileAPI.getInstance();

  const { basename: routeBasename } = useParams();

  return useCallback(
    (to: string | Partial<Location> | number, options?: NavigateOptions) => {
      if (typeof to === "number") {
        return navigate(to);
      }

      const toObj = typeof to === "string" ? parsePath(to) : to;
      
      if (!toObj?.pathname || !api) {
        return navigate(to, options);
      }

      let { pathname } = toObj;
      const { search, hash } = toObj;

      if (!pathname.startsWith("/")) {
        return navigate({ pathname, search, hash }, options);
      }

      const dynamicBase = routeBasename || api.getBaseName() || "drive";

      if (dynamicBase) {
        pathname = `/${dynamicBase}${pathname}`;
      }

      return navigate({ pathname, search, hash }, options);
    },
    [navigate, api, routeBasename]
  );
}
