import { FC, useEffect, useState, useMemo } from "react";
import { Modal } from "../Modal";
import ViewFile from "../ViewFile";
import { CustomFileData } from "../../api/api";
import { useUser } from "../../provider/UserProvider";
import { useModal } from "../../provider/useModal";
import { PreviewHeader } from "../PreviewHeader";
import { useBrowser } from "../../provider/BrowserProvider";

interface PreviewProps {
  file: CustomFileData;
  onClose: () => void;
}

const PreviewModal: FC<PreviewProps> = (props: PreviewProps) => {
  const { modalState, openModal } = useModal();
  const { api } = useUser();
  const { fileMap, currentView } = useBrowser();
  const [file, setFile] = useState<CustomFileData | null>(null);
  const [zoomLevel, setZoomLevel] = useState(100);
  const [id , setId] = useState<string | null>(null);

  // Get compatible files based on file extension and current view
  const compatibleFiles = useMemo(() => {
    if (!file) return [];
    const fileExt = file.file?.split('.').pop()?.toLowerCase();
    return Object.values(fileMap).filter(f => 
      !f.isDir && 
      f.name.split('.').pop()?.toLowerCase() === fileExt &&
      // Match deleted status with current view
      (currentView === 'trash' ? f.deleted : !f.deleted)
    );
  }, [file, fileMap, currentView]);


  useEffect(() => {
    if(!id && props.file?.id) {
      setId(props.file.id);
    }
  }, [id, props.file]);

  useEffect(() => {
    if(!api || !id) {
      setFile(null);
      return;
    }
    api.viewFile(id).then((data) => {
      if(!data.data  || data.error) {
        console.log("Failed to get file", data);
        return;
      }
      setFile(data.data);
    });
  }, [id, api]);

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 25, 200));
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 25, 25));
  };

  const handleShare = () => {
    openModal("SHARE", { file });
  };

  const handleDownload = () => {
    if (file && api) {
      window.open(api.getDownloadUrl(file));
    }
  };

  const handleFileChange = (newFile: CustomFileData) => {
    setId(newFile.id);
  };

  return (
    <Modal isOpen={modalState.modalType == "PREVIEW"} onClose={props.onClose} 
    className="h-[100vh] md:h-[70vh]" size="5xl" backdrop="blur" placement="bottom-center"
    hideCloseButton isDismissable={false} shouldCloseOnInteractOutside={() => false} >
      {() => (
        <div className="h-full w-full bg-white flex flex-col">
          <PreviewHeader
            files={compatibleFiles}
            initialFile={file!}
            onClose={props.onClose}
            onShare={handleShare}
            onDownload={handleDownload}
            onZoomIn={handleZoomIn}
            onZoomOut={handleZoomOut}
            // onFullScreen={handleFullScreen}
            zoomLevel={zoomLevel}
            onFileChange={handleFileChange}
          />
          <div className="flex-1 overflow-hidden">
            <ViewFile file={file} />
          </div>
        </div>
      )}
    </Modal>
  );
};

export default PreviewModal;
