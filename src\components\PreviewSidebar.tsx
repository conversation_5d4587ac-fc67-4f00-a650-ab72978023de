import { FC, useCallback, useEffect, useMemo, useState } from "react";
import { CustomFileData } from "../api/api";
import { useUser } from "../provider/UserProvider";
import FileDescription from "./fileInfo/FileDescription";

interface FileSidebarProps {
  file: CustomFileData;
}

const PreviewSidebar: FC<FileSidebarProps> = ({ file }) => {
  const {api} = useUser();
  const [user , setUser] = useState<any>(null);

  const formatDate = (date: string | Date) => {
    const d = date instanceof Date ? date : new Date(date);

    const onlyDate = d.getDate().toString().padStart(2, '0');
    const month = d.toLocaleString('default', { month: 'short' });
    const year = d.getFullYear().toString().slice(-2);
    let hours = d.getHours();
    const minutes = d.getMinutes().toString().padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours ? hours : 12;
    
    return `${onlyDate} ${month} ${year}, ${hours}:${minutes}${ampm}`;
  }

  const formatSize = (bytes: number | undefined) => {
    if (!bytes) return "0 MB";
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };

  // Get only non-system tags (those that don't start with underscore)
  const getTags = () => {
    if (!file.tags) return [];
    return file.tags
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag && !tag.startsWith('_'));
  };

    const description = useMemo(() => {
      return file.config?.description;
    }, [file]);

  const getUser = useCallback(async () => {
    if (!api || !file.created_by) return;
    const {data, error} = await api.viewUser(file.created_by);
    if (error) {
      console.log("Error fetching user", error);
      return
    }
    return data;
  }, [api, file]);

  useEffect(() => {
     getUser().then((data) => {
       if(data) setUser(data);
     })
  }, [getUser]);


  return (
    <div className="w-full md:w-[320px] bg-white flex flex-col h-full overflow-y-auto">
      <div className="flex-1 overflow-y-auto">
        <div className="">
          {/* <h2 className="text-base font-medium mb-4">Info {file.name}</h2> */}
          
          <div className="space-y-6">
            <div>
              {/* <h3 className="text-sm font-medium mb-4">Details</h3> */}
              <div className="space-y-4">
                {description && <div className="w-[90%] md:w-[80%]">
                  {/* <h4 className="text-sm text-gray-500">Description</h4> */}
                  <p className="text-sm text-gray-700"><FileDescription file={file}/></p>
                </div>}
                
                <div>
                  <h4 className="text-sm text-gray-500">Type</h4>
                  <p className="text-sm text-gray-700">
                    {file.file?.split('.').pop()?.toUpperCase()}
                  </p>
                </div>

                {/* <div>
                  <h4 className="text-sm text-gray-500">Dimensions</h4>
                  <p className="text-sm text-gray-700">
                    {file.meta?.width && file.meta?.height 
                      ? `${file.meta.width} x ${file.meta.height}`
                      : "Unknown"}
                  </p>
                </div> */}

                {user && <div>
                  <h4 className="text-sm text-gray-500">Uploader</h4>
                  <p className="text-sm text-gray-700">{user?.name || user?.username || "Unknown"}</p>
                </div>}

                <div>
                  <h4 className="text-sm text-gray-500">Created</h4>
                  <p className="text-sm text-gray-700">{formatDate(file.modDate || '')}</p>
                </div>

                <div>
                  <h4 className="text-sm text-gray-500">Size</h4>
                  <p className="text-sm text-gray-700">{formatSize(file.meta?.size)}</p>
                </div>
              </div>
            </div>

            {/* <div>
              <h3 className="text-sm font-medium mb-2">Comments</h3>
              <div className="text-sm text-gray-500">No comments</div>
            </div> */}

            <div>
              <h3 className="text-sm font-medium mb-2">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {getTags().length > 0 ? (
                  getTags().map((tag, index) => (
                    <span 
                      key={index} 
                      className="bg-gray-100 px-2 py-1 rounded text-sm text-gray-700"
                    >
                      {tag}
                    </span>
                  ))
                ) : (
                  <div className="text-sm text-gray-500">No tags</div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PreviewSidebar;