import { FC } from "react";
import { Modal } from "../Modal";
import { CustomFileData } from "../../api/api";
import { Tags } from "../Tags";
import { useModal } from "../../provider/useModal";
import { Button } from "../Button";
import Close from "../icons/Close";

interface EditTagsModalProps {
  file: CustomFileData;
  updateTags: (file: { id: string }, tags: string) => Promise<void>;
  onClose: () => void;
}

const EditTagsModal: FC<EditTagsModalProps> = (props) => {
  const { modalState } = useModal();

  return (
    <Modal isOpen={modalState.modalType === "EDIT_TAGS"} onClose={props.onClose} size="md" backdrop="blur" hideCloseButton>
      {() => (
        <div className="w-full bg-white p-6 flex flex-col gap-4 rounded-xl">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Edit tags</h2>
            <Button
              varient="light"
              size="sm"
              onClick={props.onClose}
              className="w-8 h-8 min-w-8"
              name={<Close />}
              fullWidth={false}
            />
          </div>

          <div className="flex flex-col">
            {props.file && <Tags updateTags={props.updateTags} onClose={props.onClose} file={props.file} />}
          </div>
        </div>
      )}
    </Modal>
  );
};

export default EditTagsModal;
