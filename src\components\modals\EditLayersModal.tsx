import { FC, useEffect, useState, useCallback } from "react";
import { Modal } from "../Modal";
import { CustomFileData } from "../../api/api";
import { Tags } from "../Tags";
import { useModal } from "../../provider/useModal";

interface EditLayersModalProps {
  file: CustomFileData;
  updateTags: (file: { id: string }, tags: string) => Promise<void>;
  onClose: () => void;
}

const EditLayersModal: FC<EditLayersModalProps> = (props) => {
  const { modalState } = useModal();
  const [layerTags, setLayerTags] = useState<string[]>([]);

  // Process the tags to remove the '_layer:' prefix
  useEffect(() => {
    if (props.file?.tags) {
      const tagsArray = props.file.tags.split(",");
      const layerTagsArray = tagsArray
        .filter((tag) => tag.startsWith("_layer:"))
        .map((tag) => tag.replace("_layer:", "").trim());
      setLayerTags(layerTagsArray);
    } else {
      setLayerTags([]);
    }
  }, [props.file?.tags]);

  // Function to handle updating the tags
  const updateLayerTags = useCallback(
    async (file: { id: string }, tags: string) => {
      // Add back the '_layer:' prefix to each tag
      const tagsArray = tags
        .split(",")
        .map((tag) => tag.trim())
        .filter((tag) => tag !== "");

      const prefixedTags = tagsArray.map((tag) => `_layer:${tag}`);

      // Get the other tags that are not layer tags
      const otherTags = props.file.tags 
        ? props.file.tags 
            .split(",")
            .filter(
              (tag) => !tag.startsWith("_layer:") && tag.trim() !== ""
            )
        : [];

      // Combine the other tags with the prefixed layer tags
      const updatedTagsArray = [...otherTags, ...prefixedTags];
      const updatedTags = updatedTagsArray.join(",");
      console.log(updatedTags);
      await props.updateTags(file, updatedTags);
      props.onClose();
    },
    [props, props.file]
  );

  return (
    <Modal
      isOpen={modalState.modalType === "EDIT_LAYERS"}
      onClose={props.onClose}
      size="2xl"
    >
      {() => (
        <div className="h-[50vh] w-full bg-white p-unit-xl flex flex-col">
          {props.file && (
            <Tags
              updateTags={updateLayerTags}
              onClose={props.onClose}
              file={{ ...props.file, tags: layerTags.join(",") }}
            />
          )}
        </div>
      )}
    </Modal>
  );
};

export default EditLayersModal;
