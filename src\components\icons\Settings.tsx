import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g clipPath="url(#clip0_201_4695)">
            <mask id="mask0_201_4695" style={{maskType: "alpha",}} maskUnits="userSpaceOnUse" x="0" y="0" width="20"
                  height="20">
                <rect width="20" height="20" fill="#D9D9D9"/>
            </mask>
            <g mask="url(#mask0_201_4695)">
                <path
                    d="M5.125 10L4.85417 8.83333C4.65972 8.76389 4.47917 8.68056 4.3125 8.58333C4.14583 8.48611 3.98611 8.375 3.83333 8.25L2.66667 8.60417L2 7.4375L2.875 6.60417C2.84722 6.40972 2.82986 6.20833 2.82292 6C2.81597 5.79167 2.83333 5.59028 2.875 5.39583L2 4.60417L2.66667 3.4375L3.8125 3.77083C3.96528 3.63194 4.12847 3.51389 4.30208 3.41667C4.47569 3.31944 4.65972 3.23611 4.85417 3.16667L5.125 2H6.45833L6.72917 3.16667C6.92361 3.23611 7.11111 3.31944 7.29167 3.41667C7.47222 3.51389 7.63889 3.63194 7.79167 3.77083L8.9375 3.4375L9.58333 4.60417L8.75 5.375C8.79167 5.58333 8.8125 5.79167 8.8125 6C8.8125 6.20833 8.79167 6.41667 8.75 6.625L9.60417 7.45833L8.9375 8.60417L7.77083 8.25C7.61806 8.38889 7.45486 8.50347 7.28125 8.59375C7.10764 8.68403 6.92361 8.76389 6.72917 8.83333L6.45833 10H5.125ZM5.8125 7.5C6.22917 7.5 6.58333 7.35417 6.875 7.0625C7.16667 6.77083 7.3125 6.41667 7.3125 6C7.3125 5.58333 7.16667 5.22917 6.875 4.9375C6.58333 4.64583 6.22917 4.5 5.8125 4.5C5.39583 4.5 5.04167 4.64583 4.75 4.9375C4.45833 5.22917 4.3125 5.58333 4.3125 6C4.3125 6.41667 4.45833 6.77083 4.75 7.0625C5.04167 7.35417 5.39583 7.5 5.8125 7.5ZM12.8958 18L12.5625 16.5208C12.3264 16.4375 12.1007 16.3368 11.8854 16.2188C11.6701 16.1007 11.4653 15.9583 11.2708 15.7917L9.83333 16.25L9 14.7917L10.0833 13.7708C10.0278 13.5208 10 13.2674 10 13.0104C10 12.7535 10.0278 12.5 10.0833 12.25L9 11.25L9.83333 9.79167L11.25 10.2292C11.4444 10.0625 11.6528 9.91319 11.875 9.78125C12.0972 9.64931 12.3264 9.54167 12.5625 9.45833L12.8958 8H14.5625L14.9167 9.45833C15.1667 9.54167 15.4028 9.64583 15.625 9.77083C15.8472 9.89583 16.0556 10.0486 16.25 10.2292L17.6667 9.79167L18.5 11.25L17.4375 12.2292C17.4931 12.4931 17.5208 12.7535 17.5208 13.0104C17.5208 13.2674 17.4931 13.5278 17.4375 13.7917L18.5 14.7917L17.6667 16.25L16.25 15.7917C16.0556 15.9583 15.8438 16.1042 15.6146 16.2292C15.3854 16.3542 15.1528 16.4583 14.9167 16.5417L14.5625 18H12.8958ZM13.75 15C14.3056 15 14.7778 14.8056 15.1667 14.4167C15.5556 14.0278 15.75 13.5556 15.75 13C15.75 12.4444 15.5556 11.9722 15.1667 11.5833C14.7778 11.1944 14.3056 11 13.75 11C13.1944 11 12.7222 11.1944 12.3333 11.5833C11.9444 11.9722 11.75 12.4444 11.75 13C11.75 13.5556 11.9444 14.0278 12.3333 14.4167C12.7222 14.8056 13.1944 15 13.75 15Z"
                    fill="#373737"/>
            </g>
        </g>
        <defs>
            <clipPath id="clip0_201_4695">
                <rect width="20" height="20" fill="white"/>
            </clipPath>
        </defs>
    </svg>
)
export default SvgComponent
