import { useEffect, useMemo, useState } from "react";
import { useUser } from "../provider/UserProvider";
import { <PERSON><PERSON>, Spinner } from "@nextui-org/react";
import MiniViewer from "./MiniViewer";
import { CustomFileData } from "../api/api";
import JsonViewer from "./JsonViewer";
import HDRViewer from "./HDRViewer";
import useWindowSize from "../hooks/useWindowSize.ts";
import ViewerDetails from "./ViewerModal/components/ViewerDetails.tsx";
import RightArrow from "./icons/RightArrow.tsx";

type PreviewType = "image" | "video" | "text" | "3d" | "json" | "hdr" | "unknown";

function ViewFile({ file }: { file: CustomFileData | null }) {
  const { api } = useUser();
  const [type, setType] = useState<PreviewType>("unknown");
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<boolean>(false);
  const [showComments, setShowComments] = useState(false);
  const [isHideModalPanel, setIsHideModalPanel] = useState(false);
  const { isBelowBreakpoint: isBelowMd } = useWindowSize("md");

  const handleModelPanelToggle = () => {
    const newValue = !isHideModalPanel;
    setIsHideModalPanel(newValue);
    if (typeof window !== "undefined") {
      localStorage.setItem("isHideModalPanel", JSON.stringify(newValue));
    }
  };

  const handleShowComments = () => {
    setShowComments((p) => !p);
  };
  const hideModalPanel = !isBelowMd && isHideModalPanel;

  // useEffect(() => {
  //   setLoading(true);
  //   setError(false);
  // }, [file]);

  useEffect(() => {
    if (file) {
      switch (file.file?.split(".").pop()?.toLowerCase()) {
        case "jpg":
        case "jpeg":
        case "png":
        case "gif":
        case "webp":
        case "svg":
          setType("image");
          break;
        case "mp4":
        case "webm":
        case "mov":
        case "avi":
        case "mkv":
          setType("video");
          break;
        case "txt":
        case "md":
        case "js":
        case "ts":
        case "html":
        case "css":

          setType("text");
          break;
        case "dmat":
        case "pmat":
        case "vjson":
        case "json": setType("json"); break;
        case "obj":
        case "fbx":
        case "glb":
        case "gltf":
        case "3dm":
          setType("3d");
          break;
        case "hdr":
        case "exr":
          setType("hdr");
          break
        default:
          setType("unknown");
      }
    } else {
      setType("unknown")
    }
  }, [file]);

  const renderPreview = useMemo(() => {
    if (!file || !file.id) return null;
    switch (type) {
      case "image":
        return (
          <div className="flex md:h-full h-[30%]">
            <div className="flex-1 relative">
              <img
                src={api?.getDownloadUrl(file!)}
                alt={file?.name}
                className="w-full h-full object-contain"
                onLoad={() => {
                  setLoading(false);
                  setError(false);
                }}
                onError={() => {
                  setError(true);
                  setLoading(false);
                }}
              />
            </div>
          </div>
        );
      case "video":
        {
          setLoading(false);
          return <video src={api?.getDownloadUrl(file!)} controls autoPlay className="w-full h-full object-contain" />;
        }
      case "text":
      case "json": return <JsonViewer file={file} setLoading={setLoading} setError={setError} />;
      case "3d":
        return <MiniViewer file={file} setError={setError} setLoading={setLoading} offsetConfigurator={true} />
      case "hdr":
        return <HDRViewer file={file} setLoading={setLoading} setError={setError} />
      default:
        return <div className="w-full h-full flex justify-center items-center">{file?.name}</div>;
    }
  }, [file, type, api])

  return (
    <div className="h-full w-full relative bg-white md:overflow-hidden flex flex-col md:flex-row overflow-y-auto">
      {/* <div className="flex-1 relative overflow-auto md:h-full h-[75%]">
        {error && (
          <div className="absolute w-full h-full flex justify-center items-center text-red-500 bg-opacity-50">
            This file can't be previewed
          </div>
        )}
        {loading && (
          <div className="absolute w-full h-full flex justify-center items-center z-10">
            <Spinner />
          </div>
        )}
        {!error && <>{renderPreview}</>}
      </div>
      <div className="md:h-full h-[25%] border-r-1">
        {file && <PreviewSidebar file={file} />}
      </div> */}
      <div className="h-full flex flex-col md:flex-row w-full">
        <div
          className={`
            ${showComments ? "h-[50%]" : "h-[calc(100%-70px)]"} 
             w-full md:h-full relative`}
        >
          <Button
            onClick={handleModelPanelToggle}
            className={`absolute top-[24px] right-unit-xl bg-[#fff9] w-11 h-10 rounded-[30px] p-0 hidden md:flex z-[1000]`}
            variant="flat"
            endContent={
                <RightArrow className={hideModalPanel ? "transform rotate-180" : ""} />
            }
          />
          {error && (
            <div className="absolute w-full h-full flex justify-center items-center text-red-500 bg-opacity-50">
              This file can't be previewed
            </div>
          )}
          {loading && (
            <div className="absolute w-full h-full flex justify-center items-center z-10">
              <Spinner />
            </div>
          )}
          {!error && <div className="w-full h-full flex-1">
            {renderPreview}
            </div>}
          </div>

          <div
            style={{ zIndex: 1000 }}
            className={`w-full 
              ${showComments ? "h-[50%]" : "h-[70px]"} md:h-full md:pt-4
              ${
                hideModalPanel
                  ? "hidden"
                  : "block md:border-l-1 border-[#CFCFCF]"
              }
              ${isBelowMd ? "-mt-4 rounded-t-2xl w-full" : "min-w-[300px] max-w-[300px] w-[300px]"} 
               flex-1 overflow-hidden bg-white shrink-0
              
            `}
          >
            {/* {file && <PreviewSidebar file={file} />} */}
            {file && <ViewerDetails
              // onClose={onClose}
              project={file}
              // profile={profile}
              handleModelPanelToggle={handleModelPanelToggle}
              // handleLikeToggle={props.handleLikeToggle}
              // liked={liked}
              // likeCount={likeCount}
              // handleSaveView={handleSaveView}
              canEdit={false}
              // handleEdit={handleEdit}
              mobileMode={isBelowMd}
              handleShowComments={handleShowComments}
              showComments={showComments}
            />}
          </div>
        </div>
    </div>
  );
}

export default ViewFile;
