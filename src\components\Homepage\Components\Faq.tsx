import { Accordion, AccordionItem } from "@nextui-org/react";
import { FC, useState } from "react";
import Minus from "../../icons/Minus";
import Plus from "../../icons/Plus";

interface FaqProps {
  className?: string;
  children?: any;
  faqs?: { question: string; answer: string }[];
}

const Faq: FC<FaqProps> = (props) => {
  const faqs = props.faqs || [
    {
      question: "Is there a trial period?",
      answer: "Yes, we offer a free trial for iJewel Drive. To get started, please contact <NAME_EMAIL>, and we will set up your account after you subscribe for the free trial.",
    },
    {
      question: "How do I cancel my plan?",
      answer:
        "To cancel your plan, please contact <NAME_EMAIL>. We'll guide you through the cancellation process and ensure a smooth transition.",
    },
    {
      question: "What payment methods can I use?",
      answer:
        "You can make payments using a credit or debit card through our Stripe payment link. For further details or assistance, feel free to contact us.",
    },
    {
      question: "How can I remove the iJewel branding?",
      answer: "To remove the iJewel branding, you will need to upgrade to a plan that supports custom branding. iJewel Viewer can be seamlessly integrated with your custom domain, allowing you to showcase 3D models under your own brand. To set up the integration, you’ll need to configure your domain settings and apply the necessary embed options. Contact us for guidance on setting it up.",
    },
    {
      question: "Can I buy more storage space on my plan?",
      answer: "Additional storage is not available as an add-on. To get more storage, you will need to upgrade to a higher plan. Please contact us for plan options.",
    },
    {
      question:
        "Can I create my own custom materials and environments in iJewel Drive?",
      answer: "Custom materials and environments can be created using iJewel Playground. You can set up your preferred metal, gem, and environment settings there and then apply them to your designs in iJewel Drive.",
    },
    {
      question:
        "Can I share my files and folders with someone who doesn’t have a iJewel Drive account?",
      answer: "Yes, you can share your files and folders with anyone, even if they don’t have a iJewel Drive account. Simply generate a shareable link and send it to them for easy access.",
    },
  ];

  // controlling the selected accordion items since nextui has a bug
  const [selectedKeys, setSelectedKeys] = useState<Set<string>>(new Set());

  const handleItemClick = (key: string) => {    
    const newKeys = new Set(selectedKeys);
    if (newKeys.has(key)) {
      newKeys.delete(key);
    } else {
      newKeys.add(key);
    }
    setSelectedKeys(newKeys);
  };

  return (
    <div className="max-w-[1000px] w-full mx-auto my-[70px] px-5">
      <h2 className="text-[42px] md:text-5xl font-semibold text-center text-[#373737] leading-[3rem]">
        FREQUENTLY ASKED <br /> QUESTIONS
      </h2>
      <div className="mt-[50px]">
        <Accordion 
          showDivider={false} 
          selectionMode="multiple"
          selectedKeys={selectedKeys}
          >  
          {
            faqs.map((faq) => (
              <AccordionItem
                classNames={{
                  title:
                    "font-sans text-xl font-semibold text-[#373737] leading-none",
                  base: "mt-5 py-[15px] px-[30px] w-full bg-[#F8F8F8] rounded-2xl",
                  trigger: "py-[10px] border-none rounded-2xl",
                }}
                key={faq.question}
                aria-label={faq.question}
                indicator={({ isOpen }) =>
                  isOpen ? <Minus className="rotate-90" /> : <Plus /> 
              }
                
                title={<span>{faq.question}</span>}
                onClick={() => handleItemClick(faq.question)}
              >
                {faq.answer}
              </AccordionItem>
            )) as any
          }
          <AccordionItem
            key="contact-us"
            aria-label="Have another question?"
            title="Have another question?"
            classNames={{
              title: "font-sans text-xl font-semibold leading-none",
              base: "mt-5 py-[15px] px-[30px] w-full bg-[#F8F8F8] rounded-2xl",
              trigger: "py-[10px] border-none rounded-2xl",
            }}
            indicator={({ isOpen }) =>
              isOpen ? <Minus className="rotate-90" /> : <Plus />
            }
            onClick={() => handleItemClick("contact-us")}
          >
            Need help finding the right solution? Contact us at{" "}
            <a href="mailto:<EMAIL>" className="text-[#3662FF]">
              <EMAIL>
            </a>
          </AccordionItem>
        </Accordion>
      </div>
    </div>
  );
};

export { Faq };
