import { useEffect, useState } from "react";
import { debounce } from "../components/shared/util";

const breakpoints = {
  xs: 0,
  xsmsm: 320,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  "2xl": 1536,
  "3xl": 1800,
};

type BreakpointKey = keyof typeof breakpoints;

export default function useWindowSize(breakpoint: BreakpointKey) {
  const [windowSize, setWindowSize] = useState<{
    width: number;
    height: number;
  }>({
    width: typeof window !== "undefined" ? window.innerWidth : 0,
    height: typeof window !== "undefined" ? window.innerHeight : 0,
  });
  const [isBelowBreakpointW, setIsBelowBreakpointW] = useState(false);
  const [isBelowBreakpointH, setIsBelowBreakpointH] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      const isBelowW = width < breakpoints[breakpoint];
      const isBelowH = height < breakpoints[breakpoint];

      if (width !== windowSize.width || isBelowW !== isBelowBreakpointW) {
        setWindowSize({ width, height });
        setIsBelowBreakpointW(isBelowW);
      }
      if (width !== windowSize.height || isBelowH !== isBelowBreakpointH) {
        setWindowSize({ width, height });
        setIsBelowBreakpointH(isBelowH);
      }
    };

    handleResize();
    const debouncedHandleResize = debounce(handleResize, 300);

    window.addEventListener("resize", debouncedHandleResize);
    return () => window.removeEventListener("resize", debouncedHandleResize);
  }, [
    breakpoint,
    windowSize.width,
    windowSize.height,
    isBelowBreakpointW,
    isBelowBreakpointH,
  ]);

  return {
    windowSize,
    isBelowBreakpoint: isBelowBreakpointW,
    isBelowBreakpointH,
  };
}
