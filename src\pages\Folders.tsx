import { ChonkyActions } from "chonky";
import { useCustomFileMap, useFileActionHandler, useFiles, useFolder<PERSON>hain } from "../hooks/fileHooks";
import { useMemo } from "react";
import DropZone from "../components/DropZone";
import { useBrowser } from "../provider/BrowserProvider";
import { UploadProgress } from "../components/upload/UploadProgress";
import { Header } from "../components/Header";
import { Sidebar } from "../components/Sidebar";
import HomeView from "../components/HomeView";
import { AddNewButton } from "../components/AddNewButton";
import { FileBrowserWithInfo } from "../components/FileBrowserWithInfo";
import SettingsView from "../components/SettingsView";
import { ViewTypes, ViewType } from "../types/views";

function Folders() {
  const {
    deleteFiles,
    duplicateFile,
    moveFiles,
    createFolder,
    uploadFiles,
    downloadFiles,
    getFile,
    updateFilePermissions,
    addToRecentFiles,
    CustomDownloadAction,
    CustomViewAction,
    CustomEditAction,
    CustomEditTagsAction,
    CustomRenameAction,
    CustomShareAction,
    CustomOpenFolderAction,
    customLockFileAction,
    customUnlockFileAction,
    customDeleteAction,
    customOverrideTagsAction,
    customModifyLayersAction,
    customSetCategoryAction,
    CustomMakeDefaultAction,
    CustomMakeFreeAction,
    CustomMakePremiumAction,
    CustomUpdateThumbnailAction,
    customEditFileInfoAction,
    customDuplicateAction,
    CustomOpenInPlaygroundAction,
    CustomCopyDownloadLinkAction,
    CustomCopyFileIdAction,
    CustomCreateFolder,
    CustomPermanentDeleteAction,
    updatePremium,
    updateTags,
    renameFile,
    toggleFileLock,
    updateLayers,
    updateCategory,
    updateDefault,
    updateThumbnail,
    customRestoreAction,
    restoreFiles,
    CustomFileInfoAction,
    CustomEditDefaultSettingsAction,
    CustomExportCsvAction,
    customClear3dAction,
    clear3DConfig
  } = useCustomFileMap();
  const files = useFiles();
  const folderChain = useFolderChain(getFile);
  const handleFileAction = useFileActionHandler(
    deleteFiles,
    moveFiles,
    createFolder,
    downloadFiles,
    updateTags,
    renameFile,
    updateFilePermissions,
    toggleFileLock,
    updateLayers,
    updateCategory,
    updateDefault,
    updatePremium,
    updateThumbnail,
    duplicateFile,
    addToRecentFiles,
    restoreFiles,
    clear3DConfig
  );
  const { fileQueue, currentView, setCurrentView } = useBrowser();

  const fileActions = useMemo(
    () => {
      const actions = [
        ChonkyActions.CreateFolder,
        CustomDownloadAction,
        CustomViewAction,
        CustomShareAction,
        CustomEditAction,
        CustomRenameAction,
        CustomEditTagsAction,
        CustomOpenFolderAction,
        customLockFileAction,
        customUnlockFileAction,
        customDeleteAction,
        customOverrideTagsAction,
        customModifyLayersAction,
        customSetCategoryAction,
        CustomMakeDefaultAction,
        CustomMakeFreeAction,
        CustomMakePremiumAction,
        CustomUpdateThumbnailAction,
        customDuplicateAction,
        customEditFileInfoAction,
        customRestoreAction,
        CustomFileInfoAction,
        CustomEditDefaultSettingsAction,
        CustomExportCsvAction,
        CustomCopyDownloadLinkAction,
        CustomCopyFileIdAction,
        CustomCreateFolder,
        customClear3dAction,
        CustomOpenInPlaygroundAction,
        CustomPermanentDeleteAction
      ];

      if (currentView === 'trash') {
        return actions.filter(action =>
          [
            customRestoreAction.id,
            CustomViewAction.id,
            CustomOpenFolderAction.id,
            CustomPermanentDeleteAction.id,
          ].includes(action.id)
        );
      }

      return actions;
    },
    [
      CustomDownloadAction,
      CustomViewAction,
      CustomEditAction,
      CustomEditTagsAction,
      CustomRenameAction,
      CustomShareAction,
      CustomOpenFolderAction,
      customLockFileAction,
      customUnlockFileAction,
      customDeleteAction,
      customOverrideTagsAction,
      customModifyLayersAction,
      customSetCategoryAction,
      CustomMakeDefaultAction,
      CustomMakeFreeAction,
      CustomMakePremiumAction,
      CustomUpdateThumbnailAction,
      customDuplicateAction,
      customEditFileInfoAction,
      customRestoreAction,
      currentView,
      CustomFileInfoAction,
      CustomEditDefaultSettingsAction,
      CustomExportCsvAction,
      customClear3dAction,
      CustomOpenInPlaygroundAction,
      CustomCopyDownloadLinkAction,
      CustomCopyFileIdAction,
      CustomCreateFolder,
      CustomPermanentDeleteAction
    ]
  );

  const fileListSlots = useMemo(() => ({
    EmptyComponent: () => (
      <AddNewButton
        onUpload={(files, options) => uploadFiles(files, options || {})}
      />
    )
  }), [uploadFiles]);


  const handleViewChange = (view: ViewType) => {
    setCurrentView(view);
  };

  // Memoize view components
  const memoizedHomeView = useMemo(() => <HomeView />, []);
  const memoizedSettingsView = useMemo(() => <SettingsView />, []);
  const memoizedFileBrowser = useMemo(() => (
    <DropZone onDrop={(files: { file: File, path: string }[], options?: any) => uploadFiles(files, options || {})}>
                <div className="h-full">
                  <FileBrowserWithInfo
                    files={files}
                    folderChain={folderChain}
                    fileActions={fileActions}
                    onFileAction={handleFileAction}
                    updateTags={updateTags}
                    slots={fileListSlots}
                    clearSelectionOnOutsideClick={true}
                  />
                </div>
      </DropZone>
  ), [files, folderChain, fileActions, handleFileAction, updateTags, fileListSlots, uploadFiles]);

  return (
    <div className="flex flex-col h-screen bg-[#F4F4F4]">
      <Header onViewChange={handleViewChange} currentView={currentView}/>
      <div className="flex flex-1 overflow-hidden md:p-2">
        {fileQueue.length > 0 && <UploadProgress />}
        <Sidebar onViewChange={handleViewChange} currentView={currentView} />
        <div className="flex-1 flex flex-col overflow-hidden">
          {currentView === ViewTypes.HOME ? (
            memoizedHomeView
          ) : currentView === ViewTypes.SETTINGS ? (
            memoizedSettingsView
          ) : (
            memoizedFileBrowser
          )}
        </div>
      </div>
    </div>
  );
}

export default Folders;
