import { useCallback, useEffect, useRef, useState } from "react";
import { useBrowser } from "../provider/BrowserProvider";
import { useModal } from "../provider/useModal";
import { Dropzone } from "../utils/dropzone";
import toast from "react-hot-toast";
import { Button } from "./Button.tsx";
import UploadButton from "./icons/UploadButton.tsx";
import { is3dFile } from "./shared/util.ts";

function DropZone({ children , onDrop }: { children: any , onDrop : any}) {

  const [dropping, setDropping] = useState(false);
  const { uploadOptions , setUploadOptions} = useBrowser();
  const {openUploadPrompt} = useModal();
  const dropAreaRef = useRef<HTMLDivElement>(null);
  const dropzoneRef = useRef<Dropzone | null>(null);

  function handleDragEnter(e : any) {
    // make sure we're dragging a file
    if(!e.dataTransfer.types.includes("Files")) return;
    const dt = e && e.dataTransfer;
    const isFile = dt && dt.types && dt.types.length == 1 && dt.types[0] == "Files";
    if (isFile) {
      setDropping(true);
    }
  }

  function handleDragLeave(e : any) {
    const area = dropAreaRef.current;
    if (!area) return;
    const rect = area.getBoundingClientRect();
    if (e.pageX < rect.left || e.pageX >= rect.right || e.pageY < rect.top || e.pageY >= rect.bottom) {
      setDropping(false);
    }
  }

  const handleDrop = useCallback(async (files: { file: File, path: string }[]) => {
    
    let options = uploadOptions;
    
    const has3dmFile = is3dFile(files.map(file => file.file)) && files.some(file => file.file.name.endsWith('.3dm'));
    const has3dFile = is3dFile(files.map(file => file.file));              
    
    if (has3dFile) {
      options = await openUploadPrompt(uploadOptions!, has3dmFile);
      if(options === null) return;
      setUploadOptions({...options});
    }

    onDrop(files, options);
  }, [onDrop, openUploadPrompt, setUploadOptions, uploadOptions]);

  const handleDropRef = useRef(handleDrop);
  useEffect(() => {
    handleDropRef.current = handleDrop;
  }, [handleDrop]);

  useEffect(() => {
    if (dropAreaRef.current && !dropzoneRef.current) {
      dropzoneRef.current = new Dropzone(dropAreaRef.current, undefined);

      dropzoneRef.current.on("dropstart", () => {
        // if(draggingRef.current) return;
        setDropping(true);
      });

      dropzoneRef.current.on("drop", (data) => {
        // if(draggingRef.current) return;
        setDropping(false);
        if (!data || !data.files) return;

        const filesArray = Array.from(data.files.values()).map((file) => ({
          file,
          path: file.filePath,
        }));

        handleDropRef.current(filesArray);
      });

      dropzoneRef.current.on("droperror", (err) => {
        toast.error("Error during drop");
        setDropping(false);
        console.error(err?.message || "Error during drop");
      });
    }

    return () => {
      dropzoneRef.current?.destroy();
      dropzoneRef.current = null;
    };
  }, []);

  return (
    <div ref={dropAreaRef} className="w-full h-full relative" 
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      // onDragOver={(e) => e.preventDefault()}
      // onMouseLeave={handleDragLeave}
      // onDragEnd={handleDragLeave}
      // onDrop={handleDrop}
    >
      {children}
      {dropping && (
        <div className="absolute inset-0 bg-[#F0F1FF] border-2 border-[#6E72F2] rounded-2xl bg-opacity-20 flex justify-center items-end z-50">
          <Button
              name={
                <div className="flex items-center gap-1 justify-center">
                  <UploadButton className="w-5 h-5"/>
                  <span className="text-sm">Drop to upload into this folder</span>
                </div>
              }
              className="mb-8 h-14 bg-white drop-shadow-md rounded-3xl w-1/3"
              size="sm"
              varient={"shadow"}
              // onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          />
        </div>
      )}
    </div>
  );
}

export default DropZone;
