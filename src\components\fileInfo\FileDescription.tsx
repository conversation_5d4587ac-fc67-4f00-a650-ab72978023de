import { useCallback, useEffect, useState } from 'react'
import { CustomFileData } from '../../api/api';
import { useCustomFileMap } from '../../hooks/fileHooks';
import toast from 'react-hot-toast';
import Edit from '../icons/Edit';
import RichTextEditor from './RichTextEditor';
import { useLexicalEditorHtml } from 'ijewelTextEditor';
import { useUser } from '../../provider/UserProvider';

function FileDescription({ file }: { file: CustomFileData }) {
    const [changedescription, setChangeDescription] = useState<boolean>(false);
    const { api } = useUser();
    const { updateDescription } = useCustomFileMap();
    const {
        htmlContent: richTxtDescription,
        setEditorState: setEditorStateHtml,
      } = useLexicalEditorHtml();

      const [editorInfo, setEditorInfo] = useState({
        name: file.config ? (file.config.name ?? file.name) : file.name,
        description: file.config ? (file.config.description ?? "") : "",
        textEditorState: file.config ? (file.config.textEditorState ?? {}) : {},
      });
      
      useEffect(() => {
        setEditorInfo({
          name: file.config ? (file.config.name ?? file.name) : file.name,
          description: file.config ? (file.config.description ?? "") : "",
          textEditorState: file.config ? (file.config.textEditorState ?? {}) : {},
        });
      }, [file]);
    
      const renderDescriptionRichText = useCallback((editorJsonState: any) => {
        if(!editorJsonState || !editorJsonState.root) {
          // setEditorStateHtml({ root : { children: [] }});
          return;
        }
        const json = JSON.parse(JSON.stringify(editorJsonState));
        json.root?.children?.shift();
        setEditorStateHtml(json);
      }, [setEditorStateHtml]);
      
      const updateEdtInfo = (newInfo: {
        name: string;
        description: string;
        textEditorState: any;
      }) => {
        setEditorInfo((p) => ({ ...p, ...newInfo }));
        if (newInfo.textEditorState) {
          renderDescriptionRichText(newInfo.textEditorState);
        }
      };

      useEffect(()=>{
        editorInfo.textEditorState && renderDescriptionRichText(editorInfo.textEditorState);
      },[editorInfo.textEditorState, file, renderDescriptionRichText])

    const handleSaveDescription = async (data:any) => {
        updateEdtInfo({
          name: data.name,
          description: data.description,
          textEditorState: data.textEditorState,
        });
        try {
            await updateDescription(file, {...file.config, description: data.description, textEditorState: data.textEditorState, name: data.name});
            setChangeDescription(false);
        } catch (error) {
            console.log(error)
            toast.error("Failed to save Description", { id: "updateDescription" });
        }
    }
    return (
        <div>
            <div className='flex gap-2 items-center' onClick={(e) => { e.preventDefault(); {api?.user && setChangeDescription(true)} }}><label className="text-gray-500 text-sm block mb-1">Title and Description</label>{api?.user && <Edit className='w-3'/>}</div>
            {!changedescription && editorInfo.name && (
              <div className="relative break-words">
                <span className="font-semibold text-lg leading-tight">
                  {editorInfo.name}
                </span>
              </div>)
            }
            {(!changedescription) ? <>{(richTxtDescription || editorInfo.description) &&
                (richTxtDescription ? (
                  <div
                    className="text-sm break-words"
                    dangerouslySetInnerHTML={{ __html: file.config?.description ? richTxtDescription : "" }}
                  />
                ) : (
                  <div className="text-sm">{editorInfo.description}</div>
                ))}</>
                :
                <>
                    <RichTextEditor
                        project={{
                                    name: editorInfo.name,
                                    description: editorInfo.description,
                                    project_data: {
                                        textEditorState: editorInfo.textEditorState,
                                    },}
                        }
                        onCancel={()=>{setChangeDescription(false)}}
                        onChange={handleSaveDescription}
                    >
                    </RichTextEditor>
                </>
            }
        </div>
    )
}

export default FileDescription
