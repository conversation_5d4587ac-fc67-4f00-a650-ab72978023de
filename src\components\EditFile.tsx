import { useCallback, useEffect, useState } from "react";
import { useUser } from "../provider/UserProvider";
import { Spinner } from "@nextui-org/react";
import { CustomFileData } from "../api/api";
import MiniEditor from "./MiniEditor";
import JsonEditor from "./JsonEditor";
import toast from "react-hot-toast";
import { useBrowser } from "../provider/BrowserProvider";
import { currentAssetsVersion, FILE_CONFIG_3D_PROPERTIES } from "./shared/variables";



type PreviewType = "image" | "video" | "text" | "3d" | 'json' |"unknown";

interface EditFileProps {
  file: CustomFileData | null;
  setFile: (file: CustomFileData | null) => void;
  isPlayground?: boolean;
  saveCallbackRef: React.MutableRefObject<((data: any) => Promise<void>) | undefined>;
}

function EditFile({ file , setFile , isPlayground , saveCallbackRef }: EditFileProps) {
  const { api } = useUser();
  const [type, setType] = useState<PreviewType>("unknown");
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<boolean>(false);
  const [defaultSettingsMode, setDefaultSettingsMode] = useState<boolean>(false);
  const {viewer} = useBrowser();

  useEffect(() => {
    setLoading(true);
    setError(false);
  }, []);


  useEffect(() => {
    if (file) {
      if(file.isDir) {
        setDefaultSettingsMode(true);
        setType("3d")
        return;
      }
      switch (file.file?.split(".").pop()?.toLowerCase()) {
        case "jpg":
        case "jpeg":
        case "png":
        case "gif":
        case "webp":
        case "svg":
          setType("image");
          break;
        case "mp4":
        case "webm":
        case "mov":
        case "avi":
        case "mkv":
          setType("video");
          break;
        case "txt":
        case "md":
          case "js":
            case "ts":
              case "html":
                case "css":
                  setType("text");
                  break;
        case "json": setType("json"); break;
        case "obj":
        case "fbx":
        case "glb":
        case "gltf":
        case "3dm":
          setType("3d");
          break;
        default:
          setType("unknown");
      }
    }
  }, [file]);

  const save3D = useCallback(async (data : any) => {
    if ( !api || !file ) return;
    toast.loading("Saving project", {id : "loading"});

    let newFile = undefined;

    const editorPlugin = viewer?.getPluginByType("IjewelEditorPlugin") as any
    if(!editorPlugin && !data){
      console.error("IjewelEditorPlugin not found");
      return;
    }
    if(editorPlugin) editorPlugin.hasEdited = false;
    

    const config = data || editorPlugin?.getConfig(true);
    if (!config) {
      console.error("IjewelEditorPlugin config not found");
      return;
    }

    let project_data = config;

    const tags = project_data.tags?.join(",") ?? ""
    delete project_data.tags;

    let thumb = undefined;
    if (project_data.posterUrl?.startsWith("data:image")) {
      //convert to file
      const blob = await fetch(project_data.posterUrl).then((r) => r.blob());
      thumb = new File([blob], "poster.png", { type: "image/png" });

      delete project_data.posterUrl
    }

    if (defaultSettingsMode){
      project_data.tags && delete project_data.tags;
      project_data.modelUrl && delete project_data.modelUrl;
      project_data.name && delete project_data.name;
      
      //add 3d settings 
      const sceneSettings : any = {}
      FILE_CONFIG_3D_PROPERTIES.forEach((key) => {
        if (project_data[key]) sceneSettings[key] = project_data[key];
      })
      
      project_data = {
        ...(file.config ?? {}),
        version: project_data.version ?? currentAssetsVersion,
        isDefault: true,
        ...sceneSettings,
      }
    }else if(isPlayground){
      //remove 3d settings and save the glb file
      FILE_CONFIG_3D_PROPERTIES.forEach((key) => {
        if (project_data[key]) delete project_data[key];
      })

      project_data.glbUpdated = true;
      project_data.isPlayground = true;

      const blob = await editorPlugin.export(true, true, false);
      const fileName = (file.name?.split(".").slice(0, -1).join(".")) + ".glb";
      newFile = new File([blob], fileName, { type: "model/gltf-binary" });
    }

    const updatedFile : any = {
      id : file.id,
      config: {...project_data},
      tags,
    }
    if(newFile) {
      updatedFile.file = newFile;
    }
    try{
      await api.updateFile(
        updatedFile,
        thumb
      );
      toast.success("Project saved", {id : "loading"});
    }catch(e){
      console.log(e);
      toast.error("Error saving project" , {id : "loading"});
    }
    
  } , [api, file, viewer, defaultSettingsMode, isPlayground]);



  const renderPreview = useCallback(() => {
    if (!file || !type || type == "unknown") return null;
    switch (type) {
      case "3d": return <MiniEditor isPlayground={isPlayground} saveCallbackRef={saveCallbackRef} defaultSettingsMode={defaultSettingsMode} file={{ ...file, url: api?.getDownloadUrl(file) }} setFile={setFile}/>;
      case "json": {
        return <JsonEditor file={file} setLoading={setLoading}/>;
      }
    }
    setError(true);
    if(loading) setLoading(false);
  }, [file, type, loading, isPlayground, saveCallbackRef, defaultSettingsMode, api, setFile]);


  useEffect(() => {
    switch (type) {
      case "3d":
        saveCallbackRef.current = save3D;
        break;
      case "json":
        saveCallbackRef.current = undefined // todo refactor the method from the json editor here
        break;
      default:
        saveCallbackRef.current = undefined
    }
  }, [type, save3D, saveCallbackRef]);

  return (
    <div className="h-full w-full flex bg-white relative overflow-hidden">
      {error && (
        <div className="absolute w-full h-full flex justify-center items-center text-red-500 bg-opacity-50 overflow-hidden ">
          This file can's be edited
        </div>
      )}
      {loading && (
        <div className="absolute w-full h-full flex justify-center items-center">
          <Spinner />
        </div>
      )}
      {!error && file && renderPreview()}
    </div>
  );
}

export default EditFile;
