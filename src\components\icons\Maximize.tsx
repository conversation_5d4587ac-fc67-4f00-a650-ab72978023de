import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <mask id="mask0_133_630" style={{maskType: "alpha",}} maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
            <rect x="20" width="20" height="20" transform="rotate(90 20 0)" fill="#D9D9D9"/>
        </mask>
        <g mask="url(#mask0_133_630)">
            <path d="M2.91683 2.91667L7.50016 2.91667L7.50016 4.16667L5.04495 4.16667L7.83662 6.95834L6.95849 7.83646L4.16683 5.0448L4.16683 7.50001L2.91683 7.50001L2.91683 2.91667ZM2.91683 12.5L4.16683 12.5L4.16683 14.9552L6.95849 12.1635L7.83662 13.0417L5.04495 15.8333L7.50016 15.8333L7.50016 17.0833L2.91683 17.0833L2.91683 12.5ZM12.1637 6.95834L14.9554 4.16667L12.5002 4.16667L12.5002 2.91667L17.0835 2.91667L17.0835 7.50001L15.8335 7.50001L15.8335 5.0448L13.0418 7.83646L12.1637 6.95834ZM12.1637 13.0417L13.0418 12.1635L15.8335 14.9552L15.8335 12.5L17.0835 12.5L17.0835 17.0833L12.5002 17.0833L12.5002 15.8333L14.9554 15.8333L12.1637 13.0417Z" fill="#373737"/>
        </g>
    </svg>
)
export default SvgComponent

