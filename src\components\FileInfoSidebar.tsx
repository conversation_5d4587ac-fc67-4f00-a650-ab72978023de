import React, { FC, useEffect, useState } from 'react';
import { CustomFileData } from '../api/api';
import { Button } from './Button';
import Close from './icons/Close';
import InfoBarDown from './icons/InfoBarDown';
import Info from "./icons/Info.tsx";
import { useModal } from '../provider/useModal';
import Edit from './icons/Edit.tsx';
import FileNote from './fileInfo/FileNote.tsx';
import { useUser } from '../provider/UserProvider.ts';
import FileDescription from './fileInfo/FileDescription.tsx';

interface FileInfoSidebarProps {
  file: CustomFileData;
  onClose: () => void;
  updateTags?: (file: CustomFileData, tags: string) => Promise<void>;
}

export const formatDate = (date: string | Date | undefined) => {
  if (!date) return 'Unknown';
  const d = date instanceof Date ? date : new Date(date);
  
  const onlyDate = d.getDate().toString().padStart(2, '0');
  const month = d.toLocaleString('default', { month: 'short' });
  const year = d.getFullYear().toString().slice(-2);
  let hours = d.getHours();
  const minutes = d.getMinutes().toString().padStart(2, '0');
  const ampm = hours >= 12 ? 'PM' : 'AM';
  hours = hours % 12;
  hours = hours ? hours : 12;
  
  return `${onlyDate} ${month} ${year}, ${hours}:${minutes}${ampm}`;
};

export const FileInfoSidebar: FC<FileInfoSidebarProps> = ({ file, onClose, updateTags }) => {
  const [showDetails, setShowDetails] = useState(true);
  const [showNotes, setShowNotes] = useState(file.notes ? true : false);
  const [showTags, setShowTags] = useState(false);
  const [userDetail, setUserDetail] = useState<any | undefined>();
  const [fileDetail, setFileDetail] = useState<CustomFileData | undefined>();
  const { openModal } = useModal();
  const {api} = useUser();
  
  const handleEditTags = () => {
    if (updateTags) {
      openModal("EDIT_TAGS", { file, updateTags });
    }
  };

  useEffect(()=>{
    async function fetchUserInfo() {
      if (!api || !file.created_by) return;
      setShowNotes(false);
      setShowTags(false);
      try {
        const [userResponse, fileDetailResponse] = await Promise.all([
          api.viewUserName(file.created_by),
          api.getFileInfo(file.id)
        ]);
      
        if (userResponse.error) {
          console.log("Error fetching user", userResponse.error);
          return;
        }
      
        if (fileDetailResponse.error || fileDetailResponse.data == null) {
          console.log("Error Fetching File Detail", fileDetailResponse.error);
          return;
        }
      
        setUserDetail(userResponse.data);
        setFileDetail(fileDetailResponse.data);
        setShowNotes(!!fileDetailResponse.data.notes);
      } catch (error) {
        console.log("Unexpected error:", error);
      }
      setShowDetails(true);
    }
    fetchUserInfo();
  },[api, file])

  //stop any click events from getting out of the component. so it doesn't deselect the file in the file browser
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };
  
  return (
    <div onClick={handleClick} className="md:w-80 md:bg-white flex flex-col h-full rounded-3xl bg-[#F6F6F6] max-h-[70vh] md:max-h-full overflow-y-auto">
      <div className="flex items-center justify-between p-6">
        <div className="flex items-center gap-2">
          <span className="text-gray-600">
            <Info className="w-4 h-4" />
          </span>
          <h2 className="text-base font-medium">{file.name}</h2>
        </div>
        <Button
          name={<Close/>}
          varient="light"
          onClick={onClose}
          size="sm"
          className="w-5 h-5"
        />
      </div>
      <div className="border-b border-gray-200"/>
      
      <div className="flex-1 overflow-y-auto bg-white">
        <div className="px-6">
          <div className="space-y-2">
            <div className="border-b border-gray-200">
              <button 
                className="w-full flex items-center justify-between py-4"
                onClick={() => setShowDetails(!showDetails)}
              >
                <span className="text-base font-medium">Details</span>
                <InfoBarDown 
                  className={`w-4 h-4 transform transition-transform ${showDetails ? '-rotate-90' : ''}`}
                />
              </button>
              {showDetails && (
                <div className="pb-6 space-y-4">
                  {fileDetail && <div>
                    <FileDescription file={fileDetail}/>
                  </div>}
                  <div>
                    <label className="text-gray-500 text-sm block mb-1">Type</label>
                    <p className="text-black text-sm">{`${file.file?.split('.').pop()?.toUpperCase()} File`}</p>
                  </div>
                  <div>
                    <label className="text-gray-500 text-sm block mb-1">Uploader</label>
                    <p className="text-black text-sm">{ (userDetail && userDetail?.name) || 'Unknown'}</p>
                  </div>
                  <div>
                    <label className="text-gray-500 text-sm block mb-1">Created</label>
                    <p className="text-black text-sm">{formatDate(file.updated)}</p>
                  </div>
                  <div>
                    <label className="text-gray-500 text-sm block mb-1">Size</label>
                    <p className="text-black text-sm">{file.size ? `${(file.size / (1024 * 1024)).toFixed(1)} MB` : 'Unknown'}</p>
                  </div>
                </div>
              )}
            </div>

            {/* Notes Section */}
            <div className="border-b border-gray-200">
              <button 
                className="w-full flex items-center justify-between py-4"
                onClick={() => setShowNotes(!showNotes)}
              >
                <span className="text-base font-medium">Notes</span>
                <InfoBarDown 
                  className={`w-4 h-4 transform transition-transform ${showNotes ? '-rotate-90' : ''}`}
                />
              </button>
              {showNotes && fileDetail && (
                <FileNote file={fileDetail}/>
              )}
            </div>

            {/* Tags Section */}
            <div>
              <div className="flex items-center justify-between">
                <button 
                  className="w-full flex items-center justify-between py-4"
                  onClick={() => setShowTags(!showTags)}
                >
                  <span className="text-base font-medium">Tags</span>
                  <InfoBarDown 
                    className={`w-4 h-4 transform transition-transform ${showTags ? '-rotate-90' : ''}`}
                  />
                </button>
              </div>
              {showTags && (
                <div className="pb-6">
                  <div className="flex flex-wrap gap-2">
                    {file.tags ? (
                      <>
                        {file.tags.split(',').map((tag, index) => (
                          <span 
                            key={index}
                            className="px-3 py-1 bg-gray-100 rounded-full text-sm"
                          >
                            {tag.trim()}
                          </span>
                        ))}
                        {updateTags && (
                          <Button
                            onClick={handleEditTags}
                            size="sm"
                            varient="light"
                            name={<Edit className="w-4 h-4" />}
                            className="px-3 py-1 bg-gray-100 rounded-full min-w-unit-8 h-8"
                          />
                        )}
                      </>
                    ) : (
                      updateTags && (
                        <Button
                          onClick={handleEditTags}
                          size="sm"
                          varient="light"
                          name={<Edit className="w-4 h-4" />}
                          className="px-3 py-1 bg-gray-100 rounded-full min-w-unit-8 h-8"
                        />
                      )
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};