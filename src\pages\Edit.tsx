import {  useEffect, useRef, useState } from "react";
import { CustomFileData } from "../api/api";
import { useLocation, useParams } from "react-router-dom";
import { useUser } from "../provider/UserProvider";
import EditFile from "../components/EditFile";
import toast from "react-hot-toast";
import { Header } from "../components/Header";
import { SaveButton } from "../components/SaveButton";


function Edit({ isPlayground } : {isPlayground ?: boolean}) {
  const {fileId} = useParams();
  const {api, isLogin , can , config} = useUser();
  const [file, setFile] = useState<CustomFileData | null>(null);
  const {state} = useLocation();
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(true);
  const saveCallbackRef = useRef<(() => Promise<void>) | undefined>(undefined);
  const [isCustomEditor, setIsCustomEditor] = useState(false);
  
  useEffect(() => {
    //delete the state when refresh
    window.onbeforeunload = () => {
      window.history.replaceState({} , "");
    };
  }, []);


  useEffect(() => {
    if(config && config["editor-path"] && !isCustomEditor) {
      setIsCustomEditor(true);
    }
  } , [config , isCustomEditor]);


  useEffect(() => {
    if(!api) return;
    if(state && state.file) {
      setFile(state.file);
      setLoading(false);
      return;
    }
    if(fileId) {
      api.viewFile(fileId).then((data) => {
        if(!data.data  || data.error) {
          setLoading(false);
          toast.error("Failed to load file");
          if(isLogin){
            setError("File might have been deleted or you don't have permission to edit, please contact the owner");
          }else{
            setError("Please login to edit this file");
          }
          return;
        }
        setLoading(false);
        setFile( prev => prev?.name === data.data?.name  ? prev : data.data);
      });
    }
  }, [fileId, api, state, isLogin]);

  return (
    <div className="w-screen h-screen flex flex-col overflow-hidden bg-bg">
      <Header>
        {!isCustomEditor && <SaveButton saveCallbackRef={saveCallbackRef}/>}
      </Header>
      { 
        !isLogin ? <div className="text-center text-red-500 h-full justify-center items-center flex">Please login to edit this file</div>
        : !loading && !can("edit-file" , undefined , {file : file}) ? <div className="text-center text-red-500 h-full justify-center items-center flex">File is not found or you don't have permission to edit this file, please contact the owner</div> // to do : check permissions properly for this
        : !file ? <div className="text-center h-full justify-center items-center flex">Loading...</div>
        : error ? <div className="text-center text-red-500 h-full justify-center items-center flex">{error}</div> : <EditFile isPlayground={isPlayground} saveCallbackRef={saveCallbackRef} file={file} setFile={setFile}/>
      }
    </div>
  );
}

export default Edit;
