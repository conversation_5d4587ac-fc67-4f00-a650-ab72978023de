import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@nextui-org/react";
import React, { useMemo, useState } from "react";

interface ConfirmProps {
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
  options: { title: string };
}

const Confirm: React.FC<ConfirmProps> = ({
  message,
  onConfirm,
  onCancel,
  options,
}) => {
  return (
    <Modal
      isOpen={true}
      placement="top-center"
      backdrop="blur"
      classNames={{ wrapper: "z-[100000]", backdrop: " z-[10000]" }}
      isDismissable={false}
      closeButton={<></>}
      shadow="lg"
      className="overflow-hidden"
    >
      <ModalContent>
        {() => (
          <>
            <ModalHeader className="flex flex-col gap-1 text-small text-clip break-words pb-unit-sm">
              {options.title || "Confirmation"}
            </ModalHeader>
            <ModalBody className="overflow-hidden h-unit-3xl pt-0">
              <p>{message}</p>
            </ModalBody>
            <ModalFooter>
              <Button
                color="danger"
                onPress={onCancel}
                className="p-unit-xl"
              >
                Cancel
              </Button>
              <Button
                color="primary"
                onPress={onConfirm}
                className="p-unit-xl"
              >
                Confirm
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};

interface UseConfirmReturn {
  openConfirm: (message: string, options?: { title: string }) => Promise<boolean>;
  ConfirmComponent: React.FC;
}

const useConfirm = (): UseConfirmReturn => {
  const [visible, setVisible] = useState(false);
  const [message, setMessage] = useState("");
  const [options, setOptions] = useState<{ title: string }>({ title: "" });
  const [resolveConfirm, setResolveConfirm] = useState<(value: boolean) => void | null>();

  const openConfirm = (msg: string, options?: { title: string }): Promise<boolean> => {
    setMessage(msg);
    setOptions(options || { title: "" });
    setVisible(true);
    return new Promise<boolean>((resolve) => {
      setResolveConfirm(() => resolve);
    });
  };

  const handleResult = (result: boolean) => {
    setVisible(false);
    if (resolveConfirm) {
      resolveConfirm(result);
    }
  };

  const handleConfirm = () => {
    handleResult(true);
  };

  const handleCancel = () => {
    handleResult(false);
  };

  const ConfirmComponent: React.FC = useMemo(()=> () =>
    visible ? (
      <Confirm
        message={message}
        options={options}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
      />
    ) : null, [visible, message, options]);

  return { openConfirm, ConfirmComponent };
};

// eslint-disable-next-line react-refresh/only-export-components
export { Confirm , useConfirm };
