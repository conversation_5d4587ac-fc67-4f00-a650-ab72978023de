{"name": "ijewel-drive", "private": true, "version": "0.0.9", "type": "module", "scripts": {"cf-deploy": "[ -f dist/index.html ] && wrangler deploy", "cf-deploy-dev": "[ -f dist/index.html ] && wrangler deploy --env dev", "cf-dev": "[ -f dist/index.html ] && wrangler dev", "dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@monaco-editor/react": "^4.6.0", "@nextui-org/react": "2.2.10", "chonky": "file:./libs/chonky-2.3.17.tgz", "ijewelTextEditor": "file:./libs/ijewel-text-editor-0.0.1-dev.1.tgz", "framer-motion": "^11.0.28", "fuzzy-search": "^3.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-json-editor-viewer": "^1.0.7", "react-router-dom": "^6.23.0", "use-debounce": "^10.0.0", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1"}, "devDependencies": {"@types/fuzzy-search": "^2.1.5", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@types/webgi": "https://dist.pixotronics.com/webgi/runtime/bundle-types-0.9.19.tgz", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "postcss-multiple-tailwind": "^1.0.1", "sass": "^1.77.2", "tailwindcss": "^3.4.3", "@cloudflare/workers-types": "^4.20241011.0", "wrangler": "^3.81.0", "typescript": "^5.6.3", "vite": "^5.2.0", "vite-plugin-singlefile": "^2.0.2"}}