import BaseLink from "../BaseLink.tsx";
import { useUser } from "../../provider/UserProvider.ts";
import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { Logo } from "../Logo.tsx";

function AuthHeader({ backgroundColor = "transparent" }: { backgroundColor?: string }) {
  const [menuOpen, setMenuOpen] = useState(false);
  const location = useLocation();

  const { user } = useUser();
  return (
    <>
      <header className={`fixed top-0 left-0 right-0 z-30 ${backgroundColor} border-b-black w-screen h-16 flex items-center pl-4 md:px-4`}>
        <div className="flex items-center justify-between w-full">
          <div className="flex md:hidden">
            {menuOpen ? <img src="/close.svg" alt="" height={"14px"} width={"14px"} onClick={() => { setMenuOpen(false) }} />
                : <img src="/menu.svg" alt="" onClick={() => { setMenuOpen(true) }} />}
          </div>
          <Link to={"https://drive.ijewel3d.com/"}>
            <Logo className="h-unit-2xl w-auto ml-2 mr-2"/>
          </Link>

          <nav className="absolute left-1/2 -translate-x-1/2 hidden md:flex items-center gap-8">
            <BaseLink to="/pricing" className="text-sm text-gray-600 font-semibold">
              Plans
            </BaseLink>
            {/* <BaseLink to="" className="text-sm text-gray-600 font-semibold">
              Contacts
            </BaseLink> */}
            {/* <BaseLink to="" className="text-sm text-gray-600 font-semibold">
              Demo
            </BaseLink> */}
          </nav>
          <div className="flex-1 hidden md:block"></div>

          <div className="flex items-center gap-2">
           {!location.pathname.includes("login") && <BaseLink to={user ? "/folders" : "/"} className="text-sm px-4 py-2 rounded-full border border-[#373737] text-[#373737]">
              {user ? "My Drive" : "Log in"}
            </BaseLink>}
            {/* <BaseLink to="/signup" className="text-sm text-white px-4 py-2 rounded-full bg-[#6E72EA]">
            Sign up
          </BaseLink> */}
          </div>
        </div>

        {menuOpen &&
          <>
            <div className="fixed top-16 left-0 w-full h-[calc(100%-4rem)] bg-black bg-opacity-50 z-10" onClick={() => setMenuOpen(false)}></div>
            <div className="fixed top-16 left-0 right-0 bg-white shadow-lg z-20 md:hidden">
              <nav className="flex flex-col p-[12px]">
                <BaseLink to="/pricing" className="text-xl text-[#373737] font-semibold py-[15px] px-[18px]">
                  Plans
                </BaseLink>
                {/* <BaseLink to="" className="text-xl text-[#373737] font-semibold py-[15px] px-[18px]">
                  Contacts
                </BaseLink>
                <BaseLink to="" className="text-xl text-[#373737] font-semibold py-[15px] px-[18px]">
                  Demo
                </BaseLink> */}
              </nav></div>
          </>}
      </header>
    </>
  );
}

export default AuthHeader;