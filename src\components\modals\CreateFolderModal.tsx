import { FC, useState } from "react";
import { Modal } from "../Modal";
import { useModal } from "../../provider/useModal";
import { Button } from "../Button.tsx";
import { Input } from "@nextui-org/react";
import { useCustomFileMap } from '../../hooks/fileHooks';
import toast from "react-hot-toast";
import Close from '../icons/Close';

interface CreateFolderModalProps {
    onClose: () => void;
}

const CreateFolderModal: FC<CreateFolderModalProps> = (props) => {
    const { modalState } = useModal();
    const { createFolder } = useCustomFileMap();
    const [ folderName, setFolderName ] = useState("");

    return (
        <Modal isOpen={modalState.modalType == "CREATE_FOLDER"} onClose={props.onClose} size="md" backdrop="blur" hideCloseButton>
            {() => (
                <div className="w-full bg-white p-3 flex flex-col gap-3 rounded-lg">
                    <div className="flex items-center justify-between">
                        <h2 className="text-xl font-medium">Create a New Folder</h2>
                        <Button
                            varient="light"
                            size="sm"
                            onClick={props.onClose}
                            className="w-6 h-6"
                            name={<Close />}
                        >
                        </Button>
                    </div>
                    <div>
                        <h3 className="text-sm mb-2 text-gray-700">Name</h3>
                        <div>
                            <Input
                                value={folderName}
                                onChange={(e) => setFolderName(e.target.value)}
                                placeholder="Folder name"
                                size="md"
                                variant="bordered"
                                classNames={{
                                    inputWrapper: "h-[38px] rounded-full",
                                    input: ["text-sm", "placeholder:text-[#898989]"]
                                }}
                            />
                        </div>

                    </div>

                    <div className="flex justify-end gap-3 mt-4">
                        <Button
                            className="w-[71px] h-8 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-700"
                            name={
                                <span className="text-sm">Cancel</span>
                            }
                            onClick={props.onClose}
                        >
                        </Button>
                        <Button
                            name={
                                <span className="text-sm">Create</span>
                            }
                            className="w-[71px] h-8 rounded-full"
                            onClick={() => {
                                if (folderName === null) return;
                                if (folderName) {
                                    createFolder(folderName);
                                } else {
                                    toast.error("Name Can't be empty");
                                }
                                props.onClose();
                            }}
                            color="primary"
                        >
                        </Button>
                    </div>
                </div>
            )}
        </Modal>
    )
}

export default CreateFolderModal;