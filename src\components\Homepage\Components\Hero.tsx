import { Link, useParams } from "react-router-dom";
import { Button } from "../../Button";
import RightArrow from "../../icons/RightArrow";
import { useEffect, useState } from "react";

export default function Hero() {
  const [isDev, setIsDev] = useState<boolean>(!(import.meta as any).env?.PROD)
    const { basename } = useParams();
    useEffect(() => {
      setIsDev(isDev || window.location.hostname.includes("dev"));
    }, []);
  return (
    <div className="max-w-[1000px] mx-auto px-4 lg:px-0">
      <h1 className="text-3xl md:text-5xl font-lt font-[700] text-[#373737] text-center leading-[40px] md:leading-[67px] py-5 md:py-[60px] m-auto">
        Simplify the Way You Manage, Customize, and Share 3D Jewelry Designs
      </h1>
      <div className="flex flex-col md:flex-row items-center justify-center gap-2.5">
        <Link to={(isDev && !basename) ? "drive-dev/signup" : `${basename}/signup`}>
          <Button
            className="h-fit text-white font-medium min-h-[56px] w-[300px] md:w-fit px-[30px] rounded-full bg-gradient-to-r from-blue-500 to-purple-500"
            varient="ghost"
            name={
              <span className="flex items-center text-xl text-[600] gap-3 leading-none">
                Get started <RightArrow />
              </span>
            }
          />
        </Link>
        <Link to={"drive/pricing"}>
          <Button
            className="h-fit font-medium min-h-[56px] w-[300px] md:w-fit px-[30px] rounded-full border-1 border-[#373737]"
            name={<span className="text-xl text-[600]">Plans</span>}
            varient="ghost"
          />
        </Link>
      </div>

      <div
        className={
          "my-5 md:my-[60px] mx-auto flex flex-col-reverse md:flex-row rounded-[30px] bg-none md:bg-[linear-gradient(97.4deg,_#F0F1FF_2.04%,_#E0F6FF_100.68%)]"
        }
      >
        {/* Content */}
        <div className={"w-full md:w-1/2 p-5 md:p-[60px]"}>
          <h2 className="text-center md:text-start font-ltnormal text-[40px] md:text-[42px] font-[700] text-gray-900 leading-none">
            iJewel <span className="text-[#6E72F2]">Drive</span>
          </h2>
          <p className="mt-5 text-[#373737] text-lg md:text-[22px] font-ltlight md:max-w-[434px]">
            Manage your 3D jewelry models in one place with real-time
            customization, private hosting and secure sharing options. Perfect
            for designers, manufacturers, and retailers alike.
          </p>
        </div>
        {/* Image */}
        <div className="flex flex-row-reverse justify-center items-center rounded-[30px] relative overflow-clip md:overflow-visible md:w-1/2 md:bg-none bg-[linear-gradient(97.4deg,_#F0F1FF_2.04%,_#E0F6FF_100.68%)]">
          <img
            src="/preview.png"
            alt="3D Viewer"
            className="md:rounded-r-[30px] object-cover w-[323px] md:w-full max-w-[450px] max-h-[320px] block md:absolute bottom-0 right-0 pointer-events-none"
          />
          <img
            src="/storage.png"
            alt="3D Viewer"
            className="object-cover w-[97px] md:w-full max-w-[118px] max-h-[169px] md:absolute -bottom-[60px] -right-[40px] pointer-events-none"
          />
        </div>
      </div>
    </div>
  );
}
