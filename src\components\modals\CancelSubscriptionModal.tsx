import { FC, useState } from "react";
import { Modal } from "../Modal";
import { useModal } from "../../provider/useModal";
import { Button } from "../Button";
import Close from "../icons/Close";
import { RadioGroup, Radio } from "@nextui-org/react";
import { Input } from "@nextui-org/react";

interface CancelSubscriptionModalProps {
  onClose: () => void;
}

const CancelSubscriptionModal: FC<CancelSubscriptionModalProps> = (props) => {
  const { modalState } = useModal();
  const [password, setPassword] = useState("");
  const [cancelDate, setCancelDate] = useState("immediately");

  const handleCancel = () => {
    // TODO: Implement subscription cancellation logic
    console.log("Cancelling subscription:", { cancelDate, password });
    props.onClose();
  };

  return (
    <Modal isOpen={modalState.modalType === "CANCEL_SUBSCRIPTION"} onClose={props.onClose} size="xl" backdrop="blur" hideCloseButton>
      {() => (
        <div className="w-full bg-white p-6 flex flex-col gap-4 rounded-xl">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Cancel subscription</h2>
            <Button
              varient="light"
              size="sm"
              onClick={props.onClose}
              className="w-8 h-8 min-w-8"
              name={<Close />}
              fullWidth={false}
            />
          </div>

          <div className="space-y-4">
            <p className="text-gray-800">Are you sure you want to cancel your <span className="font-medium">Premium</span> subscription?</p>
            
            <ul className="list-disc text-gray-600 ml-5 space-y-1 text-sm">
              <li>You will lose access to all premium features</li>
              <li>Your account will be switched to the basic plan</li>
            </ul>

            <p className="text-gray-600">Please note that you have 30 days to remove any excess files. After this period, any remaining files will be deleted selectively.</p>
            
            <p className="text-gray-600">You can resubscribe anytime you want</p>

            <div className="pt-4">
              <p className="text-gray-800 font-medium mb-3">Cancel subscription</p>
              <RadioGroup
                value={cancelDate}
                onValueChange={setCancelDate}
                classNames={{
                  wrapper: "gap-3"
                }}
              >
                <Radio 
                  value="immediately"
                  classNames={{
                    base: "max-w-full",
                    wrapper: "before:border-gray-300",
                    labelWrapper: "text-gray-700"
                  }}
                >
                  Immediately 31 Aug 2024
                </Radio>
                <Radio 
                  value="end"
                  classNames={{
                    base: "max-w-full",
                    wrapper: "before:border-gray-300",
                    labelWrapper: "text-gray-700"
                  }}
                >
                  End of the current period 10 Sep 2024
                </Radio>
              </RadioGroup>
            </div>

            <div>
              <Input
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                variant="bordered"
                classNames={{
                  input: "h-11 rounded-full",
                  inputWrapper: "h-11 rounded-full"
                }}
              />
            </div>
          </div>

          <div className="flex justify-end gap-2 pt-2">
            <Button
              varient="light"
              size="sm"
              name="Cancel"
              onClick={props.onClose}
              className="px-4 py-1.5 rounded-full h-9"
              fullWidth={false}
            />
            <Button
              size="sm"
              name="Cancel subscription"
              onClick={handleCancel}
              className="px-4 py-1.5 rounded-full h-9 bg-red-500 hover:bg-red-600 text-white hover:text-white"
              fullWidth={false}
            />
          </div>
        </div>
      )}
    </Modal>
  );
};

export default CancelSubscriptionModal; 