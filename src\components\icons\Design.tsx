import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg width="18" height="18" viewBox="4 3 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path fillRule="evenodd" clipRule="evenodd" d="M13 3.5H12.6088L12.3322 3.77662L4.77662 11.3322L4.5 11.6088V12V19.5556V20.5H5.44444H20.5556H21.5V19.5556V4.44444V3.5H20.5556H13ZM6.38889 12.3912L13.3912 5.38889H19.6111V18.6111H6.38889V12.3912Z" fill="url(#paint0_linear_203_1528)"/>
        <defs>
            <linearGradient id="paint0_linear_203_1528" x1="9.3378" y1="6.74921" x2="24.6647" y2="20.8589" gradientUnits="userSpaceOnUse">
                <stop offset="0.0155496" stop-color="#81C3FF"/>
                <stop offset="0.933808" stop-color="#B150FF"/>
            </linearGradient>
        </defs>
    </svg>
)
export default SvgComponent