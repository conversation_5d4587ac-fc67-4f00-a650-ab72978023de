import { FC, useEffect, useState } from "react";
import { CustomFileData } from "../api/api";
import Plus from "./icons/Plus";
import Minus from "./icons/Minus";
import Drive from "./icons/Drive";
import Download from "./icons/Download.tsx";
import OpenIn from "./icons/OpenIn.tsx";
import Share from "./icons/Share";
import Left from "./icons/Left.tsx";
import Right from "./icons/Right.tsx";
import Maximize from "./icons/Maximize.tsx";
import Close from "./icons/Close.tsx";
import { Button } from "./Button.tsx";
import DownArrow from "./icons/DownArrow.tsx";
import { usePreviewNavigation } from "../hooks/fileHooks";
import { Dropdown, DropdownItem, DropdownMenu, DropdownTrigger, Button as NextButton } from "@nextui-org/react";
import { useParams } from "react-router-dom";

interface PreviewHeaderProps {
  files: CustomFileData[];
  initialFile: CustomFileData;
  onClose: () => void;
  onShare?: () => void;
  onDownload?: () => void;
  onZoomIn?: () => void;
  onZoomOut?: () => void;
  onFullScreen?: () => void;
  zoomLevel?: number;
  onFileChange?: (file: CustomFileData) => void;
}

export const PreviewHeader: FC<PreviewHeaderProps> = ({
  files,
  initialFile,
  onClose,
  onShare,
  onDownload,
  onZoomIn,
  onZoomOut,
  onFullScreen,
  zoomLevel = 100,
  onFileChange,
}) => {
  const {
    currentIndex,
    totalFiles,
    onNext,
    onPrevious,
    currentFile: file
  } = usePreviewNavigation(files, initialFile);
  const [is3DFile, setIs3DFile] = useState<boolean>(false);
  const [isImageFile, setIsImageFile] = useState<boolean>(false);
  const {basename} = useParams();

  useEffect(() => {
    if (file && onFileChange) {
      onFileChange(file);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [file]);

  useEffect(()=>{
    if(initialFile != null){
      setIs3DFile(['obj', 'fbx', 'gltf', 'glb', 'stl', '3ds', 'dae'].includes(
        initialFile.name.split('.').pop()?.toLowerCase() || ''
      ));

      setIsImageFile(['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(
        initialFile.name.split('.').pop()?.toLowerCase() || ''
      ));
    }
  },[initialFile])

  function handleView(){
    basename && window.open(`/${basename}/files/${initialFile.id}/view`, "_blank")
  }

  function handleEdit(){
    basename && window.open(`/${basename}/files/${initialFile.id}/edit`, "_blank")
  }

  function handlePlaygroundOpen(){
    basename && window.open(`/${basename}/files/${initialFile.id}/playground`,"_blank");
  }

  return (
    <div className="h-16 px-4 border-b border-gray-100 bg-white flex items-center md:justify-between justify-end ">
      {/* Left section */}
      <div className="items-center gap-2 hidden md:flex">
        <div className="w-8 h-8 rounded-lg bg-gray-100 flex items-center justify-center">
          <Drive className=" text-gray-600" />
        </div>
        <span className="text-sm text-gray-600">{initialFile?.name}</span>
        {/* <span className="text-sm text-gray-400 ml-2">Client1</span> */}
      </div>

      {/* Center section */}
      <div className="items-center gap-2 hidden md:flex">
        <div className="flex items-center gap-2">
          <Button
            varient="light"
            size="sm"
            onClick={onPrevious}
            disabled={currentIndex <= 1}
            name={<Left className="w-5 h-5" />}
            className="w-8 h-8"
          />
          
          <span className="text-sm text-gray-600">
            {totalFiles > 0 ? `${currentIndex} of ${totalFiles}` : 'No files'}
          </span>
          
          <Button
            varient="light"
            size="sm"
            onClick={onNext} 
            disabled={currentIndex >= totalFiles}
            name={<Right className="w-5 h-5" />}
            className="w-8 h-8"
          />
        </div>
      </div>

      {/* Right section */}
      <div className="flex items-center gap-2">

        {isImageFile && (
            <div className="flex items-center gap-2 border-r-1 pr-2">
              <div className="flex">
                <Button
                    varient="light"
                    size="sm"
                    onClick={onZoomOut}
                    name={<Minus className="w-3 h-3" />}
                    className="w-8 h-8 bg-[#F0F1FF] rounded-l-full"
                />

                <span className="text-sm text-gray-600 min-w-[40px] text-center bg-[#F0F1FF] flex items-center justify-center">
                {zoomLevel}%
              </span>

                <Button
                    varient="light"
                    size="sm"
                    onClick={onZoomIn}
                    name={<Plus className="w-3 h-3" />}
                    className="w-8 h-8 bg-[#F0F1FF] rounded-r-full"
                />
              </div>


              <Button
                  varient="light"
                  size="sm"
                  onClick={onFullScreen}
                  name={<Maximize className="w-5 h-5" />}
                  className="h-8 bg-[#F0F1FF] rounded-full"
              />
            </div>
        )}

        {/* <Button
            varient="light"
            size="sm"
            name={<PreviewHeaderDropdown className="w-5 h-5" />}
            className="w-[50px] h-8 bg-[#F0F1FF] rounded-full"
        /> */}

        <Button
            varient="light"
            size="sm"
            onClick={onDownload}
            name={
              <div className="flex items-center gap-1 md:w-32 px-[15px]">
                <Download className="w-5 h-5" />
                <span className="text-sm hidden md:block">Download</span>
              </div>
            }
            className="h-8 rounded-full bg-[#F0F1FF]"
        />

        {is3DFile && (
          <Dropdown>
            <DropdownTrigger>
              <NextButton
                 className="h-8 rounded-full bg-[#F0F1FF] w-full"
                 variant="light" 
                 size="sm"
              >
                  <div className="flex items-center gap-1 px-[15px] md:w-36">
                    <OpenIn className="w-5 h-5" />
                    <span className="text-sm hidden md:block">Open In</span>
                    <DownArrow className="w-5 h-5"/>
                  </div>
              </NextButton>
            </DropdownTrigger>
            <DropdownMenu>
              <DropdownItem key="view" onClick={(e)=>{ e.preventDefault(); handleView();}}>Viewer</DropdownItem>
              <DropdownItem key="edit" onClick={(e)=>{ e.preventDefault(); handleEdit();}}>Editor</DropdownItem>
              <DropdownItem key="playground" onClick={(e)=>{ e.preventDefault(); handlePlaygroundOpen();}}>Playground</DropdownItem>
            </DropdownMenu>
          </Dropdown>
        )}
        
        <Button
          varient="light"
          size="sm"
          onClick={onShare}
          name={
            <div className="flex items-center gap-1">
              <Share className="w-5 h-5" />
              <span className="text-sm hidden md:block">Share</span>
            </div>
          }
          className="h-8 rounded-full bg-[#F0F1FF]"
        />
        
        <Button
          varient="light"
          size="sm"
          onClick={onClose}
          name={<Close/>}
          className="w-8 h-8"
        />
      </div>
    </div>
  );
}; 