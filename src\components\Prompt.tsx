import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Input,
} from "@nextui-org/react";
import React, { useCallback, useEffect, useMemo, useState } from "react";

interface PromptProps {
  message: string;
  onSubmit: (value: string) => void;
  onCancel: () => void;
  options: {title?:string, type?:string, placeholder?:string , defaultValue?: string};
}

const Prompt: React.FC<PromptProps> = ({ message, onSubmit, onCancel,options }) => {
  const [inputValue, setInputValue] = useState(options.defaultValue || "");

  const handleSubmit = useCallback(() => {
    onSubmit(inputValue);
  }, [inputValue, onSubmit]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Enter") {
        handleSubmit();
      }
    };

    document.addEventListener("keydown", handleKeyDown);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [handleSubmit, inputValue]);

  return (
    <Modal
      isOpen={true}
      // onOpenChange={onOpenChange}
      placement="top-center"
      backdrop="blur"
      classNames={{ wrapper: "z-[100000]", backdrop: " z-[10000]" }}
      isDismissable={false}
      closeButton={<></>}
      shadow="lg"
      className="overflow-hidden"
    >
      <ModalContent>
        {() => (
          <>
            <ModalHeader className="flex flex-col gap-1 text-small text-clip break-words pb-unit-sm">
            {message?.startsWith("GLTFEncryption") ? "Access Restricted" : options.title }
              <br />
              <span className="text-sm text-default-400 font-normal mt-unit-md">
                {message?.startsWith("GLTFEncryption") ?  "This model is encrypted, please enter the password/key to continue" : message}
              </span>
            </ModalHeader>
            <ModalBody className="overflow-hidden h-unit-3xl pt-0">
              <Input
                // label="Password"
                placeholder={options.placeholder}
                type={options.type}
                autoComplete="new-password"
                variant="bordered"
                value={inputValue}
                onValueChange={setInputValue}
                classNames={{
                  inputWrapper: "h-unit-3xl",
                  label: "!mb-0",
                }}
              />
            </ModalBody>
            <ModalFooter>
              <Button
                color="danger"
                onPress={onCancel}
                className="p-unit-xl"
              > 
                Cancel
              </Button>
              <Button
                color="primary"
                onPress={handleSubmit}
                className="p-unit-xl"
              >
                Confirm
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};

interface UsePromptReturn {
  openPrompt: (message: string) => Promise<string | null>;
  PromptComponent: React.FC;
}

const usePrompt = (): UsePromptReturn => {
  const [visible, setVisible] = useState(false);
  const [message, setMessage] = useState("");
  const[options, setOptions] = useState({});
  const [resolvePrompt, setResolvePrompt] =
    useState<(value: string | null) => void | null>();

  const openPrompt = (msg: string , options?: {title?:string, type?:string, placeholder?:string, defaultValue?: string}): Promise<string | null> => {
    setMessage(msg);
    setOptions(options || {title:"", type:"", placeholder:""});
    setVisible(true);
    return new Promise<string | null>((resolve) => {
      setResolvePrompt(() => resolve);
    });
  };

  const handleResult = useCallback((result: string | null) => {
    setVisible(false);
    if (resolvePrompt) {
      resolvePrompt(result);
    }
  }, [resolvePrompt]);

  const handleSubmit = useCallback((value: string) => {
    handleResult(value);
  }, [handleResult]);

  const handleCancel = useCallback(() => {
    handleResult(null);
  }, [handleResult]);

  const PromptComponent: React.FC = useMemo(() => {
    return () =>
      visible ? (
        <Prompt
          message={message}
          options={options}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
        />
      ) : null;
  }, [visible, message, options, handleSubmit, handleCancel]);

  return { openPrompt, PromptComponent };
};

// eslint-disable-next-line react-refresh/only-export-components
export { usePrompt, Prompt };
