import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <mask id="mask0_167_362" style={{maskType: "alpha",}} maskUnits="userSpaceOnUse" x="0" y="0" width="20"
              height="20">
            <rect width="20" height="20" fill="#D9D9D9"/>
        </mask>
        <g mask="url(#mask0_167_362)">
            <path
                d="M3.5 16C3.09722 16 2.74653 15.8507 2.44792 15.5521C2.14931 15.2535 2 14.9028 2 14.5V5.5C2 5.0875 2.14931 4.73438 2.44792 4.44063C2.74653 4.14688 3.09722 4 3.5 4H8L10 6H16.5C16.9125 6 17.2656 6.14688 17.5594 6.44063C17.8531 6.73438 18 7.0875 18 7.5V14.5C18 14.9028 17.8531 15.2535 17.5594 15.5521C17.2656 15.8507 16.9125 16 16.5 16H3.5ZM3.5 14.5H16.5V7.5H9.375L7.375 5.5H3.5V14.5Z"
                fill="#373737"/>
        </g>
    </svg>
)
export default SvgComponent

