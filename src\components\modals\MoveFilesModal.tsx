import { FC } from "react";
import { Modal } from "../Modal";
import { useModal } from "../../provider/useModal";
import { Button } from "../Button.tsx";
import { useCustomFileMap } from '../../hooks/fileHooks';
import { CustomFileData } from "../../api/api.ts";
import Close from "../icons/Close";

interface MoveFilesModalProps {
  files: CustomFileData[];
  destination: CustomFileData;
  onClose: () => void;
}

const MoveFilesModal: FC<MoveFilesModalProps> = (props) => {
  const { modalState } = useModal();
  const { moveFiles } = useCustomFileMap();
  
  const handleMove = () => {
    moveFiles(props.files, props.destination.id);
    props.onClose();
  };

  const fileCount = props.files?.length || 0;
  const fileText = fileCount === 1 
    ? `"${props.files[0]?.name}"` 
    : `${fileCount} files`;
  
  const destinationName = props.destination?.name || "destination folder";

  return (
    <Modal isOpen={modalState.modalType === "MOVE_FILES"} onClose={props.onClose} size="md" backdrop="blur" hideCloseButton>
      {() => (
        <div className="w-full bg-white p-6 flex flex-col gap-4 rounded-xl">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Move files</h2>
            <Button
              varient="light"
              size="sm"
              onClick={props.onClose}
              className="w-8 h-8 min-w-8"
              name={<Close />}
              fullWidth={false}
            />
          </div>
          
          <div>
            <p className="text-gray-700">
              Are you sure you want to move {fileText} to "{destinationName}"?
            </p>
          </div>
          
          <div className="flex justify-end gap-3 mt-4">
            <Button
              className="px-4 py-1.5 rounded-full h-9"
              name="Cancel"
              onClick={props.onClose}
              fullWidth={false}
            />
            <Button
              name="Move"
              className="px-4 py-1.5 rounded-full h-9"
              onClick={handleMove}
              color="primary"
              fullWidth={false}
            />
          </div>
        </div>
      )}
    </Modal>
  );
};

export default MoveFilesModal; 