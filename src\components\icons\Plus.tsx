import { SVGProps } from "react";
import { JSX } from "react/jsx-runtime";

const Plus = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => (
  <svg
    width="15"
    height="14"
    viewBox="0 0 15 14"
    xmlns="http://www.w3.org/2000/svg"
    fill="currentColor"
    {...props}
  >
    <path
      d="M14.5 7.99787H8.48298V14H6.50213V7.99787H0.5V6.00213H6.50213V0H8.48298V6.00213H14.5V7.99787Z"
      fill="#373737"
    />
  </svg>
);
export default Plus;
