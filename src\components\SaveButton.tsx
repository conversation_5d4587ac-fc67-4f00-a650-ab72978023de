import { Button, Dropdown, DropdownItem, DropdownMenu, DropdownTrigger } from "@nextui-org/react";
import { FC, useCallback, useState } from "react";
import DownArrow from "./icons/ArrowBack"
import toast from "react-hot-toast";

interface buttonProps {
  saveCallbackRef?: React.MutableRefObject<(() => Promise<void>) | undefined>;
}

const SaveButton: FC<buttonProps> = (props) => {
  const [saving, setSaving] = useState(false);
  // const [isPosterUpdating, setIsPosterUpdating] = useState(false);
  console.log("SaveButton props", props.saveCallbackRef);
  const handleSaveClick = useCallback(async () => {
    if(saving) return;
    setSaving(true);
    if(props.saveCallbackRef?.current) {
      console.log("Saving project...", props.saveCallbackRef?.current);
      try{
        await props.saveCallbackRef?.current();

      }catch (e) {
        console.error(e);
        setSaving(false);
      }
    }
    setSaving(false);
  }, [props, saving ]);


  const handleSaveDropdownClick = useCallback(async (key: string) => {
    if(saving) return;
    const viewRout = window.location.href.split("/").slice(0, -1).join("/") + "/view";
    switch (key) {
      case "saveAndPreview":
        await handleSaveClick();
        window.open(viewRout, "_blank");
        break;
      case "copy":
        // await handleSaveClick();
        toast.success("Link copied to clipboard");
        navigator.clipboard.writeText(viewRout);
        break;
      default:
        break;
    }
  }, [handleSaveClick, saving]);

  return (
    <div className="flex h-full items-center">
      <Button
        isDisabled={saving }
        onClick={() => handleSaveClick()}
        size="lg"
        color="primary"
        fullWidth
        className={"h-full rounded-r-none text-sm  px-unit-xl"}
        disableRipple
      >
        Save
      </Button>
      <Dropdown
        className="rounded-small"
        key={"save-dropdown"}
        aria-label="save-dropdown"
        // showArrow
        size="sm"
        offset={10}
        // shadow=""
        closeOnSelect={true}
        placement="bottom-end"
        classNames={{
          base: "",
          content: "p-0 shadow-md",
        }}
      >
        <DropdownTrigger>
          <Button
            // disabled={!props.hasEdited}
            // onClick={() => handleSaveDropdownClick("save")}
            name={"drop"}
            size="sm"
            color="primary"
            startContent={<DownArrow className=" h-3 w-3 -rotate-90" />}
            className={"h-full rounded-large rounded-l-none  !px-0 ml-[1px]"}
            disableRipple
          ></Button>
        </DropdownTrigger>
        <DropdownMenu
          disabledKeys={saving ? ["saveAndPreview", "delete"] : []}
          aria-label="save-dropdown-menu"
          onAction={(key) => handleSaveDropdownClick(key as string)}
          className="p-0  "
          classNames={{
            list: "gap-0 [&>:last-child]:rounded-t-none [&>:first-child]:rounded-b-none [&>*:not(:last-child):not(:first-child)]:rounded-none [&>*]:p-unit-lg [&>*]:px-unit-xl overflow-hidden",
            base: "overflow-hidden",
          }}
        >
          <DropdownItem key={"saveAndPreview"} className={` border-b-default-100 border-b-1 text-sm`}>
            <p className="font-semibold text-sm">Save & Preview</p>
            <p className="text-sm text-default-500">{"The preview opens in a new tab"}</p>
          </DropdownItem>
          <DropdownItem key={"copy"} className={` border-b-default-100 border-b-1 text-sm flex`}>
              {/* <img src="/icons/link.svg" alt="" className="mr-unit-xl " /> */}
              <p className="font-semibold text-sm">{"Copy link"}</p>
              <p className="text-sm text-default-500">{"Copy view link to clipboard"}</p>
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>
    </div>
  );
};

export { SaveButton };
