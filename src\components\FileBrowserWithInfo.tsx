import { FC, useState, useEffect, useCallback } from 'react';
import { File<PERSON>rowser, FileNavbar, FileToolbar, FileList, FileContextMenu } from 'chonky';
import { FileInfoSidebar } from './FileInfoSidebar';
import { CustomFileData } from '../api/api';
import { useBrowser } from '../provider/BrowserProvider';
import { useCustomFileMap } from '../hooks/fileHooks';

export const FileBrowserWithInfo: FC<any> = (props) => {
  const [selectedFile, setSelectedFile] = useState<CustomFileData | null>(null);
  const { selectedFiles, fileMap , isSideBarOpen, setIsSideBarOpen, currentView , loading  } = useBrowser();
  const { refreshFileMap , hasMore } = useCustomFileMap();


  useEffect(() => {
    if (selectedFiles.size === 1) {
      const fileId = Array.from(selectedFiles)[0];
      const file = fileMap[fileId];
      if (file) {
        setSelectedFile(file);
      }
    } else if (selectedFiles.size === 0) {
      setSelectedFile(null);
      setIsSideBarOpen(false);
    }
  }, [selectedFiles, fileMap, setIsSideBarOpen]);

  const loadNext = useCallback(async () => {
    if(!hasMore) return;
    await refreshFileMap(false , false)
  }, [hasMore, refreshFileMap]);

  return (
    <div className="flex h-full">
      <div className={`flex-1 ${isSideBarOpen ? 'md:mr-4' : ''}`}>
        <FileBrowser {...props} onFileAction={props.onFileAction} disableDragAndDropProvider>
          <FileNavbar/>
          <FileToolbar />
          {(currentView === "trash" && Boolean(!props.files?.length)) ? null : <FileList hasMore={hasMore && !loading} loadNext={loadNext}/>}
          <FileContextMenu />
        </FileBrowser>
      </div>
      {isSideBarOpen && selectedFile && (
        <div className="md:block md:w-80 w-full fixed md:relative bottom-0">
          <FileInfoSidebar 
            file={selectedFile} 
            onClose={() => setIsSideBarOpen(false)}
            updateTags={props.updateTags}
          />
        </div>
      )}
    </div>
  );
};