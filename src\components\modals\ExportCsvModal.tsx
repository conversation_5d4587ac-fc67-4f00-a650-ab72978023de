import { FC, useState } from "react";
import { Modal } from "../Modal";
import { useModal } from "../../provider/useModal";
import { Button } from "../Button";
import { Checkbox, Spinner } from "@nextui-org/react";
import { CustomFileData } from "../../api/api";
import { useUser } from "../../provider/UserProvider";
import { CsvExportOptions, defaultCsvExportOptions, generateCSV, downloadCSV } from "../../utils/csvExport";
import { is3dFile } from "../shared/util";
import toast from "react-hot-toast";
import Close from "../icons/Close";
import { useParams } from "react-router-dom";

interface ExportCsvModalProps {
  folder: CustomFileData;
  onClose: () => void;
}

const ExportCsvModal: FC<ExportCsvModalProps> = (props) => {
  const { modalState } = useModal();
  const { api, getPublicShareUrls } = useUser();
  const { basename } = useParams();
  const [options, setOptions] = useState<CsvExportOptions>(defaultCsvExportOptions);
  const [isLoading, setIsLoading] = useState(false);

  const handleOptionChange = (key: keyof CsvExportOptions, value: boolean) => {
    setOptions(prev => ({ ...prev, [key]: value }));
  };

  const handleExport = async () => {
    if (!api || !props.folder) {
      toast.error("Unable to export: API not available");
      return;
    }

    setIsLoading(true);
    const toastId = "csv-export";
    toast.loading("Gathering files...", { id: toastId });

    try {
      const { data: allFiles, error } = await api.getAllNestedChildren(props.folder.id);
      
      if (error) {
        toast.error("Failed to get folder contents", { id: toastId });
        return;
      }

      if (!allFiles || allFiles.length === 0) {
        toast.error("No files found in this folder", { id: toastId });
        return;
      }

      if(options.includeDownloadUrl){
        allFiles.forEach(file => {
          file.url = file.isDir ? "" : api.getDownloadUrl(file , false);
        });
      }

      toast.loading("Generating CSV...", { id: toastId });

      const publicShareLink = getPublicShareUrls() 

      const csvContent = generateCSV(allFiles, options, basename, publicShareLink);
      const filename = `${props.folder.name || 'folder'}_export_${new Date().toISOString().split('T')[0]}.csv`;

      downloadCSV(csvContent, filename);

      let filteredFiles = allFiles;
      if (options.only3dFiles) {
        filteredFiles = filteredFiles.filter(file => is3dFile(file));
      }
      if (options.onlyPublicFiles) {
        filteredFiles = filteredFiles.filter(file => file.meta?.pub === true);
      }

      const filteredCount = filteredFiles.length;
      let message = `Exported ${filteredCount} items to CSV`;

      if (options.only3dFiles && options.onlyPublicFiles) {
        message = `Exported ${filteredCount} public 3D files and folders to CSV`;
      } else if (options.only3dFiles) {
        message = `Exported ${filteredCount} 3D files and folders to CSV`;
      } else if (options.onlyPublicFiles) {
        message = `Exported ${filteredCount} public files and folders to CSV`;
      }

      toast.success(message, { id: toastId });
      props.onClose();
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Failed to export CSV", { id: toastId });
    } finally {
      setIsLoading(false);
    }
  };

  const selectedCount = Object.values(options).filter(Boolean).length;

  return (
    <Modal 
      isOpen={modalState.modalType === "EXPORT_CSV"} 
      onClose={props.onClose} 
      size="md" 
      backdrop="blur" 
      hideCloseButton
    >
      {() => (
        <div className="w-full bg-white p-4 flex flex-col gap-4 rounded-lg">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-medium">Export Folder to CSV</h2>
            <Button
              varient="light"
              size="sm"
              onClick={props.onClose}
              className="w-6 h-6"
              name={<Close />}
              disabled={isLoading}
            />
          </div>

          <div className="text-sm text-gray-600">
            Export all files and folders from <strong>{props.folder.name}</strong> and its subfolders to a CSV file.
          </div>

          <div>
            <h3 className="text-sm font-medium mb-3 text-gray-700">Filter options:</h3>
            <div className="space-y-3">
              <div>
                <Checkbox
                  isSelected={options.only3dFiles}
                  onValueChange={(value) => handleOptionChange('only3dFiles', value)}
                  size="sm"
                >
                  Include only 3D files and folders
                </Checkbox>
                <div className="text-xs text-gray-500 mt-1 ml-6">
                  When enabled, only 3D model files (.glb, .gltf, .obj, .fbx, .stl, .3dm) will be included
                </div>
              </div>
              <div>
                <Checkbox
                  isSelected={options.onlyPublicFiles}
                  onValueChange={(value) => handleOptionChange('onlyPublicFiles', value)}
                  size="sm"
                >
                  Include only public files
                </Checkbox>
                <div className="text-xs text-gray-500 mt-1 ml-6">
                  When enabled, only files that are publicly accessible will be included
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-sm font-medium mb-3 text-gray-700">Select properties to include:</h3>
            <div className="grid grid-cols-2 gap-2">
              <Checkbox
                isSelected={options.includeId}
                onValueChange={(value) => handleOptionChange('includeId', value)}
                size="sm"
              >
                ID
              </Checkbox>
              <Checkbox
                isSelected={options.includeName}
                onValueChange={(value) => handleOptionChange('includeName', value)}
                size="sm"
              >
                Name
              </Checkbox>
              <Checkbox
                isSelected={options.includeType}
                onValueChange={(value) => handleOptionChange('includeType', value)}
                size="sm"
              >
                Type
              </Checkbox>
              <Checkbox
                isSelected={options.includePath}
                onValueChange={(value) => handleOptionChange('includePath', value)}
                size="sm"
              >
                Path
              </Checkbox>
              <Checkbox
                isSelected={options.includeSize}
                onValueChange={(value) => handleOptionChange('includeSize', value)}
                size="sm"
              >
                Size
              </Checkbox>
              <Checkbox
                isSelected={options.includeTags}
                onValueChange={(value) => handleOptionChange('includeTags', value)}
                size="sm"
              >
                Tags
              </Checkbox>
              <Checkbox
                isSelected={options.includeModDate}
                onValueChange={(value) => handleOptionChange('includeModDate', value)}
                size="sm"
              >
                Modified Date
              </Checkbox>
              <Checkbox
                isSelected={options.includeCreatedBy}
                onValueChange={(value) => handleOptionChange('includeCreatedBy', value)}
                size="sm"
              >
                Created By
              </Checkbox>
              <Checkbox
                isSelected={options.includeNotes}
                onValueChange={(value) => handleOptionChange('includeNotes', value)}
                size="sm"
              >
                Notes
              </Checkbox>
              <Checkbox
                isSelected={options.includeDownloadUrl}
                onValueChange={(value) => handleOptionChange('includeDownloadUrl', value)}
                size="sm"
              >
                Download URL
              </Checkbox>
              <Checkbox
                isSelected={options.includeShareUrl}
                onValueChange={(value) => handleOptionChange('includeShareUrl', value)}
                size="sm"
              >
                Share URL
              </Checkbox>
              <Checkbox
                isSelected={options.includeCustomDomainShareUrl}
                onValueChange={(value) => handleOptionChange('includeCustomDomainShareUrl', value)}
                size="sm"
                isDisabled={!getPublicShareUrls() || getPublicShareUrls().length === 0}
              >
                Custom Domain Share URL
              </Checkbox>
            </div>
          </div>

          {selectedCount === 0 && (
            <div className="text-sm text-red-500">
              Please select at least one property to export.
            </div>
          )}

          <div className="flex justify-end gap-3 mt-4">
            <Button
              className="w-[71px] h-8 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-700"
              name={<span className="text-sm">Cancel</span>}
              onClick={props.onClose}
              disabled={isLoading}
            />
            <Button
              name={
                isLoading ? (
                  <div className="flex items-center gap-2">
                    <Spinner size="sm" color="white" />
                    <span className="text-sm">Exporting...</span>
                  </div>
                ) : (
                  <span className="text-sm">Export CSV</span>
                )
              }
              className="h-8 rounded-full min-w-[100px]"
              onClick={handleExport}
              color="primary"
              fullWidth={false}
              disabled={selectedCount === 0 || isLoading}
            />
          </div>
        </div>
      )}
    </Modal>
  );
};

export default ExportCsvModal;
