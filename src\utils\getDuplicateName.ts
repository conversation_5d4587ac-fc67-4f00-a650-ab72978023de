export function getDuplicateName(fileName: string): string {
    const lastDotIndex = fileName.lastIndexOf(".");
    if (lastDotIndex === -1 || lastDotIndex === 0) {
      throw new Error("Invalid filename: missing or malformed file extension");
    }
  
    const nameWithoutExt = fileName.slice(0, lastDotIndex);
    const fileExt = fileName.slice(lastDotIndex + 1);
    
    const match = nameWithoutExt.match(/^(.*?)(?:\((\d+)\))$/);
  
    if (match) {
      const baseName = match[1];
      const number = parseInt(match[2], 10) + 1;
      return `${baseName}(${number}).${fileExt}`;
    } else {
      return `${nameWithoutExt}(1).${fileExt}`;
    }
  }
  