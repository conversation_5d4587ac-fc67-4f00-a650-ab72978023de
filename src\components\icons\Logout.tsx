import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <g clipPath="url(#clip0_201_4716)">
            <mask id="mask0_201_4716" style={{maskType: "alpha",}} maskUnits="userSpaceOnUse" x="0" y="0" width="20"
                  height="20">
                <rect width="20" height="20" fill="#D9D9D9"/>
            </mask>
            <g mask="url(#mask0_201_4716)">
                <path
                    d="M4.5 17C4.0875 17 3.73437 16.8531 3.44062 16.5594C3.14687 16.2656 3 15.9125 3 15.5V4.5C3 4.0875 3.14687 3.73438 3.44062 3.44063C3.73437 3.14688 4.0875 3 4.5 3H10V4.5H4.5V15.5H10V17H4.5ZM13.5 13.5L12.4375 12.4375L14.125 10.75H8V9.25H14.125L12.4375 7.5625L13.5 6.5L17 10L13.5 13.5Z"
                    fill="#373737"/>
            </g>
        </g>
        <defs>
            <clipPath id="clip0_201_4716">
                <rect width="20" height="20" fill="white"/>
            </clipPath>
        </defs>
    </svg>
)
export default SvgComponent
