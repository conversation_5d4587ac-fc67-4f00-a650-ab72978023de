import { FC, useEffect, useRef } from "react";
import { Input } from "./Input";
import { useBrowser } from "../provider/BrowserProvider";
import SearchIcon from "../components/icons/Search.tsx"
import { InputProps } from "@nextui-org/react";

const Search: FC<InputProps> = ({size}) => {
  const ref = useRef<HTMLInputElement>(null);
  const { searchString, setSearchString } = useBrowser();


  useEffect(() => {
    ref.current!.style.display = "flex";
    ref.current!.style.order = "0";
    document.querySelector(".chonky-searchFieldContainer")?.appendChild(ref.current as Node);
  }, [ref]);

  return (
    <div ref={ref} className="h-[38px] flex-1 w-full">
      <div className="relative flex items-center md:w-auto w-full">
        <Input
          type="string"
          placeholder="Search files, folders..."
          size={size}
          value={searchString}
          onChange={setSearchString}
          startContent={<div className="hidden md:block"><SearchIcon /></div>}
          className="h-[38px] w-full md:w-[283px] bg-white border-2 border-default-200 hover:border-default-300 rounded-full"
          autoComplete="one-time-code"
        />
      </div>
    </div>
  );
};

export { Search };
