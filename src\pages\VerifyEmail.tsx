import { useCallback, useEffect, useState } from "react";
import { useUser } from "../provider/UserProvider.ts";
import toast from "react-hot-toast";
import { useParams } from "react-router-dom";
import { Logo } from "../components/Logo.tsx";
import { Header } from "../components/Header.tsx";
import { Button } from "@nextui-org/react";
import { useBaseNavigate } from "../hooks/UseBaseNavigate.tsx";

const setSignupCache = (data: any) => {
  localStorage.setItem("siginupCache", JSON.stringify(data));
};

const getSignupCache = () =>
  localStorage.getItem("siginupCache") && JSON.parse(localStorage.getItem("siginupCache")!);


// Validation function that returns error message or undefined
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function validateField(type: string, value: string, compareValue?: string): string | undefined {
  switch (type) {
    case "password":
      if (!value.trim()) return "Password is required";
      if (value.length < 8) return "Password must be at least 8 characters long";
      break;
    case "passwordConfirm":
      if (!value.trim()) return "Confirm password is required";
      if (value !== compareValue) return "Passwords do not match";
      break;
    default:
      break;
  }
  return undefined;
}

function VerifyEmail() {
  const { setIsLogin, api, setUser } = useUser();
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | undefined>(undefined);
  const navigate = useBaseNavigate();
  const { token : paramsToken } = useParams();
  const [token, setToken] = useState<string | null>(paramsToken || null);
  const { config , user } = useUser();
  const [emailSentDate, setEmailSentDate] = useState<Date | null>(null);

  //2 minuts
  const resendEmailTimeout = 2 * 60 * 1000;
  const [remainingTime, setRemainingTime] = useState<number | null>(null);

  useEffect(() => {
    if(token) return;
    setToken(paramsToken || null);
    const cache = getSignupCache();
    if(cache && cache.email){
      setEmailSentDate(new Date(cache.date));
    }
  }, []);
  
  useEffect(() => {
    if(!emailSentDate) return;
    const interval = setInterval(() => {
      const diff = new Date().getTime() - emailSentDate.getTime();
      if(diff > resendEmailTimeout){
        clearInterval(interval);
        setRemainingTime(null);
      }else{
        setRemainingTime(resendEmailTimeout - diff);
      }
    }, 1000);
    return () => clearInterval(interval);
  }, [emailSentDate]);

  const handleVerifyUser = useCallback(async () => {
    if (!api) {
      toast.error("API not initialized");
      return;
    }

    if(token){
      //hide token from url
      navigate(".." , {relative : "path"});
    }

    setLoading(true);
    const currentToken = token || api.getToken();
    if (!currentToken) {
      setError("Token not found");
      setLoading(false);
      return;
    }

    const { user, error } = await api.confirmEmailVerification({token : currentToken});

    setLoading(false);


    if (error && error !== "Email already verified") { // if it returns email already verified, then go to setup user 
      if(error === "Invalid token (mismatch)"){
        setError("Invalid token. Please check your email for the correct link, or request a new one.");
        return;
      }
      setError("Error verifying email. " + error);
      return;
    }

    toast.success("Email verified successfully");

    //user setup will happen after we login the user.    
    setIsLogin(true);
    setUser(user);
    navigate("/folders");

  }, [api, navigate, setIsLogin, setUser, token]);

  useEffect(() => {
    if(!config || !token) return;
    handleVerifyUser()
  // eslint-disable-next-line react-hooks/exhaustive-deps
  } , [config, token])

  const handleResendEmail = useCallback(async () => {
    if (!api) {
      toast.error("API not initialized");
      return;
    }
    if(!user){
      toast.error("User not found");
      return;
    }
    //check remaining time
    if(remainingTime){
      toast.error("Please wait before resending email");
      return;
    }
    setLoading(true);
    const { error } = await api.requestEmailVerification();
    setLoading(false);
    if (error) {
      if(error === "Verification email already sent"){
        toast.error("Verification email already sent");
        setEmailSentDate(new Date());
        setRemainingTime(resendEmailTimeout);
        return;
      }
      setError("Error resending email verification");
      return;
    }
    setSignupCache({ email: user.email , date : new Date()});
    setEmailSentDate(new Date());
    setRemainingTime(resendEmailTimeout);
    toast.success("Email verification sent");
  }, [api, remainingTime, resendEmailTimeout, user]);

  useEffect(() => {
    if (!token && !user && !api?.user) {
      navigate("/login");
    }
  }, [api?.user, navigate, remainingTime, token, user])
  
  return (
    <div className="w-screen h-screen flex flex-col justify-center items-center fixed t-0 l-0 z-10 bg-white">
      <Header />
      <div className="flex flex-col items-center justify-center flex-1">
        <Logo className="h-12 w-12" />
        {user && !user?.verified  && !error ? (
          <div className="flex flex-col items-center justify-center">
            <p className="text-lg font-semibold mt-4">You are not verified, please check your email</p>
            <Button
              onClick={handleResendEmail}
              className="mt-4 p-unit-xl font-semibold"
              color="primary"
              isDisabled={remainingTime !== null}
              isLoading={remainingTime !== null}
              size="lg"
              >
                {remainingTime ? `Resend email in ${Math.ceil(remainingTime / 1000)}s` : "Resend email"}
            </Button>
          </div>
        ) : loading ? (
          <p className="text-lg font-semibold mt-4">Verifying your email...</p>
        ) : error ? (
          <p className="text-lg font-semibold mt-4 max-w-[300px]">{error}</p>
        ) : user?.verified ? (
          <p className="text-lg font-semibold mt-4">Email verified successfully</p>
        ) : null}
      </div>
    </div>
  );
}

export default VerifyEmail;
