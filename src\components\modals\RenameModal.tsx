import { FC, useState, useEffect } from "react";
import { Modal } from "../Modal";
import { useModal } from "../../provider/useModal";
import { Button } from "../Button.tsx";
import { Input } from "@nextui-org/react";
import { useCustomFileMap } from '../../hooks/fileHooks';
import { CustomFileData } from "../../api/api.ts";
import toast from "react-hot-toast";

interface RenameModalProps {
    file: CustomFileData;
    onClose: () => void;
}

const getNameWithoutExtension = (fileName: string) => {
    const parts = fileName.split('.');
    if (parts.length > 1) {
        parts.pop();
        return parts.join('.');
    }
    return fileName;
};

const RenameModal: FC<RenameModalProps> = (props) => {
    const { modalState } = useModal();
    const { renameFile } = useCustomFileMap();
    const [newName, setNewName] = useState("");

    useEffect(() => {
        if (props.file) {
            setNewName(getNameWithoutExtension(props.file.name));
        }
    }, [props.file]);

    return (
        <Modal isOpen={modalState.modalType == "RENAME"} onClose={props.onClose} size="md" backdrop="blur">
            {() => (
                <div className="w-full bg-white p-3 flex flex-col gap-3">
                    <div>
                        <h2 className="font-medium size-20px mb-2">Rename</h2>
                        <Input
                            className="h-[38px] text-sm"
                            value={newName}
                            onChange={(e) => setNewName(e.target.value)}
                            placeholder="Enter a new name without extension"
                            variant="bordered"
                            size="lg"
                        />
                    </div>

                    <div className="flex justify-end gap-3 h-8">
                        <Button
                            className="w-[71px] h-8 rounded-3xl"
                            name={
                                <span className="text-sm">Cancel</span>
                            }
                            onClick={props.onClose}
                        />
                        <Button
                            name={
                                <span className="text-sm">Rename</span>
                            }
                            className="w-[71px] h-8 rounded-3xl"
                            onClick={() => {
                                if (newName === null) return;
                                if (newName) {
                                    renameFile(props.file, newName);
                                } else {
                                    toast.error("Name Can't be empty");
                                }
                                props.onClose();
                            }}
                            color="primary"
                        />
                    </div>
                </div>
            )}
        </Modal>
    );
};

export default RenameModal;