import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 529.33 321.8" {...props} fill="currentColor">
    <g data-name="Layer 2">
      <g data-name="Layer 1">
        <path d="M450 319.8a32.82 32.82 0 0 1-8.89-1.41l-133.1-37.54-81.37-23c-13.84-3.91-18.45-12.21-14.53-26.14q30.24-107.23 60.49-214.47c3-10.7 8.74-15.91 17.46-15.91a32.08 32.08 0 0 1 8.64 1.36Q406 33 513.21 63.23c14.2 4 18.74 12.16 14.72 26.42q-30.16 107-60.36 214c-1.96 6.92-5.9 16.15-17.57 16.15Zm-13-39.56L489.67 93.7 303.12 41.08 250.5 227.62Z" />
        <path d="M225.68 261.41q-11.73-3.32-16-10.91l-.24-.09-108.94 39.26q-34-94.34-68.2-189.21l189.22-68.2 24.19 67.09 13.26-47q-7.17-19.86-14.32-39.71C240.29.51 234-2.45 222 1.89Q117.17 39.64 12.35 77.45C.65 81.67-2.4 88.16 1.79 99.79q37.76 104.82 75.56 209.64c4.21 11.68 10.76 14.78 22.34 10.61l104.58-37.69 42-15.14Z" />
      </g>
    </g>
  </svg>
)
export default SvgComponent
