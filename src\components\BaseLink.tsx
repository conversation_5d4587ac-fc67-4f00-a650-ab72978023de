import { Link, LinkProps, useParams, parsePath } from "react-router-dom";
import {useUser} from "../provider/UserProvider";
import { FileAPI } from "../api/api";


function BaseLink({ to, ...restProps }: LinkProps) {
  const {api} = useUser() || FileAPI.getInstance()
  const { basename: routeBasename } = useParams();



  const toObj =
    typeof to === "string"
      ? parsePath(to) // parsePath from 'react-router-dom'
      : to;

  if (!toObj?.pathname) {
    return <Link to={to} {...restProps} />;
  }

  let { pathname} = toObj;
  const {  search, hash } = toObj;

  //  If `pathname` is relative (doesn't start with "/"), do not alter it.
  if (!pathname.startsWith("/") || !api) {
    return <Link to={{ pathname, search, hash }} {...restProps} />;
  }

  // If it's an absolute path, prepend the dynamicBase (if we have one).
  //    e.g. `/signup` => `/drive/signup` or `/dev-1/signup`
  const dynamicBase = routeBasename || "drive";
  if (dynamicBase) {
    pathname = `/${dynamicBase}${pathname}`;
  }

  return <Link to={{ pathname, search, hash }} {...restProps} />;
}

export default BaseLink;
