import { FC, useState } from "react";
import { Modal } from "../Modal";
import { useModal } from "../../provider/useModal";
import { Button } from "../Button.tsx";
import { useCustomFileMap } from '../../hooks/fileHooks';
import { CustomFileData } from "../../api/api.ts";
import { Checkbox } from "@nextui-org/react";

interface DeleteModalProps {
    file: CustomFileData[];
    onClose: () => void;
    permanent?: boolean;
}

const DeleteModal: FC<DeleteModalProps> = (props) => {
    const { modalState } = useModal();
    const [permanentDelete, setPermanentDelete] = useState(props.permanent ?? false);
    const { deleteFiles } = useCustomFileMap();
    return (
        <Modal isOpen={modalState.modalType == "DELETE"} onClose={props.onClose} size="md" backdrop="blur">
            {() => (
                <div className="w-full bg-white p-3 flex flex-col gap-3">
                    <div>
                        <h2 className="font-medium size-20px mb-2">Delete file?</h2>
                    </div>
                    <h3>Are you sure you want to delete selected file or folder?</h3>
                    <div>
                        <Checkbox isSelected={permanentDelete} onValueChange={()=>{ setPermanentDelete(!permanentDelete) }}> <span className="text-sm text-red-500">Permanantely delete</span></Checkbox>
                        {permanentDelete && <p className="text-sm text-gray-500">This action cannot be undone.</p>}
                    </div>
                    <div className="flex justify-end gap-3 h-8">
                        <Button
                            className="w-[71px] h-8 rounded-3xl"
                            name={
                                <span className="text-sm">Cancel</span>
                            }
                            onClick={props.onClose}
                        >
                        </Button>
                        <Button
                            name={
                                <span className="text-sm">Delete</span>
                            }
                            className="w-[71px] h-8 rounded-3xl"
                            onClick={() => {
                                deleteFiles(props.file, permanentDelete)
                                props.onClose();
                            }}
                            color="primary"
                        >
                        </Button>
                    </div>
                </div>
            )}
        </Modal>
    )
}

export default DeleteModal