import { SVGProps } from "react"
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <mask id="mask0_124_3960" style={{maskType: "alpha",}} maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
            <rect width="20" height="20" fill="#D9D9D9"/>
        </mask>
        <g mask="url(#mask0_124_3960)">
            <path d="M7.95811 14.7114L3.5127 10.266L4.40353 9.37499L7.95811 12.9296L15.596 5.29166L16.4869 6.1827L7.95811 14.7114Z" fill="#62C655"/>
        </g>
    </svg>
)
export default SvgComponent